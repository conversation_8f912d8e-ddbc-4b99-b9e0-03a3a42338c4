import { ChatStore, createEmptySession } from '../stores/ChatStore';
import { createMessage } from './message';

describe('会话隔离测试', () => {
  let chatStore: ChatStore;

  beforeEach(() => {
    chatStore = new ChatStore();
    // 清除localStorage以确保测试环境干净
    localStorage.clear();
  });

  afterEach(() => {
    chatStore.clearAllData();
  });

  test('应该能够创建多个独立的会话', () => {
    // 创建第一个会话
    const session1 = chatStore.newSession();
    chatStore.onUserInput('第一个会话的消息');
    
    // 创建第二个会话
    const session2 = chatStore.newSession();
    chatStore.onUserInput('第二个会话的消息');

    // 验证会话是独立的
    expect(chatStore.sessions).toHaveLength(2);
    expect(chatStore.sessions[0].messages[0].content).toBe('第一个会话的消息');
    expect(chatStore.sessions[1].messages[0].content).toBe('第二个会话的消息');
  });

  test('应该防止并发操作导致的消息串话', async () => {
    // 创建两个会话
    const session1 = chatStore.newSession();
    chatStore.onUserInput('会话1消息');
    const session1Id = chatStore.currentSession.id;

    const session2 = chatStore.newSession();
    chatStore.onUserInput('会话2消息');
    const session2Id = chatStore.currentSession.id;

    // 模拟并发操作
    const operation1 = chatStore.withSessionLock(session1Id, async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
      return '会话1结果';
    });

    const operation2 = chatStore.withSessionLock(session1Id, async () => {
      await new Promise(resolve => setTimeout(resolve, 50));
      return '会话1冲突结果';
    });

    const results = await Promise.all([operation1, operation2]);
    
    // 第二个操作应该返回null，因为会话被锁定
    expect(results[0]).toBe('会话1结果');
    expect(results[1]).toBeNull();
  });

  test('应该正确处理会话切换时的安全检查', () => {
    // 创建多个会话
    chatStore.newSession();
    chatStore.onUserInput('会话1');
    
    chatStore.newSession();
    chatStore.onUserInput('会话2');

    const session2Id = chatStore.currentSession.id;

    // 模拟会话有正在进行的操作
    chatStore['incrementPendingOperation'](session2Id);

    // 尝试切换到有待处理操作的会话应该失败
    const success = chatStore.selectSession(0);
    expect(success).toBe(true); // 切换到没有待处理操作的会话应该成功

    // 尝试切换回有待处理操作的会话应该失败
    const failedSwitch = chatStore.selectSession(1);
    expect(failedSwitch).toBe(false);
  });

  test('应该使用sessionId而不是index进行消息更新', () => {
    // 创建会话并添加消息
    chatStore.newSession();
    chatStore.onUserInput('原始消息');
    const sessionId = chatStore.currentSession.id;
    const messageIndex = 0;

    // 使用sessionId更新消息
    const success = chatStore.updateMessageBySessionId(
      sessionId,
      messageIndex,
      (message) => {
        if (message) {
          message.content = '更新后的消息';
        }
      }
    );

    expect(success).toBe(true);
    expect(chatStore.currentSession.messages[0].content).toBe('更新后的消息');

    // 尝试使用不存在的sessionId应该失败
    const failedUpdate = chatStore.updateMessageBySessionId(
      'non-existent-id',
      messageIndex,
      (message) => {
        if (message) {
          message.content = '不应该更新';
        }
      }
    );

    expect(failedUpdate).toBe(false);
    expect(chatStore.currentSession.messages[0].content).toBe('更新后的消息');
  });

  test('应该正确清理会话锁定状态', async () => {
    chatStore.newSession();
    chatStore.onUserInput('测试消息');
    const sessionId = chatStore.currentSession.id;

    // 执行一个会抛出错误的操作
    try {
      await chatStore.withSessionLock(sessionId, async () => {
        throw new Error('测试错误');
      });
    } catch (error) {
      // 预期的错误
    }

    // 验证锁定状态已被清理
    expect(chatStore.hasPendingOperations(sessionId)).toBe(false);
    
    // 应该能够再次锁定该会话
    const result = await chatStore.withSessionLock(sessionId, async () => {
      return '成功';
    });

    expect(result).toBe('成功');
  });
});
