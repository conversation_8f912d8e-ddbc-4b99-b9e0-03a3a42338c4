import { ChatStore, createEmptySession } from '../stores/ChatStore';
import { createMessage } from './message';

describe('会话隔离测试', () => {
  let chatStore: ChatStore;

  beforeEach(() => {
    chatStore = new ChatStore();
    // 清除localStorage以确保测试环境干净
    localStorage.clear();
  });

  afterEach(() => {
    chatStore.clearAllData();
  });

  test('应该能够创建多个独立的会话', () => {
    // 创建第一个会话
    const session1 = chatStore.newSession();
    const session1Id = session1.id;
    chatStore.onUserInputBySessionId(session1Id, '第一个会话的消息');

    // 创建第二个会话
    const session2 = chatStore.newSession();
    const session2Id = session2.id;
    chatStore.onUserInputBySessionId(session2Id, '第二个会话的消息');

    // 验证会话是独立的
    expect(chatStore.sessions).toHaveLength(2);

    const actualSession1 = chatStore.sessions.find(s => s.id === session1Id);
    const actualSession2 = chatStore.sessions.find(s => s.id === session2Id);

    expect(actualSession1?.messages[0].content).toBe('第一个会话的消息');
    expect(actualSession2?.messages[0].content).toBe('第二个会话的消息');
  });

  test('应该防止并发操作导致的消息串话', async () => {
    // 创建两个会话
    const session1 = chatStore.newSession();
    chatStore.onUserInput('会话1消息');
    const session1Id = chatStore.currentSession.id;

    const session2 = chatStore.newSession();
    chatStore.onUserInput('会话2消息');
    const session2Id = chatStore.currentSession.id;

    // 模拟并发操作
    const operation1 = chatStore.withSessionLock(session1Id, async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
      return '会话1结果';
    });

    const operation2 = chatStore.withSessionLock(session1Id, async () => {
      await new Promise(resolve => setTimeout(resolve, 50));
      return '会话1冲突结果';
    });

    const results = await Promise.all([operation1, operation2]);
    
    // 第二个操作应该返回null，因为会话被锁定
    expect(results[0]).toBe('会话1结果');
    expect(results[1]).toBeNull();
  });

  test('应该正确处理会话切换时的安全检查', () => {
    // 创建多个会话
    chatStore.newSession();
    chatStore.onUserInput('会话1');
    
    chatStore.newSession();
    chatStore.onUserInput('会话2');

    const session2Id = chatStore.currentSession.id;

    // 模拟会话有正在进行的操作
    chatStore['incrementPendingOperation'](session2Id);

    // 尝试切换到有待处理操作的会话应该失败
    const success = chatStore.selectSession(0);
    expect(success).toBe(true); // 切换到没有待处理操作的会话应该成功

    // 尝试切换回有待处理操作的会话应该失败
    const failedSwitch = chatStore.selectSession(1);
    expect(failedSwitch).toBe(false);
  });

  test('应该使用sessionId而不是index进行消息更新', () => {
    // 创建会话并添加消息
    chatStore.newSession();
    chatStore.onUserInput('原始消息');
    const sessionId = chatStore.currentSession.id;
    const messageIndex = 0;

    // 使用sessionId更新消息
    const success = chatStore.updateMessageBySessionId(
      sessionId,
      messageIndex,
      (message) => {
        if (message) {
          message.content = '更新后的消息';
        }
      }
    );

    expect(success).toBe(true);
    expect(chatStore.currentSession.messages[0].content).toBe('更新后的消息');

    // 尝试使用不存在的sessionId应该失败
    const failedUpdate = chatStore.updateMessageBySessionId(
      'non-existent-id',
      messageIndex,
      (message) => {
        if (message) {
          message.content = '不应该更新';
        }
      }
    );

    expect(failedUpdate).toBe(false);
    expect(chatStore.currentSession.messages[0].content).toBe('更新后的消息');
  });

  test('应该正确清理会话锁定状态', async () => {
    chatStore.newSession();
    chatStore.onUserInput('测试消息');
    const sessionId = chatStore.currentSession.id;

    // 执行一个会抛出错误的操作
    try {
      await chatStore.withSessionLock(sessionId, async () => {
        throw new Error('测试错误');
      });
    } catch (error) {
      // 预期的错误
    }

    // 验证锁定状态已被清理
    expect(chatStore.hasPendingOperations(sessionId)).toBe(false);
    
    // 应该能够再次锁定该会话
    const result = await chatStore.withSessionLock(sessionId, async () => {
      return '成功';
    });

    expect(result).toBe('成功');
  });

  test('应该防止消息串话问题', () => {
    // 创建4个会话，模拟用户描述的场景
    const sessions = [];
    for (let i = 1; i <= 4; i++) {
      const session = chatStore.newSession();
      chatStore.onUserInputBySessionId(session.id, `问题${i}`);
      sessions.push(session);
    }

    // 验证每个会话都有正确的消息
    sessions.forEach((session, index) => {
      const actualSession = chatStore.sessions.find(s => s.id === session.id);
      expect(actualSession?.messages[0].content).toBe(`问题${index + 1}`);
    });

    // 模拟后续消息添加到错误会话的情况
    // 这应该不会发生，因为我们使用了基于sessionId的方法
    const session3Id = sessions[2].id;
    chatStore.onBotMessageBySessionId(session3Id, '回答3');

    // 验证消息被添加到正确的会话
    const session3 = chatStore.sessions.find(s => s.id === session3Id);
    expect(session3?.messages).toHaveLength(2);
    expect(session3?.messages[0].content).toBe('问题3');
    expect(session3?.messages[1].content).toBe('回答3');

    // 验证其他会话没有被影响
    sessions.forEach((session, index) => {
      if (index !== 2) { // 跳过第3个会话
        const actualSession = chatStore.sessions.find(s => s.id === session.id);
        expect(actualSession?.messages).toHaveLength(1);
        expect(actualSession?.messages[0].content).toBe(`问题${index + 1}`);
      }
    });
  });
});
