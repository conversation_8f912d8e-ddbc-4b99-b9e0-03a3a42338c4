import React, { useState } from 'react';
import { Button, Input, Card, Space, Typography, message } from 'antd';
import { observer } from 'mobx-react-lite';
import { useChatStoreOnly } from '../stores';

const { Title, Text } = Typography;

export const SessionIsolationTest: React.FC = observer(() => {
  const chatStore = useChatStoreOnly();
  const [testInput, setTestInput] = useState('');

  const createTestSessions = () => {
    // 清除现有会话
    chatStore.clearAllData();
    
    // 创建4个测试会话
    const sessions = [];
    for (let i = 1; i <= 4; i++) {
      const session = chatStore.newSession();
      // 直接添加到sessions数组，模拟用户输入
      chatStore.sessions.unshift(session);
      chatStore.currentSessionIndex = 0;
      chatStore.tempSession = null;
      
      // 使用安全方法添加用户输入
      const success = chatStore.onUserInputBySessionId(session.id, `问题${i}`, undefined, true);
      console.log(`会话${i}创建结果:`, success);
      sessions.push(session);
    }
    
    message.success('创建了4个测试会话');
    chatStore.debugSessionState();
  };

  const testMessageIsolation = () => {
    if (chatStore.sessions.length < 4) {
      message.error('请先创建测试会话');
      return;
    }

    // 向第3个会话添加消息
    const session3 = chatStore.sessions[2];
    if (session3) {
      const success = chatStore.onUserInputBySessionId(session3.id, '额外的问题3', undefined, true);
      console.log('向会话3添加消息结果:', success);
      
      if (success) {
        message.success('向会话3添加了额外消息');
        chatStore.debugSessionState();
      } else {
        message.error('添加消息失败');
      }
    }
  };

  const addCustomMessage = () => {
    if (!testInput.trim()) {
      message.error('请输入测试消息');
      return;
    }

    if (chatStore.sessions.length === 0) {
      message.error('请先创建测试会话');
      return;
    }

    // 向第一个会话添加自定义消息
    const firstSession = chatStore.sessions[0];
    const success = chatStore.onUserInputBySessionId(firstSession.id, testInput, undefined, true);
    
    if (success) {
      message.success('添加自定义消息成功');
      setTestInput('');
      chatStore.debugSessionState();
    } else {
      message.error('添加自定义消息失败');
    }
  };

  const clearAllSessions = () => {
    chatStore.clearAllData();
    message.success('已清除所有会话');
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>会话隔离测试</Title>
      
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        <Card title="测试操作">
          <Space wrap>
            <Button type="primary" onClick={createTestSessions}>
              创建4个测试会话
            </Button>
            <Button onClick={testMessageIsolation}>
              测试消息隔离
            </Button>
            <Button danger onClick={clearAllSessions}>
              清除所有会话
            </Button>
          </Space>
        </Card>

        <Card title="自定义消息测试">
          <Space.Compact style={{ width: '100%' }}>
            <Input
              placeholder="输入测试消息"
              value={testInput}
              onChange={(e) => setTestInput(e.target.value)}
              onPressEnter={addCustomMessage}
            />
            <Button type="primary" onClick={addCustomMessage}>
              添加到第一个会话
            </Button>
          </Space.Compact>
        </Card>

        <Card title="会话状态">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text strong>当前会话数: {chatStore.sessions.length}</Text>
            <Text strong>当前会话索引: {chatStore.currentSessionIndex}</Text>
            <Text strong>临时会话: {chatStore.tempSession ? '存在' : '不存在'}</Text>
            
            {chatStore.sessions.map((session, index) => (
              <Card 
                key={session.id} 
                size="small" 
                title={`会话 ${index + 1} - ${session.topic}`}
                style={{ 
                  backgroundColor: index === chatStore.currentSessionIndex ? '#f6ffed' : '#fafafa' 
                }}
              >
                <Text>ID: {session.id}</Text><br />
                <Text>消息数: {session.messages.length}</Text><br />
                {session.messages.map((msg, msgIndex) => (
                  <div key={msgIndex} style={{ marginLeft: '10px', marginTop: '5px' }}>
                    <Text type="secondary">
                      [{msg.role}] {typeof msg.content === 'string' ? msg.content : '[多媒体内容]'}
                    </Text>
                  </div>
                ))}
              </Card>
            ))}
          </Space>
        </Card>
      </Space>
    </div>
  );
});

export default SessionIsolationTest;
