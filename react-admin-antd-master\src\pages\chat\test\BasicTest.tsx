import React from 'react';
import { Button, message } from 'antd';
import { useChatStoreOnly } from '../stores';

export const BasicTest: React.FC = () => {
  const chatStore = useChatStoreOnly();

  const runBasicTest = () => {
    try {
      console.log('开始基本测试...');
      
      // 清除现有数据
      chatStore.clearAllData();
      console.log('清除数据完成');
      
      // 创建第一个会话
      const session1 = chatStore.newSession();
      console.log('创建会话1:', session1.id);
      
      // 将临时会话转为正式会话
      chatStore.sessions.unshift(session1);
      chatStore.currentSessionIndex = 0;
      chatStore.tempSession = null;
      
      // 测试直接调用 onUserInput
      console.log('测试直接调用 onUserInput...');
      chatStore.onUserInput('测试消息1', undefined, session1);
      console.log('会话1消息数:', session1.messages.length);
      
      // 创建第二个会话
      const session2 = chatStore.newSession();
      console.log('创建会话2:', session2.id);
      
      chatStore.sessions.unshift(session2);
      chatStore.currentSessionIndex = 0;
      chatStore.tempSession = null;
      
      // 测试 onUserInputBySessionId
      console.log('测试 onUserInputBySessionId...');
      const success = chatStore.onUserInputBySessionId(session2.id, '测试消息2', undefined, true);
      console.log('onUserInputBySessionId 结果:', success);
      console.log('会话2消息数:', session2.messages.length);
      
      // 打印最终状态
      chatStore.debugSessionState();
      
      message.success('基本测试完成，请查看控制台');
      
    } catch (error) {
      console.error('测试失败:', error);
      message.error('测试失败: ' + error.message);
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>基本功能测试</h2>
      <Button type="primary" onClick={runBasicTest}>
        运行基本测试
      </Button>
    </div>
  );
};

export default BasicTest;
