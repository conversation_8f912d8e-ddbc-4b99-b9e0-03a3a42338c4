import {
  toText
} from "./chunk-MAHHFZMB.js";
import {
  katex
} from "./chunk-QXAFKEYB.js";
import {
  find,
  html,
  normalize,
  parse,
  parse2,
  svg
} from "./chunk-SPXOOSQY.js";
import {
  SKIP,
  visitParents
} from "./chunk-H7EG4SLY.js";
import "./chunk-OL46QLBJ.js";

// node_modules/.pnpm/hast-util-parse-selector@4.0.0/node_modules/hast-util-parse-selector/lib/index.js
var search = /[#.]/g;
function parseSelector(selector, defaultTagName) {
  const value = selector || "";
  const props = {};
  let start = 0;
  let previous;
  let tagName;
  while (start < value.length) {
    search.lastIndex = start;
    const match = search.exec(value);
    const subvalue = value.slice(start, match ? match.index : value.length);
    if (subvalue) {
      if (!previous) {
        tagName = subvalue;
      } else if (previous === "#") {
        props.id = subvalue;
      } else if (Array.isArray(props.className)) {
        props.className.push(subvalue);
      } else {
        props.className = [subvalue];
      }
      start += subvalue.length;
    }
    if (match) {
      previous = match[0];
      start++;
    }
  }
  return {
    type: "element",
    // @ts-expect-error: tag name is parsed.
    tagName: tagName || defaultTagName || "div",
    properties: props,
    children: []
  };
}

// node_modules/.pnpm/hastscript@9.0.1/node_modules/hastscript/lib/create-h.js
function createH(schema, defaultTagName, caseSensitive) {
  const adjust = caseSensitive ? createAdjustMap(caseSensitive) : void 0;
  function h2(selector, properties, ...children) {
    let node;
    if (selector === null || selector === void 0) {
      node = { type: "root", children: [] };
      const child = (
        /** @type {Child} */
        properties
      );
      children.unshift(child);
    } else {
      node = parseSelector(selector, defaultTagName);
      const lower = node.tagName.toLowerCase();
      const adjusted = adjust ? adjust.get(lower) : void 0;
      node.tagName = adjusted || lower;
      if (isChild(properties)) {
        children.unshift(properties);
      } else {
        for (const [key, value] of Object.entries(properties)) {
          addProperty(schema, node.properties, key, value);
        }
      }
    }
    for (const child of children) {
      addChild(node.children, child);
    }
    if (node.type === "element" && node.tagName === "template") {
      node.content = { type: "root", children: node.children };
      node.children = [];
    }
    return node;
  }
  return h2;
}
function isChild(value) {
  if (value === null || typeof value !== "object" || Array.isArray(value)) {
    return true;
  }
  if (typeof value.type !== "string") return false;
  const record = (
    /** @type {Record<string, unknown>} */
    value
  );
  const keys = Object.keys(value);
  for (const key of keys) {
    const value2 = record[key];
    if (value2 && typeof value2 === "object") {
      if (!Array.isArray(value2)) return true;
      const list = (
        /** @type {ReadonlyArray<unknown>} */
        value2
      );
      for (const item of list) {
        if (typeof item !== "number" && typeof item !== "string") {
          return true;
        }
      }
    }
  }
  if ("children" in value && Array.isArray(value.children)) {
    return true;
  }
  return false;
}
function addProperty(schema, properties, key, value) {
  const info = find(schema, key);
  let result;
  if (value === null || value === void 0) return;
  if (typeof value === "number") {
    if (Number.isNaN(value)) return;
    result = value;
  } else if (typeof value === "boolean") {
    result = value;
  } else if (typeof value === "string") {
    if (info.spaceSeparated) {
      result = parse2(value);
    } else if (info.commaSeparated) {
      result = parse(value);
    } else if (info.commaOrSpaceSeparated) {
      result = parse2(parse(value).join(" "));
    } else {
      result = parsePrimitive(info, info.property, value);
    }
  } else if (Array.isArray(value)) {
    result = [...value];
  } else {
    result = info.property === "style" ? style(value) : String(value);
  }
  if (Array.isArray(result)) {
    const finalResult = [];
    for (const item of result) {
      finalResult.push(
        /** @type {number | string} */
        parsePrimitive(info, info.property, item)
      );
    }
    result = finalResult;
  }
  if (info.property === "className" && Array.isArray(properties.className)) {
    result = properties.className.concat(
      /** @type {Array<number | string> | number | string} */
      result
    );
  }
  properties[info.property] = result;
}
function addChild(nodes, value) {
  if (value === null || value === void 0) {
  } else if (typeof value === "number" || typeof value === "string") {
    nodes.push({ type: "text", value: String(value) });
  } else if (Array.isArray(value)) {
    for (const child of value) {
      addChild(nodes, child);
    }
  } else if (typeof value === "object" && "type" in value) {
    if (value.type === "root") {
      addChild(nodes, value.children);
    } else {
      nodes.push(value);
    }
  } else {
    throw new Error("Expected node, nodes, or string, got `" + value + "`");
  }
}
function parsePrimitive(info, name, value) {
  if (typeof value === "string") {
    if (info.number && value && !Number.isNaN(Number(value))) {
      return Number(value);
    }
    if ((info.boolean || info.overloadedBoolean) && (value === "" || normalize(value) === normalize(name))) {
      return true;
    }
  }
  return value;
}
function style(styles) {
  const result = [];
  for (const [key, value] of Object.entries(styles)) {
    result.push([key, value].join(": "));
  }
  return result.join("; ");
}
function createAdjustMap(values) {
  const result = /* @__PURE__ */ new Map();
  for (const value of values) {
    result.set(value.toLowerCase(), value);
  }
  return result;
}

// node_modules/.pnpm/hastscript@9.0.1/node_modules/hastscript/lib/svg-case-sensitive-tag-names.js
var svgCaseSensitiveTagNames = [
  "altGlyph",
  "altGlyphDef",
  "altGlyphItem",
  "animateColor",
  "animateMotion",
  "animateTransform",
  "clipPath",
  "feBlend",
  "feColorMatrix",
  "feComponentTransfer",
  "feComposite",
  "feConvolveMatrix",
  "feDiffuseLighting",
  "feDisplacementMap",
  "feDistantLight",
  "feDropShadow",
  "feFlood",
  "feFuncA",
  "feFuncB",
  "feFuncG",
  "feFuncR",
  "feGaussianBlur",
  "feImage",
  "feMerge",
  "feMergeNode",
  "feMorphology",
  "feOffset",
  "fePointLight",
  "feSpecularLighting",
  "feSpotLight",
  "feTile",
  "feTurbulence",
  "foreignObject",
  "glyphRef",
  "linearGradient",
  "radialGradient",
  "solidColor",
  "textArea",
  "textPath"
];

// node_modules/.pnpm/hastscript@9.0.1/node_modules/hastscript/lib/index.js
var h = createH(html, "div");
var s = createH(svg, "g", svgCaseSensitiveTagNames);

// node_modules/.pnpm/web-namespaces@2.0.1/node_modules/web-namespaces/index.js
var webNamespaces = {
  html: "http://www.w3.org/1999/xhtml",
  mathml: "http://www.w3.org/1998/Math/MathML",
  svg: "http://www.w3.org/2000/svg",
  xlink: "http://www.w3.org/1999/xlink",
  xml: "http://www.w3.org/XML/1998/namespace",
  xmlns: "http://www.w3.org/2000/xmlns/"
};

// node_modules/.pnpm/hast-util-from-dom@5.0.1/node_modules/hast-util-from-dom/lib/index.js
function fromDom(tree, options) {
  return transform(tree, options || {}) || { type: "root", children: [] };
}
function transform(node, options) {
  const transformed = one(node, options);
  if (transformed && options.afterTransform)
    options.afterTransform(node, transformed);
  return transformed;
}
function one(node, options) {
  switch (node.nodeType) {
    case 1: {
      const domNode = (
        /** @type {Element} */
        node
      );
      return element(domNode, options);
    }
    case 3: {
      const domNode = (
        /** @type {Text} */
        node
      );
      return text(domNode);
    }
    case 8: {
      const domNode = (
        /** @type {Comment} */
        node
      );
      return comment(domNode);
    }
    case 9: {
      const domNode = (
        /** @type {Document} */
        node
      );
      return root(domNode, options);
    }
    case 10: {
      return doctype();
    }
    case 11: {
      const domNode = (
        /** @type {DocumentFragment} */
        node
      );
      return root(domNode, options);
    }
    default: {
      return void 0;
    }
  }
}
function root(node, options) {
  return { type: "root", children: all(node, options) };
}
function doctype() {
  return { type: "doctype" };
}
function text(node) {
  return { type: "text", value: node.nodeValue || "" };
}
function comment(node) {
  return { type: "comment", value: node.nodeValue || "" };
}
function element(node, options) {
  const space = node.namespaceURI;
  const x = space === webNamespaces.svg ? s : h;
  const tagName = space === webNamespaces.html ? node.tagName.toLowerCase() : node.tagName;
  const content = (
    // @ts-expect-error: DOM types are wrong, content can exist.
    space === webNamespaces.html && tagName === "template" ? node.content : node
  );
  const attributes = node.getAttributeNames();
  const properties = {};
  let index = -1;
  while (++index < attributes.length) {
    properties[attributes[index]] = node.getAttribute(attributes[index]) || "";
  }
  return x(tagName, properties, all(content, options));
}
function all(node, options) {
  const nodes = node.childNodes;
  const children = [];
  let index = -1;
  while (++index < nodes.length) {
    const child = transform(nodes[index], options);
    if (child !== void 0) {
      children.push(child);
    }
  }
  return children;
}

// node_modules/.pnpm/hast-util-from-html-isomorphic@2.0.0/node_modules/hast-util-from-html-isomorphic/lib/browser.js
var parser = new DOMParser();
function fromHtmlIsomorphic(value, options) {
  const node = (options == null ? void 0 : options.fragment) ? parseFragment(value) : parser.parseFromString(value, "text/html");
  return (
    /** @type {Root} */
    fromDom(node)
  );
}
function parseFragment(value) {
  const template = document.createElement("template");
  template.innerHTML = value;
  return template.content;
}

// node_modules/.pnpm/rehype-katex@7.0.1/node_modules/rehype-katex/lib/index.js
var emptyOptions = {};
var emptyClasses = [];
function rehypeKatex(options) {
  const settings = options || emptyOptions;
  return function(tree, file) {
    visitParents(tree, "element", function(element2, parents) {
      const classes = Array.isArray(element2.properties.className) ? element2.properties.className : emptyClasses;
      const languageMath = classes.includes("language-math");
      const mathDisplay = classes.includes("math-display");
      const mathInline = classes.includes("math-inline");
      let displayMode = mathDisplay;
      if (!languageMath && !mathDisplay && !mathInline) {
        return;
      }
      let parent = parents[parents.length - 1];
      let scope = element2;
      if (element2.tagName === "code" && languageMath && parent && parent.type === "element" && parent.tagName === "pre") {
        scope = parent;
        parent = parents[parents.length - 2];
        displayMode = true;
      }
      if (!parent) return;
      const value = toText(scope, { whitespace: "pre" });
      let result;
      try {
        result = katex.renderToString(value, {
          ...settings,
          displayMode,
          throwOnError: true
        });
      } catch (error) {
        const cause = (
          /** @type {Error} */
          error
        );
        const ruleId = cause.name.toLowerCase();
        file.message("Could not render math with KaTeX", {
          ancestors: [...parents, element2],
          cause,
          place: element2.position,
          ruleId,
          source: "rehype-katex"
        });
        try {
          result = katex.renderToString(value, {
            ...settings,
            displayMode,
            strict: "ignore",
            throwOnError: false
          });
        } catch {
          result = [
            {
              type: "element",
              tagName: "span",
              properties: {
                className: ["katex-error"],
                style: "color:" + (settings.errorColor || "#cc0000"),
                title: String(error)
              },
              children: [{ type: "text", value }]
            }
          ];
        }
      }
      if (typeof result === "string") {
        const root2 = fromHtmlIsomorphic(result, { fragment: true });
        result = /** @type {Array<ElementContent>} */
        root2.children;
      }
      const index = parent.children.indexOf(scope);
      parent.children.splice(index, 1, ...result);
      return SKIP;
    });
  };
}
export {
  rehypeKatex as default
};
//# sourceMappingURL=rehype-katex.js.map
