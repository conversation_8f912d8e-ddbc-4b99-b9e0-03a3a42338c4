import React, { useState } from 'react';
import { Button, Input, Card, Space, Typography, message } from 'antd';
import { observer } from 'mobx-react-lite';
import { useChatStoreOnly } from '../stores';

const { Title, Text } = Typography;

export const NewChatTest: React.FC = observer(() => {
  const chatStore = useChatStoreOnly();
  const [testMessage, setTestMessage] = useState('测试新建聊天消息');

  const testNewChat = () => {
    try {
      console.log('=== 开始新建聊天测试 ===');
      
      // 1. 创建新聊天
      const newSession = chatStore.newSession();
      console.log('创建新会话:', newSession.id);
      
      // 2. 打印当前状态
      chatStore.debugSessionState();
      
      // 3. 测试向临时会话添加消息
      const success = chatStore.onUserInputBySessionId(newSession.id, testMessage, undefined, true);
      console.log('添加用户消息结果:', success);
      
      if (success) {
        message.success('新建聊天测试成功！');
        console.log('测试成功，打印最终状态:');
        chatStore.debugSessionState();
      } else {
        message.error('新建聊天测试失败！');
      }
      
    } catch (error) {
      console.error('测试过程中出错:', error);
      message.error('测试失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  const testTempSessionSearch = () => {
    try {
      console.log('=== 测试临时会话查找 ===');
      
      // 创建新会话
      const newSession = chatStore.newSession();
      console.log('创建临时会话:', newSession.id);
      
      // 测试查找逻辑
      const foundInTemp = chatStore.tempSession && chatStore.tempSession.id === newSession.id;
      const foundInSessions = chatStore.sessions.findIndex(s => s.id === newSession.id) !== -1;
      
      console.log('临时会话中找到:', foundInTemp);
      console.log('正式会话中找到:', foundInSessions);
      
      message.info(`临时会话: ${foundInTemp ? '找到' : '未找到'}, 正式会话: ${foundInSessions ? '找到' : '未找到'}`);
      
    } catch (error) {
      console.error('查找测试出错:', error);
      message.error('查找测试失败');
    }
  };

  const clearAll = () => {
    chatStore.clearAllData();
    message.success('已清除所有数据');
  };

  return (
    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <Title level={2}>新建聊天测试</Title>
      
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        <Card title="测试消息">
          <Input
            value={testMessage}
            onChange={(e) => setTestMessage(e.target.value)}
            placeholder="输入测试消息"
          />
        </Card>

        <Card title="测试操作">
          <Space wrap>
            <Button type="primary" onClick={testNewChat}>
              测试新建聊天
            </Button>
            <Button onClick={testTempSessionSearch}>
              测试会话查找
            </Button>
            <Button danger onClick={clearAll}>
              清除所有数据
            </Button>
          </Space>
        </Card>

        <Card title="当前状态">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text><strong>正式会话数:</strong> {chatStore.sessions.length}</Text>
            <Text><strong>临时会话:</strong> {chatStore.tempSession ? `存在 (${chatStore.tempSession.id})` : '不存在'}</Text>
            <Text><strong>当前会话索引:</strong> {chatStore.currentSessionIndex}</Text>
            
            {chatStore.tempSession && (
              <Card size="small" title="临时会话详情" style={{ backgroundColor: '#fff7e6' }}>
                <Text>ID: {chatStore.tempSession.id}</Text><br />
                <Text>主题: {chatStore.tempSession.topic}</Text><br />
                <Text>消息数: {chatStore.tempSession.messages.length}</Text>
                {chatStore.tempSession.messages.map((msg, index) => (
                  <div key={index} style={{ marginLeft: '10px', marginTop: '5px' }}>
                    <Text type="secondary">
                      [{msg.role}] {typeof msg.content === 'string' ? msg.content : '[多媒体]'}
                    </Text>
                  </div>
                ))}
              </Card>
            )}
            
            {chatStore.sessions.map((session, index) => (
              <Card 
                key={session.id} 
                size="small" 
                title={`会话 ${index + 1} - ${session.topic}`}
                style={{ backgroundColor: index === chatStore.currentSessionIndex ? '#f6ffed' : '#fafafa' }}
              >
                <Text>ID: {session.id}</Text><br />
                <Text>消息数: {session.messages.length}</Text>
                {session.messages.map((msg, msgIndex) => (
                  <div key={msgIndex} style={{ marginLeft: '10px', marginTop: '5px' }}>
                    <Text type="secondary">
                      [{msg.role}] {typeof msg.content === 'string' ? msg.content : '[多媒体]'}
                    </Text>
                  </div>
                ))}
              </Card>
            ))}
          </Space>
        </Card>
      </Space>
    </div>
  );
});

export default NewChatTest;
