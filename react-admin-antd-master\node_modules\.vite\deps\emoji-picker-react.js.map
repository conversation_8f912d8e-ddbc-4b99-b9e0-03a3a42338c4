{"version": 3, "sources": ["../../.pnpm/flairup@1.0.0/node_modules/flairup/src/utils/asArray.ts", "../../.pnpm/flairup@1.0.0/node_modules/flairup/src/utils/is.ts", "../../.pnpm/flairup@1.0.0/node_modules/flairup/src/utils/joinTruthy.ts", "../../.pnpm/flairup@1.0.0/node_modules/flairup/src/utils/stableHash.ts", "../../.pnpm/flairup@1.0.0/node_modules/flairup/src/utils/stringManipulators.ts", "../../.pnpm/flairup@1.0.0/node_modules/flairup/src/Rule.ts", "../../.pnpm/flairup@1.0.0/node_modules/flairup/src/Sheet.ts", "../../.pnpm/flairup@1.0.0/node_modules/flairup/src/utils/forIn.ts", "../../.pnpm/flairup@1.0.0/node_modules/flairup/src/cx.ts", "../../.pnpm/flairup@1.0.0/node_modules/flairup/src/index.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/DomUtils/classNames.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/Stylesheet/stylesheet.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/config/compareConfig.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/Reactions/DEFAULT_REACTIONS.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/types/exposedTypes.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/config/categoryConfig.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/config/cdnUrls.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/data/skinToneVariations.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/dataUtils/DataTypes.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/dataUtils/alphaNumericEmojiIndex.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/dataUtils/emojiSelectors.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/config/config.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/context/PickerConfigContext.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useDebouncedState.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useHideEmojisByUniocode.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useDisallowedEmojis.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useInitialLoad.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/context/PickerContext.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/config/mutableConfig.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/config/useConfig.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useIsSearchMode.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/DomUtils/focusElement.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/DomUtils/getActiveElement.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/context/ElementRefContext.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/DomUtils/scrollTo.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/DomUtils/keyboardNavigation.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useCloseAllOpenToggles.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useDisallowMouseMove.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useFocus.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useFilter.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useSetVariationPicker.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useShouldShowSkinTonePicker.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useKeyboardNavigation.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/preloadEmoji.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useOnFocus.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/main/PickerMain.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/DomUtils/elementPositionInRow.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/DomUtils/selectors.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/dataUtils/parseNativeEmoji.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/dataUtils/suggested.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/typeRefinements/typeRefinements.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useMouseDownHandlers.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/atoms/Button.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/emoji/ClickableEmojiButton.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/emoji/emojiStyles.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/emoji/EmojiImg.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/emoji/NativeEmoji.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/emoji/ViewOnlyEmoji.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/emoji/Emoji.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/Reactions/BtnPlus.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/Reactions/Reactions.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useOnScroll.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useIsEmojiHidden.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/body/EmojiCategory.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useIsEverMounted.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/body/Suggested.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/body/EmojiList.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/body/EmojiVariationPicker.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/body/Body.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/DomUtils/detectEmojyPartiallyBelowFold.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useEmojiPreviewEvents.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/Layout/Flex.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/Layout/Space.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/Layout/Absolute.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/Layout/Relative.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/header/SkinTonePicker/BtnSkinToneVariation.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/header/SkinTonePicker/SkinTonePicker.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/footer/Preview.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/DomUtils/categoryNameFromDom.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useActiveCategoryScrollDetection.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useScrollCategoryIntoView.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/hooks/useShouldHideCustomEmojis.ts", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/navigation/CategoryButton.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/navigation/CategoryNavigation.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/header/Search/BtnClearSearch.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/header/Search/CssSearch.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/header/Search/IcnSearch.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/header/Search/Search.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/header/Header.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/EmojiPickerReact.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/ErrorBoundary.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/components/emoji/ExportedEmoji.tsx", "../../.pnpm/emoji-picker-react@4.13.2_react@18.3.1/node_modules/emoji-picker-react/src/index.tsx"], "sourcesContent": ["export function asArray<T>(v: T | T[]): T[] {\n  return [].concat(v as unknown as []);\n}\n", "import { ClassName } from '../types.js';\n\nexport function isPsuedoSelector(selector: string): boolean {\n  return selector.startsWith(':');\n}\n\nexport function isStyleCondition(selector: string): boolean {\n  return (\n    isString(selector) &&\n    (selector === '*' ||\n      (selector.length > 1 && ':>~.+*'.includes(selector.slice(0, 1))) ||\n      isImmediatePostcondition(selector))\n  );\n}\n\nexport function isValidProperty(\n  property: string,\n  value: unknown,\n): value is string {\n  return (\n    (isString(value) || typeof value === 'number') &&\n    !isCssVariables(property) &&\n    !isPsuedoSelector(property) &&\n    !isMediaQuery(property)\n  );\n}\n\nexport function isMediaQuery(selector: string): boolean {\n  return selector.startsWith('@media');\n}\n\nexport function isDirectClass(selector: string): boolean {\n  return selector === '.';\n}\n\nexport function isCssVariables(selector: string): boolean {\n  return selector === '--';\n}\n\nexport function isString(value: unknown): value is string {\n  return value + '' === value;\n}\n\nexport function isClassName(value: unknown): value is ClassName {\n  return isString(value) && value.length > 1 && value.startsWith('.');\n}\n\nexport function isImmediatePostcondition(\n  value: unknown,\n): value is `&${string}` {\n  return isString(value) && (value.startsWith('&') || isPsuedoSelector(value));\n}\n", "export function joinTruthy(arr: unknown[], delimiter: string = ''): string {\n  return arr.filter(Boolean).join(delimiter);\n}\n", "// Stable hash function.\nexport function stableHash(prefix: string, seed: string): string {\n  let hash = 0;\n  if (seed.length === 0) return hash.toString();\n  for (let i = 0; i < seed.length; i++) {\n    const char = seed.charCodeAt(i);\n    hash = (hash << 5) - hash + char;\n    hash = hash & hash; // Convert to 32bit integer\n  }\n  return `${prefix ?? 'cl'}_${hash.toString(36)}`;\n}\n", "// Some properties need special handling\nexport function handlePropertyValue(property: string, value: string): string {\n  if (property === 'content') {\n    return `\"${value}\"`;\n  }\n\n  return value;\n}\n\nexport function camelCaseToDash(str: string): string {\n  return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n}\n\nexport function joinedProperty(property: string, value: string): string {\n  return `${property}:${value}`;\n}\n\nexport function toClass(str: string): string {\n  return str ? `.${str}` : '';\n}\n\nexport function appendString(base: string, line: string): string {\n  return base ? `${base}\\n${line}` : line;\n}\n", "import { Sheet } from './Sheet';\nimport { asArray } from './utils/asArray';\nimport { isImmediatePostcondition, isPsuedoSelector } from './utils/is';\nimport { joinTruthy } from './utils/joinTruthy';\nimport { stableHash } from './utils/stableHash';\nimport {\n  camelCaseToDash,\n  handlePropertyValue,\n  joinedProperty,\n  toClass,\n} from './utils/stringManipulators';\n\nexport class Rule {\n  public hash: string;\n  public joined: string;\n  public key: string;\n\n  constructor(\n    private sheet: Sheet,\n    public property: string,\n    public value: string,\n    private selector: Selector,\n  ) {\n    this.property = property;\n    this.value = value;\n    this.joined = joinedProperty(property, value);\n    const joinedConditions = this.selector.preconditions.concat(\n      this.selector.postconditions,\n    );\n    this.hash = this.selector.hasConditions\n      ? (this.selector.scopeClassName as string)\n      : stableHash(this.sheet.name, this.joined);\n    this.key = joinTruthy([this.joined, joinedConditions, this.hash]);\n  }\n\n  public toString(): string {\n    let selectors = mergeSelectors(this.selector.preconditions, {\n      right: this.hash,\n    });\n\n    selectors = mergeSelectors(this.selector.postconditions, {\n      left: selectors,\n    });\n\n    return `${selectors} {${Rule.genRule(this.property, this.value)}}`;\n  }\n\n  static genRule(property: string, value: string): string {\n    const transformedProperty = camelCaseToDash(property);\n    return (\n      joinedProperty(\n        transformedProperty,\n        handlePropertyValue(property, value),\n      ) + ';'\n    );\n  }\n}\n\nexport function mergeSelectors(\n  selectors: string[],\n  { left = '', right = '' }: { left?: string; right?: string } = {},\n): string {\n  const output = selectors.reduce((selectors, current) => {\n    if (isPsuedoSelector(current)) {\n      return selectors + current;\n    }\n\n    if (isImmediatePostcondition(current)) {\n      return selectors + current.slice(1);\n    }\n\n    return joinTruthy([selectors, current], ' ');\n\n    // selector then postcondition\n  }, left);\n\n  // preconditions, then selector\n  return joinTruthy([output, toClass(right)], ' ');\n}\n\nexport class Selector {\n  public preconditions: string[] = [];\n  public scopeClassName: string | null = null;\n  public scopeName: string | null = null;\n  public postconditions: string[] = [];\n\n  constructor(\n    private sheet: Sheet,\n    scopeName: string | null = null,\n    {\n      preconditions,\n      postconditions,\n    }: {\n      preconditions?: string[] | string | undefined;\n      postconditions?: string[] | string | undefined;\n    } = {},\n  ) {\n    this.preconditions = preconditions ? asArray(preconditions) : [];\n    this.postconditions = postconditions ? asArray(postconditions) : [];\n    this.setScope(scopeName);\n  }\n\n  private setScope(scopeName: string | null): Selector {\n    if (!scopeName) {\n      return this;\n    }\n\n    if (!this.scopeClassName) {\n      this.scopeName = scopeName;\n      this.scopeClassName = stableHash(\n        this.sheet.name,\n        // adding the count guarantees uniqueness across style.create calls\n        scopeName + this.sheet.count,\n      );\n    }\n\n    return this;\n  }\n\n  get hasConditions(): boolean {\n    return this.preconditions.length > 0 || this.postconditions.length > 0;\n  }\n\n  addScope(scopeName: string): Selector {\n    return new Selector(this.sheet, scopeName, {\n      preconditions: this.preconditions,\n      postconditions: this.postconditions,\n    });\n  }\n\n  addPrecondition(precondition: string): Selector {\n    return new Selector(this.sheet, this.scopeClassName, {\n      postconditions: this.postconditions,\n      preconditions: this.preconditions.concat(precondition),\n    });\n  }\n\n  addPostcondition(postcondition: string): Selector {\n    return new Selector(this.sheet, this.scopeClassName, {\n      preconditions: this.preconditions,\n      postconditions: this.postconditions.concat(postcondition),\n    });\n  }\n\n  createRule(property: string, value: string): Rule {\n    return new Rule(this.sheet, property, value, this);\n  }\n}\n", "import { Rule } from './Rule.js';\nimport { StoredStyles } from './types.js';\nimport { isString } from './utils/is.js';\nimport { appendString } from './utils/stringManipulators.js';\n\nexport class Sheet {\n  private styleTag: HTMLStyleElement | undefined;\n\n  // Hash->css\n  private storedStyles: StoredStyles = {};\n\n  // styles->hash\n  private storedClasses: Record<string, string> = {};\n  private style: string = '';\n  public count = 0;\n  public id: string;\n\n  constructor(\n    public name: string,\n    private rootNode?: HTMLElement | null,\n  ) {\n    this.id = `flairup-${name}`;\n\n    this.styleTag = this.createStyleTag();\n  }\n\n  getStyle(): string {\n    return this.style;\n  }\n\n  append(css: string): void {\n    this.style = appendString(this.style, css);\n  }\n\n  apply(): void {\n    this.count++;\n\n    if (!this.styleTag) {\n      return;\n    }\n\n    this.styleTag.innerHTML = this.style;\n  }\n\n  isApplied(): boolean {\n    return !!this.styleTag;\n  }\n\n  createStyleTag(): HTMLStyleElement | undefined {\n    // check that we're in the browser and have access to the DOM\n    if (\n      typeof document === 'undefined' ||\n      this.isApplied() ||\n      // Explicitly disallow mounting to the DOM\n      this.rootNode === null\n    ) {\n      return this.styleTag;\n    }\n\n    const styleTag = document.createElement('style');\n    styleTag.type = 'text/css';\n    styleTag.id = this.id;\n    (this.rootNode ?? document.head).appendChild(styleTag);\n    return styleTag;\n  }\n\n  addRule(rule: Rule): string {\n    const storedClass = this.storedClasses[rule.key];\n\n    if (isString(storedClass)) {\n      return storedClass;\n    }\n\n    this.storedClasses[rule.key] = rule.hash;\n    this.storedStyles[rule.hash] = [rule.property, rule.value];\n\n    this.append(rule.toString());\n    return rule.hash;\n  }\n}\n", "export function forIn<O extends Record<string, unknown>>(\n  obj: O,\n  fn: (key: string, value: O[string]) => void,\n): void {\n  for (const key in obj) {\n    fn(key.trim(), obj[key]);\n  }\n}\n", "import { joinTruthy } from './utils/joinTruthy';\n\nexport function cx(...args: unknown[]): string {\n  const classes = args.reduce((classes: string[], arg) => {\n    if (arg instanceof Set) {\n      classes.push(...arg);\n    } else if (typeof arg === 'string') {\n      classes.push(arg);\n    } else if (Array.isArray(arg)) {\n      classes.push(cx(...arg));\n    } else if (typeof arg === 'object') {\n      // @ts-expect-error - it is a string\n      Object.entries(arg).forEach(([key, value]) => {\n        if (value) {\n          classes.push(key);\n        }\n      });\n    }\n\n    return classes;\n  }, [] as string[]);\n\n  return joinTruthy(classes, ' ').trim();\n}\n", "import { Rule, Selector, mergeSelectors } from './Rule.js';\nimport { Sheet } from './Sheet.js';\nimport {\n  CSSVariablesObject,\n  ClassSet,\n  CreateSheetInput,\n  DirectClass,\n  ScopedStyles,\n  Styles,\n  createSheetReturn,\n} from './types.js';\nimport { asArray } from './utils/asArray.js';\nimport { forIn } from './utils/forIn.js';\nimport {\n  isCssVariables,\n  isDirectClass,\n  isMediaQuery,\n  isStyleCondition,\n  isValidProperty,\n} from './utils/is.js';\n\nexport { cx } from './cx.js';\n\nexport type { CreateSheetInput, Styles };\n\nexport function createSheet(\n  name: string,\n  rootNode?: HTMLElement | null,\n): createSheetReturn {\n  const sheet = new Sheet(name, rootNode);\n\n  return {\n    create,\n    getStyle: sheet.getStyle.bind(sheet),\n    isApplied: sheet.isApplied.bind(sheet),\n  };\n\n  function create<K extends string>(styles: CreateSheetInput<K>) {\n    const scopedStyles: ScopedStyles<K> = {} as ScopedStyles<K>;\n\n    iteratePreconditions(sheet, styles, new Selector(sheet)).forEach(\n      ([scopeName, styles, selector]) => {\n        iterateStyles(sheet, styles as Styles, selector).forEach(\n          (className) => {\n            addScopedStyle(scopeName as K, className);\n          },\n        );\n      },\n    );\n\n    // Commit the styles to the sheet.\n    // Done only once per create call.\n    // This way we do not update the DOM on every style.\n    sheet.apply();\n\n    return scopedStyles;\n\n    function addScopedStyle(name: K, className: string) {\n      scopedStyles[name as keyof ScopedStyles<K>] =\n        scopedStyles[name as keyof ScopedStyles<K>] ?? new Set<string>();\n      scopedStyles[name as keyof ScopedStyles<K>].add(className);\n    }\n  }\n}\n\n// This one plucks out all of the preconditions\n// and creates selector objects from them\nfunction iteratePreconditions(\n  sheet: Sheet,\n  styles: Styles,\n  selector: Selector,\n) {\n  const output: Array<[string, Styles, Selector]> = [];\n\n  forIn(styles, (key: string, value) => {\n    if (isStyleCondition(key)) {\n      return iteratePreconditions(\n        sheet,\n        value as Styles,\n        selector.addPrecondition(key),\n      ).forEach((item) => output.push(item));\n    }\n\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore - this is a valid case\n    output.push([key, styles[key], selector.addScope(key)]);\n  });\n\n  return output;\n}\n\nfunction iterateStyles(\n  sheet: Sheet,\n  styles: Styles,\n  selector: Selector,\n): ClassSet {\n  const output: ClassSet = new Set<string>();\n  // eslint-disable-next-line max-statements\n  forIn(styles, (property, value) => {\n    let res: string[] | Set<string> = [];\n\n    // Postconditions\n    if (isStyleCondition(property)) {\n      res = iterateStyles(\n        sheet,\n        value as Styles,\n        selector.addPostcondition(property),\n      );\n      // Direct classes: \".\": \"className\"\n    } else if (isDirectClass(property)) {\n      res = asArray(value as DirectClass);\n    } else if (isMediaQuery(property)) {\n      res = handleMediaQuery(sheet, value as Styles, property, selector);\n\n      // \"--\": { \"--variable\": \"value\" }\n    } else if (isCssVariables(property)) {\n      res = cssVariablesBlock(sheet, value as CSSVariablesObject, selector);\n\n      // \"property\": \"value\"\n    } else if (isValidProperty(property, value)) {\n      const rule = selector.createRule(property, value);\n      sheet.addRule(rule);\n      output.add(rule.hash);\n    }\n\n    return addEachClass(res, output);\n  });\n\n  return output;\n}\n\nfunction addEachClass(list: string[] | Set<string>, to: Set<string>) {\n  list.forEach((className) => to.add(className));\n  return to;\n}\n\n// eslint-disable-next-line max-statements\nfunction cssVariablesBlock(\n  sheet: Sheet,\n  styles: CSSVariablesObject,\n  selector: Selector,\n) {\n  const classes: ClassSet = new Set<string>();\n\n  const chunkRows: string[] = [];\n  forIn(styles, (property: string, value) => {\n    if (isValidProperty(property, value)) {\n      chunkRows.push(Rule.genRule(property, value));\n      return;\n    }\n    const res = iterateStyles(sheet, value ?? {}, selector);\n    addEachClass(res, classes);\n  });\n\n  if (!selector.scopeClassName) {\n    return classes;\n  }\n\n  if (chunkRows.length) {\n    const output = chunkRows.join(' ');\n    sheet.append(\n      `${mergeSelectors(selector.preconditions, {\n        right: selector.scopeClassName,\n      })} {${output}}`,\n    );\n  }\n\n  classes.add(selector.scopeClassName);\n  return classes;\n}\n\nfunction handleMediaQuery(\n  sheet: Sheet,\n  styles: Styles,\n  mediaQuery: string,\n  selector: Selector,\n) {\n  sheet.append(mediaQuery + ' {');\n\n  // iterateStyles will internally append each rule to the sheet\n  // as needed. All we have to do is just open the block and close it after.\n  const output = iterateStyles(sheet, styles, selector);\n\n  sheet.append('}');\n\n  return output;\n}\n", "export enum ClassNames {\n  hiddenOnSearch = 'epr-hidden-on-search',\n  searchActive = 'epr-search-active',\n  hidden = 'epr-hidden',\n  visible = 'epr-visible',\n  active = 'epr-active',\n  emoji = 'epr-emoji',\n  category = 'epr-emoji-category',\n  label = 'epr-emoji-category-label',\n  categoryContent = 'epr-emoji-category-content',\n  emojiHasVariations = 'epr-emoji-has-variations',\n  scrollBody = 'epr-body',\n  emojiList = 'epr-emoji-list',\n  external = '__EmojiPicker__',\n  emojiPicker = 'EmojiPickerReact',\n  open = 'epr-open',\n  vertical = 'epr-vertical',\n  horizontal = 'epr-horizontal',\n  variationPicker = 'epr-emoji-variation-picker',\n  darkTheme = 'epr-dark-theme',\n  autoTheme = 'epr-auto-theme'\n}\n\nexport function asSelectors(...classNames: ClassNames[]): string {\n  return classNames.map(c => `.${c}`).join('');\n}\n", "import { Styles, createSheet } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../DomUtils/classNames';\n\nexport const stylesheet = createSheet('epr', null);\n\nconst hidden = {\n  display: 'none',\n  opacity: '0',\n  pointerEvents: 'none',\n  visibility: 'hidden',\n  overflow: 'hidden'\n};\n\nexport const commonStyles = stylesheet.create({\n  hidden: {\n    '.': ClassNames.hidden,\n    ...hidden\n  }\n});\n\nexport const PickerStyleTag = React.memo(function PickerStyleTag() {\n  return (\n    <style\n      suppressHydrationWarning\n      dangerouslySetInnerHTML={{ __html: stylesheet.getStyle() }}\n    />\n  );\n});\n\nexport const commonInteractionStyles = stylesheet.create({\n  '.epr-main': {\n    ':has(input:not(:placeholder-shown))': {\n      categoryBtn: {\n        ':hover': {\n          opacity: '1',\n          backgroundPositionY: 'var(--epr-category-navigation-button-size)'\n        }\n      },\n      hiddenOnSearch: {\n        '.': ClassNames.hiddenOnSearch,\n        ...hidden\n      }\n    },\n    ':has(input:placeholder-shown)': {\n      visibleOnSearchOnly: hidden\n    }\n  },\n  hiddenOnReactions: {\n    transition: 'all 0.5s ease-in-out'\n  },\n  '.epr-reactions': {\n    hiddenOnReactions: {\n      height: '0px',\n      width: '0px',\n      opacity: '0',\n      pointerEvents: 'none',\n      overflow: 'hidden'\n    }\n  },\n  '.EmojiPickerReact:not(.epr-search-active)': {\n    categoryBtn: {\n      ':hover': {\n        opacity: '1',\n        backgroundPositionY: 'var(--epr-category-navigation-button-size)'\n      },\n      '&.epr-active': {\n        opacity: '1',\n        backgroundPositionY: 'var(--epr-category-navigation-button-size)'\n      }\n    },\n    visibleOnSearchOnly: {\n      '.': 'epr-visible-on-search-only',\n      ...hidden\n    }\n  }\n});\n\nexport function darkMode(key: string, value: Styles) {\n  return {\n    '.epr-dark-theme': {\n      [key]: value\n    },\n    '.epr-auto-theme': {\n      [key]: {\n        '@media (prefers-color-scheme: dark)': value\n      }\n    }\n  };\n}\n", "import { PickerConfig } from './config';\n\n// eslint-disable-next-line complexity\nexport function compareConfig(prev: PickerConfig, next: PickerConfig) {\n  const prevCustomEmojis = prev.customEmojis ?? [];\n  const nextCustomEmojis = next.customEmojis ?? [];\n  return (\n    prev.open === next.open &&\n    prev.emojiVersion === next.emojiVersion &&\n    prev.reactionsDefaultOpen === next.reactionsDefaultOpen &&\n    prev.searchPlaceHolder === next.searchPlaceHolder &&\n    prev.searchPlaceholder === next.searchPlaceholder &&\n    prev.defaultSkinTone === next.defaultSkinTone &&\n    prev.skinTonesDisabled === next.skinTonesDisabled &&\n    prev.autoFocusSearch === next.autoFocusSearch &&\n    prev.emojiStyle === next.emojiStyle &&\n    prev.theme === next.theme &&\n    prev.suggestedEmojisMode === next.suggestedEmojisMode &&\n    prev.lazyLoadEmojis === next.lazyLoadEmojis &&\n    prev.className === next.className &&\n    prev.height === next.height &&\n    prev.width === next.width &&\n    prev.style === next.style &&\n    prev.searchDisabled === next.searchDisabled &&\n    prev.skinTonePickerLocation === next.skinTonePickerLocation &&\n    prevCustomEmojis.length === nextCustomEmojis.length\n  );\n}", "export const DEFAULT_REACTIONS = [\n  '1f44d', // 👍\n  '2764-fe0f', // ❤️\n  '1f603', // 😃\n  '1f622', // 😢\n  '1f64f', // 🙏\n  '1f44e', // 👎\n  '1f621' // 😡\n];\n", "export type EmojiClickData = {\n  activeSkinTone: SkinTones;\n  unified: string;\n  unifiedWithoutSkinTone: string;\n  emoji: string;\n  names: string[];\n  imageUrl: string;\n  getImageUrl: (emojiStyle?: EmojiStyle) => string;\n  isCustom: boolean;\n};\n\nexport enum SuggestionMode {\n  RECENT = 'recent',\n  FREQUENT = 'frequent'\n}\n\nexport enum EmojiStyle {\n  NATIVE = 'native',\n  APPLE = 'apple',\n  TWITTER = 'twitter',\n  GOOGLE = 'google',\n  FACEBOOK = 'facebook'\n}\n\nexport enum Theme {\n  DARK = 'dark',\n  LIGHT = 'light',\n  AUTO = 'auto'\n}\n\nexport enum SkinTones {\n  NEUTRAL = 'neutral',\n  LIGHT = '1f3fb',\n  MEDIUM_LIGHT = '1f3fc',\n  MEDIUM = '1f3fd',\n  MEDIUM_DARK = '1f3fe',\n  DARK = '1f3ff'\n}\n\nexport enum Categories {\n  SUGGESTED = 'suggested',\n  CUSTOM = 'custom',\n  SMILEYS_PEOPLE = 'smileys_people',\n  ANIMALS_NATURE = 'animals_nature',\n  FOOD_DRINK = 'food_drink',\n  TRAVEL_PLACES = 'travel_places',\n  ACTIVITIES = 'activities',\n  OBJECTS = 'objects',\n  SYMBOLS = 'symbols',\n  FLAGS = 'flags'\n}\n\nexport enum SkinTonePickerLocation {\n  SEARCH = 'SEARCH',\n  PREVIEW = 'PREVIEW'\n}\n", "import { Categories, SuggestionMode } from '../types/exposedTypes';\n\nexport { Categories };\n\nconst categoriesOrdered: Categories[] = [\n  Categories.SUGGESTED,\n  Categories.CUSTOM,\n  Categories.SMILEYS_PEOPLE,\n  Categories.ANIMALS_NATURE,\n  Categories.FOOD_DRINK,\n  Categories.TRAVEL_PLACES,\n  Categories.ACTIVITIES,\n  Categories.OBJECTS,\n  Categories.SYMBOLS,\n  Categories.FLAGS\n];\n\nexport const SuggestedRecent: CategoryConfig = {\n  name: 'Recently Used',\n  category: Categories.SUGGESTED\n};\n\nexport type CustomCategoryConfig = {\n  category: Categories.CUSTOM;\n  name: string;\n};\n\nconst configByCategory: Record<Categories, CategoryConfig> = {\n  [Categories.SUGGESTED]: {\n    category: Categories.SUGGESTED,\n    name: 'Frequently Used'\n  },\n  [Categories.CUSTOM]: {\n    category: Categories.CUSTOM,\n    name: 'Custom Emojis'\n  },\n  [Categories.SMILEYS_PEOPLE]: {\n    category: Categories.SMILEYS_PEOPLE,\n    name: 'Smileys & People'\n  },\n  [Categories.ANIMALS_NATURE]: {\n    category: Categories.ANIMALS_NATURE,\n    name: 'Animals & Nature'\n  },\n  [Categories.FOOD_DRINK]: {\n    category: Categories.FOOD_DRINK,\n    name: 'Food & Drink'\n  },\n  [Categories.TRAVEL_PLACES]: {\n    category: Categories.TRAVEL_PLACES,\n    name: 'Travel & Places'\n  },\n  [Categories.ACTIVITIES]: {\n    category: Categories.ACTIVITIES,\n    name: 'Activities'\n  },\n  [Categories.OBJECTS]: {\n    category: Categories.OBJECTS,\n    name: 'Objects'\n  },\n  [Categories.SYMBOLS]: {\n    category: Categories.SYMBOLS,\n    name: 'Symbols'\n  },\n  [Categories.FLAGS]: {\n    category: Categories.FLAGS,\n    name: 'Flags'\n  }\n};\n\nexport function baseCategoriesConfig(\n  modifiers?: Record<Categories, CategoryConfig>\n): CategoriesConfig {\n  return categoriesOrdered.map(category => {\n    return {\n      ...configByCategory[category],\n      ...(modifiers && modifiers[category] && modifiers[category])\n    };\n  });\n}\n\nexport function categoryFromCategoryConfig(category: CategoryConfig) {\n  return category.category;\n}\n\nexport function categoryNameFromCategoryConfig(category: CategoryConfig) {\n  return category.name;\n}\n\nexport type CategoriesConfig = CategoryConfig[];\n\nexport type CategoryConfig = {\n  category: Categories;\n  name: string;\n};\n\nexport type UserCategoryConfig = Array<Categories | CategoryConfig>;\n\nexport function mergeCategoriesConfig(\n  userCategoriesConfig: UserCategoryConfig = [],\n  modifiers: CategoryConfigModifiers = {}\n): CategoriesConfig {\n  const extra = {} as Record<Categories, CategoryConfig>;\n\n  if (modifiers.suggestionMode === SuggestionMode.RECENT) {\n    extra[Categories.SUGGESTED] = SuggestedRecent;\n  }\n\n  const base = baseCategoriesConfig(extra);\n  if (!userCategoriesConfig?.length) {\n    return base;\n  }\n\n  return userCategoriesConfig.map(category => {\n    if (typeof category === 'string') {\n      return getBaseConfigByCategory(category, extra[category]);\n    }\n\n    return {\n      ...getBaseConfigByCategory(category.category, extra[category.category]),\n      ...category\n    };\n  });\n}\n\nfunction getBaseConfigByCategory(\n  category: Categories,\n  modifier: CategoryConfig = {} as CategoryConfig\n) {\n  return Object.assign(configByCategory[category], modifier);\n}\n\ntype CategoryConfigModifiers = {\n  suggestionMode?: SuggestionMode;\n};\n", "import { EmojiStyle } from '../types/exposedTypes';\n\nconst CDN_URL_APPLE =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/';\nconst CDN_URL_FACEBOOK =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-facebook/img/facebook/64/';\nconst CDN_URL_TWITTER =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-twitter/img/twitter/64/';\nconst CDN_URL_GOOGLE =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-google/img/google/64/';\n\nexport function cdnUrl(emojiStyle: EmojiStyle): string {\n  switch (emojiStyle) {\n    case EmojiStyle.TWITTER:\n      return CDN_URL_TWITTER;\n    case EmojiStyle.GOOGLE:\n      return CDN_URL_GOOGLE;\n    case EmojiStyle.FACEBOOK:\n      return CDN_URL_FACEBOOK;\n    case EmojiStyle.APPLE:\n    default:\n      return CDN_URL_APPLE;\n  }\n}\n", "import { SkinTones } from '../types/exposedTypes';\n\nconst skinToneVariations = [\n  SkinTones.NEUTRAL,\n  SkinTones.LIGHT,\n  SkinTones.MEDIUM_LIGHT,\n  SkinTones.MEDIUM,\n  SkinTones.MEDIUM_DARK,\n  SkinTones.DARK\n];\n\nexport const skinTonesNamed = Object.entries(SkinTones).reduce(\n  (acc, [key, value]) => {\n    acc[value] = key;\n    return acc;\n  },\n  {} as Record<string, string>\n);\n\nexport const skinTonesMapped: Record<\n  string,\n  string\n> = skinToneVariations.reduce(\n  (mapped, skinTone) =>\n    Object.assign(mapped, {\n      [skinTone]: skinTone\n    }),\n  {}\n);\n\nexport default skinToneVariations;\n", "import emojis from '../data/emojis';\n\nexport enum EmojiProperties {\n  name = 'n',\n  unified = 'u',\n  variations = 'v',\n  added_in = 'a',\n  imgUrl = 'imgUrl'\n}\n\nexport interface DataEmoji extends WithName {\n  [EmojiProperties.unified]: string;\n  [EmojiProperties.variations]?: string[];\n  [EmojiProperties.added_in]: string;\n  [EmojiProperties.imgUrl]?: string;\n}\n\nexport type DataEmojis = DataEmoji[];\n\nexport type DataGroups = keyof typeof emojis;\n\nexport type WithName = {\n  [EmojiProperties.name]: string[];\n};\n", "import { DataEmoji } from './DataTypes';\nimport { allEmojis, emojiNames, emojiUnified } from './emojiSelectors';\n\nexport const alphaNumericEmojiIndex: BaseIndex = {};\n\nsetTimeout(() => {\n  allEmojis.reduce((searchIndex, emoji) => {\n    indexEmoji(emoji);\n    return searchIndex;\n  }, alphaNumericEmojiIndex as BaseIndex);\n});\n\ntype BaseIndex = Record<string, Record<string, DataEmoji>>;\n\nexport function indexEmoji(emoji: DataEmoji): void {\n  const joinedNameString = emojiNames(emoji)\n    .flat()\n    .join('')\n    .toLowerCase()\n    .replace(/[^a-zA-Z\\d]/g, '')\n    .split('');\n\n  joinedNameString.forEach(char => {\n    alphaNumericEmojiIndex[char] = alphaNumericEmojiIndex[char] ?? {};\n\n    alphaNumericEmojiIndex[char][emojiUnified(emoji)] = emoji;\n  });\n}\n", "import { Categories } from '../config/categoryConfig';\nimport { cdnUrl } from '../config/cdnUrls';\nimport { CustomEmoji } from '../config/customEmojiConfig';\nimport emojis from '../data/emojis';\nimport skinToneVariations, {\n  skinTonesMapped\n} from '../data/skinToneVariations';\nimport { EmojiStyle, SkinTones } from '../types/exposedTypes';\n\nimport { DataEmoji, DataEmojis, EmojiProperties, WithName } from './DataTypes';\nimport { indexEmoji } from './alphaNumericEmojiIndex';\n\nexport function emojiNames(emoji: WithName): string[] {\n  return emoji[EmojiProperties.name] ?? [];\n}\n\nexport function addedIn(emoji: DataEmoji): number {\n  return parseFloat(emoji[EmojiProperties.added_in]);\n}\n\nexport function emojiName(emoji?: WithName): string {\n  if (!emoji) {\n    return '';\n  }\n\n  return emojiNames(emoji)[0];\n}\n\nexport function unifiedWithoutSkinTone(unified: string): string {\n  const splat = unified.split('-');\n  const [skinTone] = splat.splice(1, 1);\n\n  if (skinTonesMapped[skinTone]) {\n    return splat.join('-');\n  }\n\n  return unified;\n}\n\nexport function emojiUnified(emoji: DataEmoji, skinTone?: string): string {\n  const unified = emoji[EmojiProperties.unified];\n\n  if (!skinTone || !emojiHasVariations(emoji)) {\n    return unified;\n  }\n\n  return emojiVariationUnified(emoji, skinTone) ?? unified;\n}\n\nexport function emojisByCategory(category: Categories): DataEmojis {\n  // @ts-ignore\n  return emojis?.[category] ?? [];\n}\n\n// WARNING: DO NOT USE DIRECTLY\nexport function emojiUrlByUnified(\n  unified: string,\n  emojiStyle: EmojiStyle\n): string {\n  return `${cdnUrl(emojiStyle)}${unified}.png`;\n}\n\nexport function emojiVariations(emoji: DataEmoji): string[] {\n  return emoji[EmojiProperties.variations] ?? [];\n}\n\nexport function emojiHasVariations(emoji: DataEmoji): boolean {\n  return emojiVariations(emoji).length > 0;\n}\n\nexport function emojiVariationUnified(\n  emoji: DataEmoji,\n  skinTone?: string\n): string | undefined {\n  return skinTone\n    ? emojiVariations(emoji).find(variation => variation.includes(skinTone))\n    : emojiUnified(emoji);\n}\n\nexport function emojiByUnified(unified?: string): DataEmoji | undefined {\n  if (!unified) {\n    return;\n  }\n\n  if (allEmojisByUnified[unified]) {\n    return allEmojisByUnified[unified];\n  }\n\n  const withoutSkinTone = unifiedWithoutSkinTone(unified);\n  return allEmojisByUnified[withoutSkinTone];\n}\n\nexport const allEmojis: DataEmojis = Object.values(emojis).flat();\n\nexport function setCustomEmojis(customEmojis: CustomEmoji[]): void {\n  emojis[Categories.CUSTOM].length = 0;\n\n  customEmojis.forEach(emoji => {\n    const emojiData = customToRegularEmoji(emoji);\n\n    emojis[Categories.CUSTOM].push(emojiData as never);\n\n    if (allEmojisByUnified[emojiData[EmojiProperties.unified]]) {\n      return;\n    }\n\n    allEmojis.push(emojiData);\n    allEmojisByUnified[emojiData[EmojiProperties.unified]] = emojiData;\n    indexEmoji(emojiData);\n  });\n}\n\nfunction customToRegularEmoji(emoji: CustomEmoji): DataEmoji {\n  return {\n    [EmojiProperties.name]: emoji.names.map(name => name.toLowerCase()),\n    [EmojiProperties.unified]: emoji.id.toLowerCase(),\n    [EmojiProperties.added_in]: '0',\n    [EmojiProperties.imgUrl]: emoji.imgUrl\n  };\n}\n\nconst allEmojisByUnified: {\n  [unified: string]: DataEmoji;\n} = {};\n\nsetTimeout(() => {\n  allEmojis.reduce((allEmojis, Emoji) => {\n    allEmojis[emojiUnified(Emoji)] = Emoji;\n\n    if (emojiHasVariations(Emoji)) {\n      emojiVariations(Emoji).forEach(variation => {\n        allEmojis[variation] = Emoji;\n      });\n    }\n\n    return allEmojis;\n  }, allEmojisByUnified);\n});\n\nexport function activeVariationFromUnified(unified: string): SkinTones | null {\n  const [, suspectedSkinTone] = unified.split('-') as [string, SkinTones];\n  return skinToneVariations.includes(suspectedSkinTone)\n    ? suspectedSkinTone\n    : null;\n}\n", "import * as React from 'react';\n\nimport { DEFAULT_REACTIONS } from '../components/Reactions/DEFAULT_REACTIONS';\nimport { GetEmojiUrl } from '../components/emoji/BaseEmojiProps';\nimport {\n  setCustomEmojis,\n  emojiUrlByUnified,\n} from '../dataUtils/emojiSelectors';\nimport {\n  EmojiClickData,\n  EmojiStyle,\n  SkinTonePickerLocation,\n  SkinTones,\n  SuggestionMode,\n  Theme,\n} from '../types/exposedTypes';\n\nimport {\n  CategoriesConfig,\n  baseCategoriesConfig,\n  mergeCategoriesConfig,\n} from './categoryConfig';\nimport { CustomEmoji } from './customEmojiConfig';\n\nconst KNOWN_FAILING_EMOJIS = ['2640-fe0f', '2642-fe0f', '2695-fe0f'];\n\nexport const DEFAULT_SEARCH_PLACEHOLDER = 'Search';\nexport const SEARCH_RESULTS_NO_RESULTS_FOUND = 'No results found';\nexport const SEARCH_RESULTS_SUFFIX =\n  ' found. Use up and down arrow keys to navigate.';\nexport const SEARCH_RESULTS_ONE_RESULT_FOUND =\n  '1 result' + SEARCH_RESULTS_SUFFIX;\nexport const SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND =\n  '%n results' + SEARCH_RESULTS_SUFFIX;\n\nexport function mergeConfig(\n  userConfig: PickerConfig = {}\n): PickerConfigInternal {\n  const base = basePickerConfig();\n\n  const previewConfig = Object.assign(\n    base.previewConfig,\n    userConfig.previewConfig ?? {}\n  );\n  const config = Object.assign(base, userConfig);\n\n  const categories = mergeCategoriesConfig(userConfig.categories, {\n    suggestionMode: config.suggestedEmojisMode,\n  });\n\n  config.hiddenEmojis.forEach((emoji) => {\n    config.unicodeToHide.add(emoji);\n  });\n\n  setCustomEmojis(config.customEmojis ?? []);\n\n  const skinTonePickerLocation = config.searchDisabled\n    ? SkinTonePickerLocation.PREVIEW\n    : config.skinTonePickerLocation;\n\n  return {\n    ...config,\n    categories,\n    previewConfig,\n    skinTonePickerLocation,\n  };\n}\n\nexport function basePickerConfig(): PickerConfigInternal {\n  return {\n    autoFocusSearch: true,\n    categories: baseCategoriesConfig(),\n    className: '',\n    customEmojis: [],\n    defaultSkinTone: SkinTones.NEUTRAL,\n    emojiStyle: EmojiStyle.APPLE,\n    emojiVersion: null,\n    getEmojiUrl: emojiUrlByUnified,\n    height: 450,\n    lazyLoadEmojis: false,\n    previewConfig: {\n      ...basePreviewConfig,\n    },\n    searchDisabled: false,\n    searchPlaceHolder: DEFAULT_SEARCH_PLACEHOLDER,\n    searchPlaceholder: DEFAULT_SEARCH_PLACEHOLDER,\n    skinTonePickerLocation: SkinTonePickerLocation.SEARCH,\n    skinTonesDisabled: false,\n    style: {},\n    suggestedEmojisMode: SuggestionMode.FREQUENT,\n    theme: Theme.LIGHT,\n    unicodeToHide: new Set<string>(KNOWN_FAILING_EMOJIS),\n    width: 350,\n    reactionsDefaultOpen: false,\n    reactions: DEFAULT_REACTIONS,\n    open: true,\n    allowExpandReactions: true,\n    hiddenEmojis: [],\n  };\n}\n\nexport type PickerConfigInternal = {\n  emojiVersion: string | null;\n  searchPlaceHolder: string;\n  searchPlaceholder: string;\n  defaultSkinTone: SkinTones;\n  skinTonesDisabled: boolean;\n  autoFocusSearch: boolean;\n  emojiStyle: EmojiStyle;\n  categories: CategoriesConfig;\n  theme: Theme;\n  suggestedEmojisMode: SuggestionMode;\n  lazyLoadEmojis: boolean;\n  previewConfig: PreviewConfig;\n  className: string;\n  height: PickerDimensions;\n  width: PickerDimensions;\n  style: React.CSSProperties;\n  getEmojiUrl: GetEmojiUrl;\n  searchDisabled: boolean;\n  skinTonePickerLocation: SkinTonePickerLocation;\n  unicodeToHide: Set<string>;\n  customEmojis: CustomEmoji[];\n  reactionsDefaultOpen: boolean;\n  reactions: string[];\n  open: boolean;\n  allowExpandReactions: boolean;\n  hiddenEmojis: string[];\n};\n\nexport type PreviewConfig = {\n  defaultEmoji: string;\n  defaultCaption: string;\n  showPreview: boolean;\n};\n\nconst basePreviewConfig: PreviewConfig = {\n  defaultEmoji: '1f60a',\n  defaultCaption: \"What's your mood?\",\n  showPreview: true,\n};\n\ntype ConfigExternal = {\n  previewConfig: Partial<PreviewConfig>;\n  onEmojiClick: MouseDownEvent;\n  onReactionClick: MouseDownEvent;\n  onSkinToneChange: OnSkinToneChange;\n} & Omit<PickerConfigInternal, 'previewConfig' | 'unicodeToHide'>;\n\nexport type PickerConfig = Partial<ConfigExternal>;\n\nexport type PickerDimensions = string | number;\n\nexport type MouseDownEvent = (\n  emoji: EmojiClickData,\n  event: MouseEvent,\n  api?: OnEmojiClickApi\n) => void;\nexport type OnSkinToneChange = (emoji: SkinTones) => void;\n\ntype OnEmojiClickApi = {\n  collapseToReactions: () => void;\n};\n", "import * as React from 'react';\n\nimport { compareConfig } from '../../config/compareConfig';\nimport {\n  basePickerConfig,\n  mergeConfig,\n  PickerConfig,\n  PickerConfigInternal\n} from '../../config/config';\n\ntype Props = PickerConfig &\n  Readonly<{\n    children: React.ReactNode;\n  }>;\n\nconst ConfigContext = React.createContext<PickerConfigInternal>(\n  basePickerConfig()\n);\n\nexport function PickerConfigProvider({ children, ...config }: Props) {\n  const mergedConfig = useSetConfig(config);\n\n  return (\n    <ConfigContext.Provider value={mergedConfig}>\n      {children}\n    </ConfigContext.Provider>\n  );\n}\n\nexport function useSetConfig(config: PickerConfig) {\n  const [mergedConfig, setMergedConfig] = React.useState(() =>\n    mergeConfig(config)\n  );\n\n  React.useEffect(() => {\n    if (compareConfig(mergedConfig, config)) {\n      return;\n    }\n    setMergedConfig(mergeConfig(config));\n    // not gonna...\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n    config.customEmojis?.length,\n    config.open,\n    config.emojiVersion,\n    config.reactionsDefaultOpen,\n    config.searchPlaceHolder,\n    config.searchPlaceholder,\n    config.defaultSkinTone,\n    config.skinTonesDisabled,\n    config.autoFocusSearch,\n    config.emojiStyle,\n    config.theme,\n    config.suggestedEmojisMode,\n    config.lazyLoadEmojis,\n    config.className,\n    config.height,\n    config.width,\n    config.searchDisabled,\n    config.skinTonePickerLocation,\n    config.allowExpandReactions\n  ]);\n\n  return mergedConfig;\n}\n\nexport function usePickerConfig() {\n  return React.useContext(ConfigContext);\n}\n", "import { useRef, useState } from 'react';\n\nexport function useDebouncedState<T>(\n  initialValue: T,\n  delay: number = 0\n): [T, (value: T) => Promise<T>] {\n  const [state, setState] = useState<T>(initialValue);\n  const timer = useRef<number | null>(null);\n\n  function debouncedSetState(value: T) {\n    return new Promise<T>(resolve => {\n      if (timer.current) {\n        clearTimeout(timer.current);\n      }\n\n      timer.current = window?.setTimeout(() => {\n        setState(value);\n        resolve(value);\n      }, delay);\n    });\n  }\n\n  return [state, debouncedSetState];\n}\n", "import { useUnicodeToHide } from \"../config/useConfig\";\n\nexport function useIsUnicodeHidden() {\n    const unicodeToHide = useUnicodeToHide();\n    return (emojiUnified: string) => unicodeToHide.has(emojiUnified);\n  }\n", "import { useRef, useMemo } from 'react';\n\nimport { useEmojiVersionConfig } from '../config/useConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport {\n  addedIn,\n  allEmojis,\n  emojiUnified,\n  unifiedWithoutSkinTone\n} from '../dataUtils/emojiSelectors';\n\nimport { useIsUnicodeHidden } from './useHideEmojisByUniocode';\n\nexport function useDisallowedEmojis() {\n  const DisallowedEmojisRef = useRef<Record<string, boolean>>({});\n  const emojiVersionConfig = useEmojiVersionConfig();\n\n  return useMemo(() => {\n    const emojiVersion = parseFloat(`${emojiVersionConfig}`);\n\n    if (!emojiVersionConfig || Number.isNaN(emojiVersion)) {\n      return DisallowedEmojisRef.current;\n    }\n\n    return allEmojis.reduce((disallowedEmojis, emoji) => {\n      if (addedInNewerVersion(emoji, emojiVersion)) {\n        disallowedEmojis[emojiUnified(emoji)] = true;\n      }\n\n      return disallowedEmojis;\n    }, DisallowedEmojisRef.current);\n  }, [emojiVersionConfig]);\n}\n\nexport function useIsEmojiDisallowed() {\n  const disallowedEmojis = useDisallowedEmojis();\n  const isUnicodeHidden = useIsUnicodeHidden();\n\n  return function isEmojiDisallowed(emoji: DataEmoji) {\n    const unified = unifiedWithoutSkinTone(emojiUnified(emoji));\n\n    return Boolean(disallowedEmojis[unified] || isUnicodeHidden(unified));\n  };\n}\n\nfunction addedInNewerVersion(\n  emoji: DataEmoji,\n  supportedLevel: number\n): boolean {\n  return addedIn(emoji) > supportedLevel;\n}\n", "import { useEffect } from 'react';\nimport * as React from 'react';\n\nexport function useMarkInitialLoad(\n  dispatch: React.Dispatch<React.SetStateAction<boolean>>\n) {\n  useEffect(() => {\n    dispatch(true);\n  }, [dispatch]);\n}\n", "import * as React from 'react';\nimport { useState } from 'react';\n\nimport {\n  useDefaultSkinToneConfig,\n  useReactionsOpenConfig\n} from '../../config/useConfig';\nimport { DataEmoji } from '../../dataUtils/DataTypes';\nimport { alphaNumericEmojiIndex } from '../../dataUtils/alphaNumericEmojiIndex';\nimport { useDebouncedState } from '../../hooks/useDebouncedState';\nimport { useDisallowedEmojis } from '../../hooks/useDisallowedEmojis';\nimport { FilterDict } from '../../hooks/useFilter';\nimport { useMarkInitialLoad } from '../../hooks/useInitialLoad';\nimport { SkinTones } from '../../types/exposedTypes';\n\nexport function PickerContextProvider({ children }: Props) {\n  const disallowedEmojis = useDisallowedEmojis();\n  const defaultSkinTone = useDefaultSkinToneConfig();\n  const reactionsDefaultOpen = useReactionsOpenConfig();\n\n  // Initialize the filter with the inititial dictionary\n  const filterRef = React.useRef<FilterState>(alphaNumericEmojiIndex);\n  const disallowClickRef = React.useRef<boolean>(false);\n  const disallowMouseRef = React.useRef<boolean>(false);\n  const disallowedEmojisRef = React.useRef<Record<string, boolean>>(\n    disallowedEmojis\n  );\n\n  const suggestedUpdateState = useDebouncedState(Date.now(), 200);\n  const searchTerm = useDebouncedState('', 100);\n  const skinToneFanOpenState = useState<boolean>(false);\n  const activeSkinTone = useState<SkinTones>(defaultSkinTone);\n  const activeCategoryState = useState<ActiveCategoryState>(null);\n  const emojisThatFailedToLoadState = useState<Set<string>>(new Set());\n  const emojiVariationPickerState = useState<DataEmoji | null>(null);\n  const reactionsModeState = useState(reactionsDefaultOpen);\n  const [isPastInitialLoad, setIsPastInitialLoad] = useState(false);\n\n  useMarkInitialLoad(setIsPastInitialLoad);\n\n  return (\n    <PickerContext.Provider\n      value={{\n        activeCategoryState,\n        activeSkinTone,\n        disallowClickRef,\n        disallowMouseRef,\n        disallowedEmojisRef,\n        emojiVariationPickerState,\n        emojisThatFailedToLoadState,\n        filterRef,\n        isPastInitialLoad,\n        searchTerm,\n        skinToneFanOpenState,\n        suggestedUpdateState,\n        reactionsModeState\n      }}\n    >\n      {children}\n    </PickerContext.Provider>\n  );\n}\n\ntype ReactState<T> = [T, React.Dispatch<React.SetStateAction<T>>];\n\nconst PickerContext = React.createContext<{\n  searchTerm: [string, (term: string) => Promise<string>];\n  suggestedUpdateState: [number, (term: number) => void];\n  activeCategoryState: ReactState<ActiveCategoryState>;\n  activeSkinTone: ReactState<SkinTones>;\n  emojisThatFailedToLoadState: ReactState<Set<string>>;\n  isPastInitialLoad: boolean;\n  emojiVariationPickerState: ReactState<DataEmoji | null>;\n  skinToneFanOpenState: ReactState<boolean>;\n  filterRef: React.MutableRefObject<FilterState>;\n  disallowClickRef: React.MutableRefObject<boolean>;\n  disallowMouseRef: React.MutableRefObject<boolean>;\n  disallowedEmojisRef: React.MutableRefObject<Record<string, boolean>>;\n  reactionsModeState: ReactState<boolean>;\n}>({\n  activeCategoryState: [null, () => {}],\n  activeSkinTone: [SkinTones.NEUTRAL, () => {}],\n  disallowClickRef: { current: false },\n  disallowMouseRef: { current: false },\n  disallowedEmojisRef: { current: {} },\n  emojiVariationPickerState: [null, () => {}],\n  emojisThatFailedToLoadState: [new Set(), () => {}],\n  filterRef: { current: {} },\n  isPastInitialLoad: true,\n  searchTerm: ['', () => new Promise<string>(() => undefined)],\n  skinToneFanOpenState: [false, () => {}],\n  suggestedUpdateState: [Date.now(), () => {}],\n  reactionsModeState: [false, () => {}]\n});\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n}>;\n\nexport function useFilterRef() {\n  const { filterRef } = React.useContext(PickerContext);\n  return filterRef;\n}\n\nexport function useDisallowClickRef() {\n  const { disallowClickRef } = React.useContext(PickerContext);\n  return disallowClickRef;\n}\n\nexport function useDisallowMouseRef() {\n  const { disallowMouseRef } = React.useContext(PickerContext);\n  return disallowMouseRef;\n}\n\nexport function useReactionsModeState() {\n  const { reactionsModeState } = React.useContext(PickerContext);\n  return reactionsModeState;\n}\n\nexport function useSearchTermState() {\n  const { searchTerm } = React.useContext(PickerContext);\n  return searchTerm;\n}\n\nexport function useActiveSkinToneState(): [\n  SkinTones,\n  (skinTone: SkinTones) => void\n] {\n  const { activeSkinTone } = React.useContext(PickerContext);\n  return activeSkinTone;\n}\n\nexport function useEmojisThatFailedToLoadState() {\n  const { emojisThatFailedToLoadState } = React.useContext(PickerContext);\n  return emojisThatFailedToLoadState;\n}\n\nexport function useIsPastInitialLoad(): boolean {\n  const { isPastInitialLoad } = React.useContext(PickerContext);\n  return isPastInitialLoad;\n}\n\nexport function useEmojiVariationPickerState() {\n  const { emojiVariationPickerState } = React.useContext(PickerContext);\n  return emojiVariationPickerState;\n}\n\nexport function useSkinToneFanOpenState() {\n  const { skinToneFanOpenState } = React.useContext(PickerContext);\n  return skinToneFanOpenState;\n}\n\nexport function useDisallowedEmojisRef() {\n  const { disallowedEmojisRef } = React.useContext(PickerContext);\n  return disallowedEmojisRef;\n}\n\nexport function useUpdateSuggested(): [number, () => void] {\n  const { suggestedUpdateState } = React.useContext(PickerContext);\n\n  const [suggestedUpdated, setsuggestedUpdate] = suggestedUpdateState;\n  return [\n    suggestedUpdated,\n    function updateSuggested() {\n      setsuggestedUpdate(Date.now());\n    }\n  ];\n}\n\nexport type FilterState = Record<string, FilterDict>;\n\ntype ActiveCategoryState = null | string;\n", "import React from 'react';\n\nimport { MouseDownEvent, OnSkinToneChange } from './config';\n\nexport type MutableConfig = {\n  onEmojiClick?: MouseDownEvent;\n  onReactionClick?: MouseDownEvent;\n  onSkinToneChange?: OnSkinToneChange;\n};\n\nexport const MutableConfigContext = React.createContext<\n  React.MutableRefObject<MutableConfig>\n>({} as React.MutableRefObject<MutableConfig>);\n\nexport function useMutableConfig(): React.MutableRefObject<MutableConfig> {\n  const mutableConfig = React.useContext(MutableConfigContext);\n  return mutableConfig;\n}\n\nexport function useDefineMutableConfig(\n  config: MutableConfig\n): React.MutableRefObject<MutableConfig> {\n  const MutableConfigRef = React.useRef<MutableConfig>({\n    onEmojiClick: config.onEmojiClick || emptyFunc,\n    onReactionClick: config.onReactionClick || config.onEmojiClick,\n    onSkinToneChange: config.onSkinToneChange || emptyFunc\n  });\n\n  React.useEffect(() => {\n    MutableConfigRef.current.onEmojiClick = config.onEmojiClick || emptyFunc;\n    MutableConfigRef.current.onReactionClick =\n      config.onReactionClick || config.onEmojiClick;\n  }, [config.onEmojiClick, config.onReactionClick]);\n\n  React.useEffect(() => {\n    MutableConfigRef.current.onSkinToneChange =\n      config.onSkinToneChange || emptyFunc;\n  }, [config.onSkinToneChange]);\n\n  return MutableConfigRef;\n}\n\nfunction emptyFunc() {}\n", "import * as React from 'react';\n\nimport { usePickerConfig } from '../components/context/PickerConfigContext';\nimport { useReactionsModeState } from '../components/context/PickerContext';\nimport {\n  EmojiClickData,\n  EmojiStyle,\n  SkinTonePickerLocation,\n  SkinTones,\n  SuggestionMode,\n  Theme\n} from '../types/exposedTypes';\n\nimport { CategoriesConfig } from './categoryConfig';\nimport {\n  DEFAULT_SEARCH_PLACEHOLDER,\n  SEARCH_RESULTS_NO_RESULTS_FOUND,\n  SEARCH_RESULTS_ONE_RESULT_FOUND,\n  SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND,\n  PickerDimensions,\n  PreviewConfig\n} from './config';\nimport { CustomEmoji } from './customEmojiConfig';\nimport { useMutableConfig } from './mutableConfig';\n\nexport enum MOUSE_EVENT_SOURCE {\n  REACTIONS = 'reactions',\n  PICKER = 'picker'\n}\n\nexport function useSearchPlaceHolderConfig(): string {\n  const { searchPlaceHolder, searchPlaceholder } = usePickerConfig();\n  return (\n    [searchPlaceHolder, searchPlaceholder].find(\n      p => p !== DEFAULT_SEARCH_PLACEHOLDER\n    ) ?? DEFAULT_SEARCH_PLACEHOLDER\n  );\n}\n\nexport function useDefaultSkinToneConfig(): SkinTones {\n  const { defaultSkinTone } = usePickerConfig();\n  return defaultSkinTone;\n}\n\nexport function useAllowExpandReactions(): boolean {\n  const { allowExpandReactions } = usePickerConfig();\n  return allowExpandReactions;\n}\n\nexport function useSkinTonesDisabledConfig(): boolean {\n  const { skinTonesDisabled } = usePickerConfig();\n  return skinTonesDisabled;\n}\n\nexport function useEmojiStyleConfig(): EmojiStyle {\n  const { emojiStyle } = usePickerConfig();\n  return emojiStyle;\n}\n\nexport function useAutoFocusSearchConfig(): boolean {\n  const { autoFocusSearch } = usePickerConfig();\n  return autoFocusSearch;\n}\n\nexport function useCategoriesConfig(): CategoriesConfig {\n  const { categories } = usePickerConfig();\n  return categories;\n}\n\nexport function useCustomEmojisConfig(): CustomEmoji[] {\n  const { customEmojis } = usePickerConfig();\n  return customEmojis;\n}\n\nexport function useOpenConfig(): boolean {\n  const { open } = usePickerConfig();\n  return open;\n}\n\nexport function useOnEmojiClickConfig(\n  mouseEventSource: MOUSE_EVENT_SOURCE\n): (emoji: EmojiClickData, event: MouseEvent) => void {\n  const { current } = useMutableConfig();\n  const [, setReactionsOpen] = useReactionsModeState();\n\n  const handler = current.onEmojiClick || (() => {});\n  const { onReactionClick } = current;\n\n  if (mouseEventSource === MOUSE_EVENT_SOURCE.REACTIONS && onReactionClick) {\n    return (...args) =>\n      onReactionClick(...args, {\n        collapseToReactions: () => {\n          setReactionsOpen(o => o);\n        }\n      });\n  }\n\n  return (...args) => {\n    handler(...args, {\n      collapseToReactions: () => {\n        setReactionsOpen(true);\n      }\n    });\n  };\n}\n\nexport function useOnSkinToneChangeConfig(): (skinTone: SkinTones) => void {\n  const { current } = useMutableConfig();\n\n  return current.onSkinToneChange || (() => {});\n}\n\nexport function usePreviewConfig(): PreviewConfig {\n  const { previewConfig } = usePickerConfig();\n  return previewConfig;\n}\n\nexport function useThemeConfig(): Theme {\n  const { theme } = usePickerConfig();\n\n  return theme;\n}\n\nexport function useSuggestedEmojisModeConfig(): SuggestionMode {\n  const { suggestedEmojisMode } = usePickerConfig();\n  return suggestedEmojisMode;\n}\n\nexport function useLazyLoadEmojisConfig(): boolean {\n  const { lazyLoadEmojis } = usePickerConfig();\n  return lazyLoadEmojis;\n}\n\nexport function useClassNameConfig(): string {\n  const { className } = usePickerConfig();\n  return className;\n}\n\nexport function useStyleConfig(): React.CSSProperties {\n  const { height, width, style } = usePickerConfig();\n  return { height: getDimension(height), width: getDimension(width), ...style };\n}\n\nexport function useReactionsOpenConfig(): boolean {\n  const { reactionsDefaultOpen } = usePickerConfig();\n  return reactionsDefaultOpen;\n}\n\nexport function useEmojiVersionConfig(): string | null {\n  const { emojiVersion } = usePickerConfig();\n  return emojiVersion;\n}\n\nexport function useSearchDisabledConfig(): boolean {\n  const { searchDisabled } = usePickerConfig();\n  return searchDisabled;\n}\n\nexport function useSkinTonePickerLocationConfig(): SkinTonePickerLocation {\n  const { skinTonePickerLocation } = usePickerConfig();\n  return skinTonePickerLocation;\n}\n\nexport function useUnicodeToHide() {\n  const { unicodeToHide } = usePickerConfig();\n  return unicodeToHide;\n}\n\nexport function useReactionsConfig(): string[] {\n  const { reactions } = usePickerConfig();\n  return reactions;\n}\n\nexport function useGetEmojiUrlConfig(): (\n  unified: string,\n  style: EmojiStyle\n) => string {\n  const { getEmojiUrl } = usePickerConfig();\n  return getEmojiUrl;\n}\n\nfunction getDimension(dimensionConfig: PickerDimensions): PickerDimensions {\n  return typeof dimensionConfig === 'number'\n    ? `${dimensionConfig}px`\n    : dimensionConfig;\n}\n\nexport function useSearchResultsConfig(searchResultsCount: number): string {\n  const hasResults = searchResultsCount > 0;\n  const isPlural = searchResultsCount > 1;\n\n  if (hasResults) {\n    return isPlural\n      ? SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND.replace(\n          '%n',\n          searchResultsCount.toString()\n        )\n      : SEARCH_RESULTS_ONE_RESULT_FOUND;\n  }\n\n  return SEARCH_RESULTS_NO_RESULTS_FOUND;\n}\n", "import { useSearchTermState } from '../components/context/PickerContext';\n\nexport default function useIsSearchMode(): boolean {\n  const [searchTerm] = useSearchTermState();\n\n  return !!searchTerm;\n}\n", "import { NullableElement } from './selectors';\n\nexport function focusElement(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  requestAnimationFrame(() => {\n    element.focus();\n  });\n}\n\nexport function focusPrevElementSibling(element: NullableElement) {\n  if (!element) return;\n\n  const prev = element.previousElementSibling as HTMLElement;\n\n  focusElement(prev);\n}\n\nexport function focusNextElementSibling(element: NullableElement) {\n  if (!element) return;\n\n  const next = element.nextElementSibling as HTMLElement;\n\n  focusElement(next);\n}\n\nexport function focusFirstElementChild(element: NullableElement) {\n  if (!element) return;\n\n  const first = element.firstElementChild as HTMLElement;\n\n  focusElement(first);\n}\n", "import { NullableElement } from './selectors';\n\nexport function getActiveElement() {\n  return document.activeElement as NullableElement;\n}\n", "import * as React from 'react';\n\nimport { focusElement } from '../../DomUtils/focusElement';\nimport { NullableElement } from '../../DomUtils/selectors';\n\nexport function ElementRefContextProvider({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  const PickerMainRef = React.useRef<HTMLElement>(null);\n  const AnchoredEmojiRef = React.useRef<HTMLElement>(null);\n  const BodyRef = React.useRef<HTMLDivElement>(null);\n  const SearchInputRef = React.useRef<HTMLInputElement>(null);\n  const SkinTonePickerRef = React.useRef<HTMLDivElement>(null);\n  const CategoryNavigationRef = React.useRef<HTMLDivElement>(null);\n  const VariationPickerRef = React.useRef<HTMLDivElement>(null);\n  const ReactionsRef = React.useRef<HTMLUListElement>(null);\n\n  return (\n    <ElementRefContext.Provider\n      value={{\n        AnchoredEmojiRef,\n        BodyRef,\n        CategoryNavigationRef,\n        PickerMainRef,\n        SearchInputRef,\n        SkinTonePickerRef,\n        VariationPickerRef,\n        ReactionsRef\n      }}\n    >\n      {children}\n    </ElementRefContext.Provider>\n  );\n}\n\nexport type ElementRef<\n  E extends HTMLElement = HTMLElement\n> = React.MutableRefObject<E | null>;\n\ntype ElementRefs = {\n  PickerMainRef: ElementRef;\n  AnchoredEmojiRef: ElementRef;\n  SkinTonePickerRef: ElementRef<HTMLDivElement>;\n  SearchInputRef: ElementRef<HTMLInputElement>;\n  BodyRef: ElementRef<HTMLDivElement>;\n  CategoryNavigationRef: ElementRef<HTMLDivElement>;\n  VariationPickerRef: ElementRef<HTMLDivElement>;\n  ReactionsRef: ElementRef<HTMLUListElement>;\n};\n\nconst ElementRefContext = React.createContext<ElementRefs>({\n  AnchoredEmojiRef: React.createRef(),\n  BodyRef: React.createRef(),\n  CategoryNavigationRef: React.createRef(),\n  PickerMainRef: React.createRef(),\n  SearchInputRef: React.createRef(),\n  SkinTonePickerRef: React.createRef(),\n  VariationPickerRef: React.createRef(),\n  ReactionsRef: React.createRef()\n});\n\nfunction useElementRef() {\n  return React.useContext(ElementRefContext);\n}\n\nexport function usePickerMainRef() {\n  return useElementRef()['PickerMainRef'];\n}\n\nexport function useAnchoredEmojiRef() {\n  return useElementRef()['AnchoredEmojiRef'];\n}\n\nexport function useSetAnchoredEmojiRef(): (target: NullableElement) => void {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  return (target: NullableElement) => {\n    if (target === null && AnchoredEmojiRef.current !== null) {\n      focusElement(AnchoredEmojiRef.current);\n    }\n\n    AnchoredEmojiRef.current = target;\n  };\n}\n\nexport function useBodyRef() {\n  return useElementRef()['BodyRef'];\n}\n\nexport function useReactionsRef() {\n  return useElementRef()['ReactionsRef'];\n}\n\nexport function useSearchInputRef() {\n  return useElementRef()['SearchInputRef'];\n}\n\nexport function useSkinTonePickerRef() {\n  return useElementRef()['SkinTonePickerRef'];\n}\n\nexport function useCategoryNavigationRef() {\n  return useElementRef()['CategoryNavigationRef'];\n}\n\nexport function useVariationPickerRef() {\n  return useElementRef()['VariationPickerRef'];\n}\n", "import { useCallback } from 'react';\n\nimport { useBodyRef } from '../components/context/ElementRefContext';\n\nimport { asSelectors, ClassNames } from './classNames';\nimport {\n  categoryLabelHeight,\n  closestCategory,\n  closestScrollBody,\n  emojiDistanceFromScrollTop,\n  isEmojiBehindLabel,\n  NullableElement,\n  queryScrollBody\n} from './selectors';\n\nexport function scrollTo(root: NullableElement, top: number = 0) {\n  const $eprBody = queryScrollBody(root);\n\n  if (!$eprBody) {\n    return;\n  }\n\n  requestAnimationFrame(() => {\n    $eprBody.scrollTop = top;\n  });\n}\n\nexport function scrollBy(root: NullableElement, by: number): void {\n  const $eprBody = queryScrollBody(root);\n\n  if (!$eprBody) {\n    return;\n  }\n\n  requestAnimationFrame(() => {\n    $eprBody.scrollTop = $eprBody.scrollTop + by;\n  });\n}\n\nexport function useScrollTo() {\n  const BodyRef = useBodyRef();\n\n  return useCallback(\n    (top: number) => {\n      requestAnimationFrame(() => {\n        if (BodyRef.current) {\n          BodyRef.current.scrollTop = top;\n        }\n      });\n    },\n    [BodyRef]\n  );\n}\n\nexport function scrollEmojiAboveLabel(emoji: NullableElement) {\n  if (!emoji || !isEmojiBehindLabel(emoji)) {\n    return;\n  }\n\n  if (emoji.closest(asSelectors(ClassNames.variationPicker))) {\n    return;\n  }\n\n  const scrollBody = closestScrollBody(emoji);\n  const by = emojiDistanceFromScrollTop(emoji);\n  scrollBy(scrollBody, -(categoryLabelHeight(closestCategory(emoji)) - by));\n}\n", "import {\n  elementCountInRow,\n  elementIndexInRow,\n  getElementInNextRow,\n  getElementInPrevRow,\n  getElementInRow,\n  hasNextRow,\n  rowNumber\n} from './elementPositionInRow';\nimport { focusElement } from './focusElement';\nimport { scrollEmojiAboveLabel } from './scrollTo';\nimport {\n  allVisibleEmojis,\n  closestCategory,\n  firstVisibleEmoji,\n  lastVisibleEmoji,\n  nextCategory,\n  nextVisibleEmoji,\n  NullableElement,\n  prevCategory,\n  prevVisibleEmoji,\n  closestCategoryContent\n} from './selectors';\n\nexport function focusFirstVisibleEmoji(parent: NullableElement) {\n  const emoji = firstVisibleEmoji(parent);\n  focusElement(emoji);\n  scrollEmojiAboveLabel(emoji);\n}\n\nexport function focusAndClickFirstVisibleEmoji(parent: NullableElement) {\n  const firstEmoji = firstVisibleEmoji(parent);\n\n  focusElement(firstEmoji);\n  firstEmoji?.click();\n}\n\nexport function focusLastVisibleEmoji(parent: NullableElement) {\n  focusElement(lastVisibleEmoji(parent));\n}\n\nexport function focusNextVisibleEmoji(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  const next = nextVisibleEmoji(element);\n\n  if (!next) {\n    return focusFirstVisibleEmoji(nextCategory(element));\n  }\n\n  focusElement(next);\n  scrollEmojiAboveLabel(next);\n}\n\nexport function focusPrevVisibleEmoji(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  const prev = prevVisibleEmoji(element);\n\n  if (!prev) {\n    return focusLastVisibleEmoji(prevCategory(element));\n  }\n\n  focusElement(prev);\n  scrollEmojiAboveLabel(prev);\n}\n\nexport function focusVisibleEmojiOneRowUp(\n  element: NullableElement,\n  exitUp: () => void\n) {\n  if (!element) {\n    return;\n  }\n\n  const prev = visibleEmojiOneRowUp(element);\n\n  if (!prev) {\n    return exitUp();\n  }\n\n  focusElement(prev);\n  scrollEmojiAboveLabel(prev);\n}\n\nexport function focusVisibleEmojiOneRowDown(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  const next = visibleEmojiOneRowDown(element);\n\n  return focusElement(next);\n}\n\nfunction visibleEmojiOneRowUp(element: HTMLElement) {\n  if (!element) {\n    return null;\n  }\n\n  const categoryContent = closestCategoryContent(element);\n  const category = closestCategory(categoryContent);\n  const indexInRow = elementIndexInRow(categoryContent, element);\n  const row = rowNumber(categoryContent, element);\n  const countInRow = elementCountInRow(categoryContent, element);\n\n  if (row === 0) {\n    const prevVisibleCategory = prevCategory(category);\n\n    if (!prevVisibleCategory) {\n      return null;\n    }\n\n    return getElementInRow(\n      allVisibleEmojis(prevVisibleCategory),\n      -1, // last row\n      countInRow,\n      indexInRow\n    );\n  }\n\n  return getElementInPrevRow(\n    allVisibleEmojis(categoryContent),\n    row,\n    countInRow,\n    indexInRow\n  );\n}\n\nfunction visibleEmojiOneRowDown(element: HTMLElement) {\n  if (!element) {\n    return null;\n  }\n\n  const categoryContent = closestCategoryContent(element);\n  const category = closestCategory(categoryContent);\n  const indexInRow = elementIndexInRow(categoryContent, element);\n  const row = rowNumber(categoryContent, element);\n  const countInRow = elementCountInRow(categoryContent, element);\n  if (!hasNextRow(categoryContent, element)) {\n    const nextVisibleCategory = nextCategory(category);\n\n    if (!nextVisibleCategory) {\n      return null;\n    }\n\n    return getElementInRow(\n      allVisibleEmojis(nextVisibleCategory),\n      0,\n      countInRow,\n      indexInRow\n    );\n  }\n\n  const itemInNextRow = getElementInNextRow(\n    allVisibleEmojis(categoryContent),\n    row,\n    countInRow,\n    indexInRow\n  );\n\n  return itemInNextRow;\n}\n", "import { useCallback } from 'react';\n\nimport {\n  useEmojiVariationPickerState,\n  useSkinToneFanOpenState\n} from '../components/context/PickerContext';\n\nexport function useCloseAllOpenToggles() {\n  const [variationPicker, setVariationPicker] = useEmojiVariationPickerState();\n  const [skinToneFanOpen, setSkinToneFanOpen] = useSkinToneFanOpenState();\n\n  const closeAllOpenToggles = useCallback(() => {\n    if (variationPicker) {\n      setVariationPicker(null);\n    }\n\n    if (skinToneFanOpen) {\n      setSkinToneFanOpen(false);\n    }\n  }, [\n    variationPicker,\n    skinToneFanOpen,\n    setVariationPicker,\n    setSkinToneFanOpen\n  ]);\n\n  return closeAllOpenToggles;\n}\n\nexport function useHasOpenToggles() {\n  const [variationPicker] = useEmojiVariationPickerState();\n  const [skinToneFanOpen] = useSkinToneFanOpenState();\n\n  return function hasOpenToggles() {\n    return !!variationPicker || skinToneFanOpen;\n  };\n}\n", "import { useEffect } from 'react';\n\nimport { useBodyRef } from '../components/context/ElementRefContext';\nimport { useDisallowMouseRef } from '../components/context/PickerContext';\n\nexport function useDisallowMouseMove() {\n  const DisallowMouseRef = useDisallowMouseRef();\n  return function disallowMouseMove() {\n    DisallowMouseRef.current = true;\n  };\n}\n\nexport function useAllowMouseMove() {\n  const DisallowMouseRef = useDisallowMouseRef();\n  return function allowMouseMove() {\n    DisallowMouseRef.current = false;\n  };\n}\n\nexport function useIsMouseDisallowed() {\n  const DisallowMouseRef = useDisallowMouseRef();\n  return function isMouseDisallowed() {\n    return DisallowMouseRef.current;\n  };\n}\n\nexport function useOnMouseMove() {\n  const BodyRef = useBodyRef();\n  const allowMouseMove = useAllowMouseMove();\n  const isMouseDisallowed = useIsMouseDisallowed();\n\n  useEffect(() => {\n    const bodyRef = BodyRef.current;\n    bodyRef?.addEventListener('mousemove', onMouseMove, {\n      passive: true\n    });\n\n    function onMouseMove() {\n      if (isMouseDisallowed()) {\n        allowMouseMove();\n      }\n    }\n    return () => {\n      bodyRef?.removeEventListener('mousemove', onMouseMove);\n    };\n  }, [BodyRef, allowMouseMove, isMouseDisallowed]);\n}\n", "import { useCallback } from 'react';\n\nimport { focusElement, focusFirstElementChild } from '../DomUtils/focusElement';\nimport {\n  useCategoryNavigationRef,\n  useSearchInputRef,\n  useSkinTonePickerRef\n} from '../components/context/ElementRefContext';\n\nexport function useFocusSearchInput() {\n  const SearchInputRef = useSearchInputRef();\n\n  return useCallback(() => {\n    focusElement(SearchInputRef.current);\n  }, [SearchInputRef]);\n}\n\nexport function useFocusSkinTonePicker() {\n  const SkinTonePickerRef = useSkinTonePickerRef();\n\n  return useCallback(() => {\n    if (!SkinTonePickerRef.current) {\n      return;\n    }\n\n    focusFirstElementChild(SkinTonePickerRef.current);\n  }, [SkinTonePickerRef]);\n}\n\nexport function useFocusCategoryNavigation() {\n  const CategoryNavigationRef = useCategoryNavigationRef();\n\n  return useCallback(() => {\n    if (!CategoryNavigationRef.current) {\n      return;\n    }\n\n    focusFirstElementChild(CategoryNavigationRef.current);\n  }, [CategoryNavigationRef]);\n}\n", "import { scrollTo } from '../DomUtils/scrollTo';\nimport {\n  usePickerMainRef,\n  useSearchInputRef\n} from '../components/context/ElementRefContext';\nimport {\n  FilterState,\n  useFilterRef,\n  useSearchTermState\n} from '../components/context/PickerContext';\nimport { useSearchResultsConfig } from '../config/useConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport { emojiNames } from '../dataUtils/emojiSelectors';\n\nimport { useFocusSearchInput } from './useFocus';\n\nfunction useSetFilterRef() {\n  const filterRef = useFilterRef();\n\n  return function setFilter(\n    setter: FilterState | ((current: FilterState) => FilterState)\n  ): void {\n    if (typeof setter === 'function') {\n      return setFilter(setter(filterRef.current));\n    }\n\n    filterRef.current = setter;\n  };\n}\n\nexport function useClearSearch() {\n  const applySearch = useApplySearch();\n  const SearchInputRef = useSearchInputRef();\n  const focusSearchInput = useFocusSearchInput();\n\n  return function clearSearch() {\n    if (SearchInputRef.current) {\n      SearchInputRef.current.value = '';\n    }\n\n    applySearch('');\n    focusSearchInput();\n  };\n}\n\nexport function useAppendSearch() {\n  const SearchInputRef = useSearchInputRef();\n  const applySearch = useApplySearch();\n\n  return function appendSearch(str: string) {\n    if (SearchInputRef.current) {\n      SearchInputRef.current.value = `${SearchInputRef.current.value}${str}`;\n      applySearch(getNormalizedSearchTerm(SearchInputRef.current.value));\n    } else {\n      applySearch(getNormalizedSearchTerm(str));\n    }\n  };\n}\n\nexport function useFilter() {\n  const SearchInputRef = useSearchInputRef();\n  const filterRef = useFilterRef();\n  const setFilterRef = useSetFilterRef();\n  const applySearch = useApplySearch();\n\n  const [searchTerm] = useSearchTermState();\n  const statusSearchResults = getStatusSearchResults(\n    filterRef.current,\n    searchTerm\n  );\n\n  return {\n    onChange,\n    searchTerm,\n    SearchInputRef,\n    statusSearchResults\n  };\n\n  function onChange(inputValue: string) {\n    const filter = filterRef.current;\n\n    const nextValue = inputValue.toLowerCase();\n\n    if (filter?.[nextValue] || nextValue.length <= 1) {\n      return applySearch(nextValue);\n    }\n\n    const longestMatch = findLongestMatch(nextValue, filter);\n\n    if (!longestMatch) {\n      // Can we even get here?\n      // If so, we need to search among all emojis\n      return applySearch(nextValue);\n    }\n\n    setFilterRef(current =>\n      Object.assign(current, {\n        [nextValue]: filterEmojiObjectByKeyword(longestMatch, nextValue)\n      })\n    );\n    applySearch(nextValue);\n  }\n}\n\nfunction useApplySearch() {\n  const [, setSearchTerm] = useSearchTermState();\n  const PickerMainRef = usePickerMainRef();\n\n  return function applySearch(searchTerm: string) {\n    requestAnimationFrame(() => {\n      setSearchTerm(searchTerm ? searchTerm?.toLowerCase() : searchTerm).then(\n        () => {\n          scrollTo(PickerMainRef.current, 0);\n        }\n      );\n    });\n  };\n}\n\nfunction filterEmojiObjectByKeyword(\n  emojis: FilterDict,\n  keyword: string\n): FilterDict {\n  const filtered: FilterDict = {};\n\n  for (const unified in emojis) {\n    const emoji = emojis[unified];\n\n    if (hasMatch(emoji, keyword)) {\n      filtered[unified] = emoji;\n    }\n  }\n\n  return filtered;\n}\n\nfunction hasMatch(emoji: DataEmoji, keyword: string): boolean {\n  return emojiNames(emoji).some(name => name.includes(keyword));\n}\n\nexport function useIsEmojiFiltered(): (unified: string) => boolean {\n  const { current: filter } = useFilterRef();\n  const [searchTerm] = useSearchTermState();\n\n  return unified => isEmojiFilteredBySearchTerm(unified, filter, searchTerm);\n}\n\nfunction isEmojiFilteredBySearchTerm(\n  unified: string,\n  filter: FilterState,\n  searchTerm: string\n): boolean {\n  if (!filter || !searchTerm) {\n    return false;\n  }\n\n  return !filter[searchTerm]?.[unified];\n}\n\nexport type FilterDict = Record<string, DataEmoji>;\n\nfunction findLongestMatch(\n  keyword: string,\n  dict: Record<string, FilterDict> | null\n): FilterDict | null {\n  if (!dict) {\n    return null;\n  }\n\n  if (dict[keyword]) {\n    return dict[keyword];\n  }\n\n  const longestMatchingKey = Object.keys(dict)\n    .sort((a, b) => b.length - a.length)\n    .find(key => keyword.includes(key));\n\n  if (longestMatchingKey) {\n    return dict[longestMatchingKey];\n  }\n\n  return null;\n}\n\nexport function getNormalizedSearchTerm(str: string): string {\n  if (!str || typeof str !== 'string') {\n    return '';\n  }\n\n  return str.trim().toLowerCase();\n}\n\nfunction getStatusSearchResults(\n  filterState: FilterState,\n  searchTerm: string\n): string {\n  if (!filterState?.[searchTerm]) return '';\n\n  const searchResultsCount =\n    Object.entries(filterState?.[searchTerm])?.length || 0;\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useSearchResultsConfig(searchResultsCount);\n}\n", "import { emojiFromElement, NullableElement } from '../DomUtils/selectors';\nimport { useSetAnchoredEmojiRef } from '../components/context/ElementRefContext';\nimport { useEmojiVariationPickerState } from '../components/context/PickerContext';\n\nexport default function useSetVariationPicker() {\n  const setAnchoredEmojiRef = useSetAnchoredEmojiRef();\n  const [, setEmojiVariationPicker] = useEmojiVariationPickerState();\n\n  return function setVariationPicker(element: NullableElement) {\n    const [emoji] = emojiFromElement(element);\n\n    if (emoji) {\n      setAnchoredEmojiRef(element);\n      setEmojiVariationPicker(emoji);\n    }\n  };\n}\n", "import { useSkinTonePickerLocationConfig } from '../config/useConfig';\nimport { SkinTonePickerLocation } from '../types/exposedTypes';\n\nexport function useShouldShowSkinTonePicker() {\n  const skinTonePickerLocationConfig = useSkinTonePickerLocationConfig();\n\n  return function shouldShowSkinTonePicker(location: SkinTonePickerLocation) {\n    return skinTonePickerLocationConfig === location;\n  };\n}\n\nexport function useIsSkinToneInSearch() {\n  const skinTonePickerLocationConfig = useSkinTonePickerLocationConfig();\n\n  return skinTonePickerLocationConfig === SkinTonePickerLocation.SEARCH;\n}\n\nexport function useIsSkinToneInPreview() {\n  const skinTonePickerLocationConfig = useSkinTonePickerLocationConfig();\n\n  return skinTonePickerLocationConfig === SkinTonePickerLocation.PREVIEW;\n}\n", "import { useCallback, useEffect, useMemo } from 'react';\n\nimport { hasNextElementSibling } from '../DomUtils/elementPositionInRow';\nimport {\n  focusNextElementSibling,\n  focusPrevElementSibling\n} from '../DomUtils/focusElement';\nimport { getActiveElement } from '../DomUtils/getActiveElement';\nimport {\n  focusAndClickFirstVisibleEmoji,\n  focusFirstVisibleEmoji,\n  focusNextVisibleEmoji,\n  focusPrevVisibleEmoji,\n  focusVisibleEmojiOneRowDown,\n  focusVisibleEmojiOneRowUp\n} from '../DomUtils/keyboardNavigation';\nimport { useScrollTo } from '../DomUtils/scrollTo';\nimport { buttonFromTarget } from '../DomUtils/selectors';\nimport {\n  useBodyRef,\n  useCategoryNavigationRef,\n  usePickerMainRef,\n  useSearchInputRef,\n  useSkinTonePickerRef\n} from '../components/context/ElementRefContext';\nimport { useSkinToneFanOpenState } from '../components/context/PickerContext';\nimport { useSearchDisabledConfig } from '../config/useConfig';\n\nimport {\n  useCloseAllOpenToggles,\n  useHasOpenToggles\n} from './useCloseAllOpenToggles';\nimport { useDisallowMouseMove } from './useDisallowMouseMove';\nimport { useAppendSearch, useClearSearch } from './useFilter';\nimport {\n  useFocusCategoryNavigation,\n  useFocusSearchInput,\n  useFocusSkinTonePicker\n} from './useFocus';\nimport useIsSearchMode from './useIsSearchMode';\nimport useSetVariationPicker from './useSetVariationPicker';\nimport {\n  useIsSkinToneInPreview,\n  useIsSkinToneInSearch\n} from './useShouldShowSkinTonePicker';\n\nenum KeyboardEvents {\n  ArrowDown = 'ArrowDown',\n  ArrowUp = 'ArrowUp',\n  ArrowLeft = 'ArrowLeft',\n  ArrowRight = 'ArrowRight',\n  Escape = 'Escape',\n  Enter = 'Enter',\n  Space = ' '\n}\n\nexport function useKeyboardNavigation() {\n  usePickerMainKeyboardEvents();\n  useSearchInputKeyboardEvents();\n  useSkinTonePickerKeyboardEvents();\n  useCategoryNavigationKeyboardEvents();\n  useBodyKeyboardEvents();\n}\n\nfunction usePickerMainKeyboardEvents() {\n  const PickerMainRef = usePickerMainRef();\n  const clearSearch = useClearSearch();\n  const scrollTo = useScrollTo();\n  const SearchInputRef = useSearchInputRef();\n  const focusSearchInput = useFocusSearchInput();\n  const hasOpenToggles = useHasOpenToggles();\n  const disallowMouseMove = useDisallowMouseMove();\n\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  const onKeyDown = useMemo(\n    () =>\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        disallowMouseMove();\n        switch (key) {\n          // eslint-disable-next-line no-fallthrough\n          case KeyboardEvents.Escape:\n            event.preventDefault();\n            if (hasOpenToggles()) {\n              closeAllOpenToggles();\n              return;\n            }\n            clearSearch();\n            scrollTo(0);\n            focusSearchInput();\n            break;\n        }\n      },\n    [\n      scrollTo,\n      clearSearch,\n      closeAllOpenToggles,\n      focusSearchInput,\n      hasOpenToggles,\n      disallowMouseMove\n    ]\n  );\n\n  useEffect(() => {\n    const current = PickerMainRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [PickerMainRef, SearchInputRef, scrollTo, onKeyDown]);\n}\n\nfunction useSearchInputKeyboardEvents() {\n  const focusSkinTonePicker = useFocusSkinTonePicker();\n  const PickerMainRef = usePickerMainRef();\n  const BodyRef = useBodyRef();\n  const SearchInputRef = useSearchInputRef();\n  const [, setSkinToneFanOpenState] = useSkinToneFanOpenState();\n  const goDownFromSearchInput = useGoDownFromSearchInput();\n  const isSkinToneInSearch = useIsSkinToneInSearch();\n\n  const onKeyDown = useMemo(\n    () =>\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        switch (key) {\n          case KeyboardEvents.ArrowRight:\n            if (!isSkinToneInSearch) {\n              return;\n            }\n            event.preventDefault();\n            setSkinToneFanOpenState(true);\n            focusSkinTonePicker();\n            break;\n          case KeyboardEvents.ArrowDown:\n            event.preventDefault();\n            goDownFromSearchInput();\n            break;\n          case KeyboardEvents.Enter:\n            event.preventDefault();\n            focusAndClickFirstVisibleEmoji(BodyRef.current);\n            break;\n        }\n      },\n    [\n      focusSkinTonePicker,\n      goDownFromSearchInput,\n      setSkinToneFanOpenState,\n      BodyRef,\n      isSkinToneInSearch\n    ]\n  );\n\n  useEffect(() => {\n    const current = SearchInputRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [PickerMainRef, SearchInputRef, onKeyDown]);\n}\n\nfunction useSkinTonePickerKeyboardEvents() {\n  const SkinTonePickerRef = useSkinTonePickerRef();\n  const focusSearchInput = useFocusSearchInput();\n  const SearchInputRef = useSearchInputRef();\n  const goDownFromSearchInput = useGoDownFromSearchInput();\n  const [isOpen, setIsOpen] = useSkinToneFanOpenState();\n  const isSkinToneInPreview = useIsSkinToneInPreview();\n  const isSkinToneInSearch = useIsSkinToneInSearch();\n  const onType = useOnType();\n\n  const onKeyDown = useMemo(\n    () =>\n      // eslint-disable-next-line complexity\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        if (isSkinToneInSearch) {\n          switch (key) {\n            case KeyboardEvents.ArrowLeft:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusNextSkinTone(focusSearchInput);\n              break;\n            case KeyboardEvents.ArrowRight:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusPrevSkinTone();\n              break;\n            case KeyboardEvents.ArrowDown:\n              event.preventDefault();\n              if (isOpen) {\n                setIsOpen(false);\n              }\n              goDownFromSearchInput();\n              break;\n            default:\n              onType(event);\n              break;\n          }\n        }\n\n        if (isSkinToneInPreview) {\n          switch (key) {\n            case KeyboardEvents.ArrowUp:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusNextSkinTone(focusSearchInput);\n              break;\n            case KeyboardEvents.ArrowDown:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusPrevSkinTone();\n              break;\n            default:\n              onType(event);\n              break;\n          }\n        }\n      },\n    [\n      isOpen,\n      focusSearchInput,\n      setIsOpen,\n      goDownFromSearchInput,\n      onType,\n      isSkinToneInPreview,\n      isSkinToneInSearch\n    ]\n  );\n\n  useEffect(() => {\n    const current = SkinTonePickerRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [SkinTonePickerRef, SearchInputRef, isOpen, onKeyDown]);\n}\n\nfunction useCategoryNavigationKeyboardEvents() {\n  const focusSearchInput = useFocusSearchInput();\n  const CategoryNavigationRef = useCategoryNavigationRef();\n  const BodyRef = useBodyRef();\n  const onType = useOnType();\n\n  const onKeyDown = useMemo(\n    () =>\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        switch (key) {\n          case KeyboardEvents.ArrowUp:\n            event.preventDefault();\n            focusSearchInput();\n            break;\n          case KeyboardEvents.ArrowRight:\n            event.preventDefault();\n            focusNextElementSibling(getActiveElement());\n            break;\n          case KeyboardEvents.ArrowLeft:\n            event.preventDefault();\n            focusPrevElementSibling(getActiveElement());\n            break;\n          case KeyboardEvents.ArrowDown:\n            event.preventDefault();\n            focusFirstVisibleEmoji(BodyRef.current);\n            break;\n          default:\n            onType(event);\n            break;\n        }\n      },\n    [BodyRef, focusSearchInput, onType]\n  );\n\n  useEffect(() => {\n    const current = CategoryNavigationRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [CategoryNavigationRef, BodyRef, onKeyDown]);\n}\n\nfunction useBodyKeyboardEvents() {\n  const BodyRef = useBodyRef();\n  const goUpFromBody = useGoUpFromBody();\n  const setVariationPicker = useSetVariationPicker();\n  const hasOpenToggles = useHasOpenToggles();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  const onType = useOnType();\n\n  const onKeyDown = useMemo(\n    () =>\n      // eslint-disable-next-line complexity\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        const activeElement = buttonFromTarget(getActiveElement());\n\n        switch (key) {\n          case KeyboardEvents.ArrowRight:\n            event.preventDefault();\n            focusNextVisibleEmoji(activeElement);\n            break;\n          case KeyboardEvents.ArrowLeft:\n            event.preventDefault();\n            focusPrevVisibleEmoji(activeElement);\n            break;\n          case KeyboardEvents.ArrowDown:\n            event.preventDefault();\n            if (hasOpenToggles()) {\n              closeAllOpenToggles();\n              break;\n            }\n            focusVisibleEmojiOneRowDown(activeElement);\n            break;\n          case KeyboardEvents.ArrowUp:\n            event.preventDefault();\n            if (hasOpenToggles()) {\n              closeAllOpenToggles();\n              break;\n            }\n            focusVisibleEmojiOneRowUp(activeElement, goUpFromBody);\n            break;\n          case KeyboardEvents.Space:\n            event.preventDefault();\n            setVariationPicker(event.target as HTMLElement);\n            break;\n          default:\n            onType(event);\n            break;\n        }\n      },\n    [\n      goUpFromBody,\n      onType,\n      setVariationPicker,\n      hasOpenToggles,\n      closeAllOpenToggles\n    ]\n  );\n\n  useEffect(() => {\n    const current = BodyRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [BodyRef, onKeyDown]);\n}\n\nfunction useGoDownFromSearchInput() {\n  const focusCategoryNavigation = useFocusCategoryNavigation();\n  const isSearchMode = useIsSearchMode();\n  const BodyRef = useBodyRef();\n\n  return useCallback(\n    function goDownFromSearchInput() {\n      if (isSearchMode) {\n        return focusFirstVisibleEmoji(BodyRef.current);\n      }\n      return focusCategoryNavigation();\n    },\n    [BodyRef, focusCategoryNavigation, isSearchMode]\n  );\n}\n\nfunction useGoUpFromBody() {\n  const focusSearchInput = useFocusSearchInput();\n  const focusCategoryNavigation = useFocusCategoryNavigation();\n  const isSearchMode = useIsSearchMode();\n\n  return useCallback(\n    function goUpFromEmoji() {\n      if (isSearchMode) {\n        return focusSearchInput();\n      }\n      return focusCategoryNavigation();\n    },\n    [focusSearchInput, isSearchMode, focusCategoryNavigation]\n  );\n}\n\nfunction focusNextSkinTone(exitLeft: () => void) {\n  const currentSkinTone = getActiveElement();\n\n  if (!currentSkinTone) {\n    return;\n  }\n\n  if (!hasNextElementSibling(currentSkinTone)) {\n    exitLeft();\n  }\n\n  focusNextElementSibling(currentSkinTone);\n}\n\nfunction focusPrevSkinTone() {\n  const currentSkinTone = getActiveElement();\n\n  if (!currentSkinTone) {\n    return;\n  }\n\n  focusPrevElementSibling(currentSkinTone);\n}\n\nfunction useOnType() {\n  const appendSearch = useAppendSearch();\n  const focusSearchInput = useFocusSearchInput();\n  const searchDisabled = useSearchDisabledConfig();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  return function onType(event: KeyboardEvent) {\n    const { key } = event;\n\n    if (hasModifier(event) || searchDisabled) {\n      return;\n    }\n\n    if (key.match(/(^[a-zA-Z0-9]$){1}/)) {\n      event.preventDefault();\n      closeAllOpenToggles();\n      focusSearchInput();\n      appendSearch(key);\n    }\n  };\n}\n\nfunction hasModifier(event: KeyboardEvent): boolean {\n  const { metaKey, ctrlKey, altKey } = event;\n\n  return metaKey || ctrlKey || altKey;\n}\n", "import { GetEmojiUrl } from '../components/emoji/BaseEmojiProps';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport { emojiUnified, emojiVariations } from '../dataUtils/emojiSelectors';\nimport { EmojiStyle } from '../types/exposedTypes';\n\nexport function preloadEmoji(\n  getEmojiUrl: GetEmojiUrl,\n  emoji: undefined | DataEmoji,\n  emojiStyle: EmojiStyle\n): void {\n  if (!emoji) {\n    return;\n  }\n\n  if (emojiStyle === EmojiStyle.NATIVE) {\n    return;\n  }\n\n  const unified = emojiUnified(emoji);\n\n  if (preloadedEmojs.has(unified)) {\n    return;\n  }\n\n  emojiVariations(emoji).forEach((variation) => {\n    const emojiUrl = getEmojiUrl(variation, emojiStyle);\n    preloadImage(emojiUrl);\n  });\n\n  preloadedEmojs.add(unified);\n}\n\nexport const preloadedEmojs: Set<string> = new Set();\n\nfunction preloadImage(url: string): void {\n  const image = new Image();\n  image.src = url;\n}\n", "import { useEffect } from 'react';\n\nimport { buttonFromTarget, emojiFromElement } from '../DomUtils/selectors';\nimport { useBodyRef } from '../components/context/ElementRefContext';\nimport { useEmojiStyleConfig, useGetEmojiUrlConfig } from '../config/useConfig';\nimport { emojiHasVariations } from '../dataUtils/emojiSelectors';\nimport { EmojiStyle } from '../types/exposedTypes';\n\nimport { preloadEmoji } from './preloadEmoji';\n\nexport function useOnFocus() {\n  const BodyRef = useBodyRef();\n  const emojiStyle = useEmojiStyleConfig();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  useEffect(() => {\n    if (emojiStyle === EmojiStyle.NATIVE) {\n      return;\n    }\n\n    const bodyRef = BodyRef.current;\n\n    bodyRef?.addEventListener('focusin', onFocus);\n\n    return () => {\n      bodyRef?.removeEventListener('focusin', onFocus);\n    };\n\n    function onFocus(event: FocusEvent) {\n      const button = buttonFromTarget(event.target as HTMLElement);\n\n      if (!button) {\n        return;\n      }\n\n      const [emoji] = emojiFromElement(button);\n\n      if (!emoji) {\n        return;\n      }\n\n      if (emojiHasVariations(emoji)) {\n        preloadEmoji(getEmojiUrl, emoji, emojiStyle);\n      }\n    }\n  }, [BodyRef, emojiStyle, getEmojiUrl]);\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  useClassNameConfig,\n  useStyleConfig,\n  useThemeConfig\n} from '../../config/useConfig';\nimport useIsSearchMode from '../../hooks/useIsSearchMode';\nimport { useKeyboardNavigation } from '../../hooks/useKeyboardNavigation';\nimport { useOnFocus } from '../../hooks/useOnFocus';\nimport { Theme } from '../../types/exposedTypes';\nimport { usePickerMainRef } from '../context/ElementRefContext';\nimport {\n  PickerContextProvider,\n  useReactionsModeState\n} from '../context/PickerContext';\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n}>;\n\nexport const DEFAULT_LABEL_HEIGHT = 40;\n\nexport default function PickerMain({ children }: Props) {\n  return (\n    <PickerContextProvider>\n      <PickerRootElement>{children}</PickerRootElement>\n    </PickerContextProvider>\n  );\n}\n\ntype RootProps = Readonly<{\n  className?: string;\n  style?: React.CSSProperties;\n  children: React.ReactNode;\n}>;\n\nfunction PickerRootElement({ children }: RootProps) {\n  const [reactionsMode] = useReactionsModeState();\n  const theme = useThemeConfig();\n  const searchModeActive = useIsSearchMode();\n  const PickerMainRef = usePickerMainRef();\n  const className = useClassNameConfig();\n  const style = useStyleConfig();\n\n  useKeyboardNavigation();\n  useOnFocus();\n\n  const { width, height, ...styleProps } = style || {};\n\n  return (\n    <aside\n      className={cx(\n        styles.main,\n        styles.baseVariables,\n        theme === Theme.DARK && styles.darkTheme,\n        theme === Theme.AUTO && styles.autoThemeDark,\n        {\n          [ClassNames.searchActive]: searchModeActive\n        },\n        reactionsMode && styles.reactionsMenu,\n        className\n      )}\n      ref={PickerMainRef}\n      style={{\n        ...styleProps,\n        ...(!reactionsMode && { height, width })\n      }}\n    >\n      {children}\n    </aside>\n  );\n}\n\nconst DarkTheme = {\n  '--epr-emoji-variation-picker-bg-color':\n    'var(--epr-dark-emoji-variation-picker-bg-color)',\n  '--epr-hover-bg-color-reduced-opacity':\n    'var(--epr-dark-hover-bg-color-reduced-opacity)',\n  '--epr-highlight-color': 'var(--epr-dark-highlight-color)',\n  '--epr-text-color': 'var(--epr-dark-text-color)',\n  '--epr-hover-bg-color': 'var(--epr-dark-hover-bg-color)',\n  '--epr-focus-bg-color': 'var(--epr-dark-focus-bg-color)',\n  '--epr-search-input-bg-color': 'var(--epr-dark-search-input-bg-color)',\n  '--epr-category-label-bg-color': 'var(--epr-dark-category-label-bg-color)',\n  '--epr-picker-border-color': 'var(--epr-dark-picker-border-color)',\n  '--epr-bg-color': 'var(--epr-dark-bg-color)',\n  '--epr-reactions-bg-color': 'var(--epr-dark-reactions-bg-color)',\n  '--epr-search-input-bg-color-active':\n    'var(--epr-dark-search-input-bg-color-active)',\n  '--epr-emoji-variation-indicator-color':\n    'var(--epr-dark-emoji-variation-indicator-color)',\n  '--epr-category-icon-active-color':\n    'var(--epr-dark-category-icon-active-color)',\n  '--epr-skin-tone-picker-menu-color':\n    'var(--epr-dark-skin-tone-picker-menu-color)',\n  '--epr-skin-tone-outer-border-color': 'var(--epr-dark-skin-tone-outer-border-color)',\n  '--epr-skin-tone-inner-border-color': 'var(--epr-dark-skin-tone-inner-border-color)'\n};\n\nconst styles = stylesheet.create({\n  main: {\n    '.': ['epr-main', ClassNames.emojiPicker],\n    position: 'relative',\n    display: 'flex',\n    flexDirection: 'column',\n    borderWidth: '1px',\n    borderStyle: 'solid',\n    borderRadius: 'var(--epr-picker-border-radius)',\n    borderColor: 'var(--epr-picker-border-color)',\n    backgroundColor: 'var(--epr-bg-color)',\n    overflow: 'hidden',\n    transition: 'all 0.3s ease-in-out, background-color 0.1s ease-in-out',\n    '*': {\n      boxSizing: 'border-box',\n      fontFamily: 'sans-serif'\n    }\n  },\n  baseVariables: {\n    '--': {\n      '--epr-highlight-color': '#007aeb',\n      '--epr-hover-bg-color': '#e5f0fa',\n      '--epr-hover-bg-color-reduced-opacity': '#e5f0fa80',\n      '--epr-focus-bg-color': '#e0f0ff',\n      '--epr-text-color': '#858585',\n      '--epr-search-input-bg-color': '#f6f6f6',\n      '--epr-picker-border-color': '#e7e7e7',\n      '--epr-bg-color': '#fff',\n      '--epr-reactions-bg-color': '#ffffff90',\n      '--epr-category-icon-active-color': '#6aa8de',\n      '--epr-skin-tone-picker-menu-color': '#ffffff95',\n      '--epr-skin-tone-outer-border-color': '#555555',\n      '--epr-skin-tone-inner-border-color': 'var(--epr-bg-color)',\n\n      '--epr-horizontal-padding': '10px',\n\n      '--epr-picker-border-radius': '8px',\n\n      /* Header */\n      '--epr-search-border-color': 'var(--epr-highlight-color)',\n      '--epr-header-padding': '15px var(--epr-horizontal-padding)',\n\n      /* Skin Tone Picker */\n      '--epr-active-skin-tone-indicator-border-color':\n        'var(--epr-highlight-color)',\n      '--epr-active-skin-hover-color': 'var(--epr-hover-bg-color)',\n\n      /* Search */\n      '--epr-search-input-bg-color-active': 'var(--epr-search-input-bg-color)',\n      '--epr-search-input-padding': '0 30px',\n      '--epr-search-input-border-radius': '8px',\n      '--epr-search-input-height': '40px',\n      '--epr-search-input-text-color': 'var(--epr-text-color)',\n      '--epr-search-input-placeholder-color': 'var(--epr-text-color)',\n      '--epr-search-bar-inner-padding': 'var(--epr-horizontal-padding)',\n\n      /*  Category Navigation */\n      '--epr-category-navigation-button-size': '30px',\n\n      /* Variation Picker */\n      '--epr-emoji-variation-picker-height': '45px',\n      '--epr-emoji-variation-picker-bg-color': 'var(--epr-bg-color)',\n\n      /*  Preview */\n      '--epr-preview-height': '70px',\n      '--epr-preview-text-size': '14px',\n      '--epr-preview-text-padding': '0 var(--epr-horizontal-padding)',\n      '--epr-preview-border-color': 'var(--epr-picker-border-color)',\n      '--epr-preview-text-color': 'var(--epr-text-color)',\n\n      /* Category */\n      '--epr-category-padding': '0 var(--epr-horizontal-padding)',\n\n      /*  Category Label */\n      '--epr-category-label-bg-color': '#ffffffe6',\n      '--epr-category-label-text-color': 'var(--epr-text-color)',\n      '--epr-category-label-padding': '0 var(--epr-horizontal-padding)',\n      '--epr-category-label-height': `${DEFAULT_LABEL_HEIGHT}px`,\n\n      /*  Emoji */\n      '--epr-emoji-size': '30px',\n      '--epr-emoji-padding': '5px',\n      '--epr-emoji-fullsize':\n        'calc(var(--epr-emoji-size) + var(--epr-emoji-padding) * 2)',\n      '--epr-emoji-hover-color': 'var(--epr-hover-bg-color)',\n      '--epr-emoji-variation-indicator-color': 'var(--epr-picker-border-color)',\n      '--epr-emoji-variation-indicator-color-hover': 'var(--epr-text-color)',\n\n      /* Z-Index */\n      '--epr-header-overlay-z-index': '3',\n      '--epr-emoji-variations-indictator-z-index': '1',\n      '--epr-category-label-z-index': '2',\n      '--epr-skin-variation-picker-z-index': '5',\n      '--epr-preview-z-index': '6',\n\n      /* Dark Theme Variables */\n      '--epr-dark': '#000',\n      '--epr-dark-emoji-variation-picker-bg-color': 'var(--epr-dark)',\n      '--epr-dark-highlight-color': '#c0c0c0',\n      '--epr-dark-text-color': 'var(--epr-highlight-color)',\n      '--epr-dark-hover-bg-color': '#363636f6',\n      '--epr-dark-hover-bg-color-reduced-opacity': '#36363680',\n      '--epr-dark-focus-bg-color': '#474747',\n      '--epr-dark-search-input-bg-color': '#333333',\n      '--epr-dark-category-label-bg-color': '#222222e6',\n      '--epr-dark-picker-border-color': '#151617',\n      '--epr-dark-bg-color': '#222222',\n      '--epr-dark-reactions-bg-color': '#22222290',\n      '--epr-dark-search-input-bg-color-active': 'var(--epr-dark)',\n      '--epr-dark-emoji-variation-indicator-color': '#444',\n      '--epr-dark-category-icon-active-color': '#3271b7',\n      '--epr-dark-skin-tone-picker-menu-color': '#22222295',\n      '--epr-dark-skin-tone-outer-border-color': 'var(--epr-dark-picker-border-color)',\n      '--epr-dark-skin-tone-inner-border-color': '#00000000',\n    }\n  },\n  autoThemeDark: {\n    '.': ClassNames.autoTheme,\n    '@media (prefers-color-scheme: dark)': {\n      '--': DarkTheme\n    }\n  },\n  darkTheme: {\n    '.': ClassNames.darkTheme,\n    '--': DarkTheme\n  },\n  reactionsMenu: {\n    '.': 'epr-reactions',\n    height: '50px',\n    display: 'inline-flex',\n    backgroundColor: 'var(--epr-reactions-bg-color)',\n    // @ts-ignore - backdropFilter is not recognized.\n    backdropFilter: 'blur(8px)',\n    '--': {\n      '--epr-picker-border-radius': '50px'\n    }\n  }\n});\n", "import { DEFAULT_LABEL_HEIGHT } from '../components/main/PickerMain';\n\nimport { ClassNames, asSelectors } from './classNames';\nimport { NullableElement } from './selectors';\n\nexport function elementCountInRow(\n  parent: NullableElement,\n  element: NullableElement\n): number {\n  if (!parent || !element) {\n    return 0;\n  }\n\n  const parentWidth = parent.getBoundingClientRect().width;\n  const elementWidth = element.getBoundingClientRect().width;\n  return Math.floor(parentWidth / elementWidth);\n}\n\nexport function elementIndexInRow(\n  parent: NullableElement,\n  element: NullableElement\n): number {\n  if (!parent || !element) {\n    return 0;\n  }\n\n  const elementWidth = element.getBoundingClientRect().width;\n  const elementLeft = element.getBoundingClientRect().left;\n  const parentLeft = parent.getBoundingClientRect().left;\n\n  return Math.floor((elementLeft - parentLeft) / elementWidth);\n}\n\nexport function rowNumber(\n  parent: NullableElement,\n  element: NullableElement\n): number {\n  if (!parent || !element) {\n    return 0;\n  }\n\n  const elementHeight = element.getBoundingClientRect().height;\n  const elementTop = element.getBoundingClientRect().top;\n  const parentTop = parent.getBoundingClientRect().top;\n  return Math.round((elementTop - parentTop) / elementHeight);\n}\n\nexport function hasNextRow(\n  parent: NullableElement,\n  element: NullableElement\n): boolean {\n  if (!parent || !element) {\n    return false;\n  }\n\n  const elementHeight = element.getBoundingClientRect().height;\n  const elementTop = element.getBoundingClientRect().top;\n  const parentTop = parent.getBoundingClientRect().top;\n  const parentHeight = parent.getBoundingClientRect().height;\n\n  return Math.round(elementTop - parentTop + elementHeight) < parentHeight;\n}\n\nfunction getRowElements(\n  elements: HTMLElement[],\n  row: number,\n  elementsInRow: number\n): HTMLElement[] {\n  if (row === -1) {\n    const lastRow = Math.floor((elements.length - 1) / elementsInRow);\n    const firstElementIndex = lastRow * elementsInRow;\n    const lastElementIndex = elements.length - 1;\n    return elements.slice(firstElementIndex, lastElementIndex + 1);\n  }\n\n  return elements.slice(row * elementsInRow, (row + 1) * elementsInRow);\n}\n\nfunction getNextRowElements(\n  allElements: HTMLElement[],\n  currentRow: number,\n  elementsInRow: number\n): HTMLElement[] {\n  const nextRow = currentRow + 1;\n\n  if (nextRow * elementsInRow > allElements.length) {\n    return [];\n  }\n\n  return getRowElements(allElements, nextRow, elementsInRow);\n}\n\nexport function getElementInRow(\n  elements: HTMLElement[],\n  row: number,\n  elementsInRow: number,\n  indexInRow: number\n): NullableElement {\n  const rowElements = getRowElements(elements, row, elementsInRow);\n  // get element, default to last\n  return rowElements[indexInRow] || rowElements[rowElements.length - 1] || null;\n}\n\nexport function getElementInNextRow(\n  allElements: HTMLElement[],\n  currentRow: number,\n  elementsInRow: number,\n  index: number\n): NullableElement {\n  const nextRowElements = getNextRowElements(\n    allElements,\n    currentRow,\n    elementsInRow\n  );\n\n  // return item in index, or last item in row\n  return (\n    nextRowElements[index] ||\n    nextRowElements[nextRowElements.length - 1] ||\n    null\n  );\n}\n\nexport function getElementInPrevRow(\n  allElements: HTMLElement[],\n  currentRow: number,\n  elementsInRow: number,\n  index: number\n): NullableElement {\n  const prevRowElements = getRowElements(\n    allElements,\n    currentRow - 1,\n    elementsInRow\n  );\n\n  // default to last\n  return (\n    prevRowElements[index] ||\n    prevRowElements[prevRowElements.length - 1] ||\n    null\n  );\n}\n\nexport function firstVisibleElementInContainer(\n  parent: NullableElement,\n  elements: HTMLElement[],\n  maxVisibilityDiffThreshold = 0\n): NullableElement {\n  if (!parent || !elements.length) {\n    return null;\n  }\n\n  const parentTop = parent.getBoundingClientRect().top;\n  const parentBottom = parent.getBoundingClientRect().bottom;\n  const parentTopWithLabel = parentTop + getLabelHeight(parent);\n\n  const visibleElements = elements.find(element => {\n    const elementTop = element.getBoundingClientRect().top;\n    const elementBottom = element.getBoundingClientRect().bottom;\n    const maxVisibilityDiffPixels =\n      element.clientHeight * maxVisibilityDiffThreshold;\n\n    const elementTopWithAllowedDiff = elementTop + maxVisibilityDiffPixels;\n    const elementBottomWithAllowedDiff =\n      elementBottom - maxVisibilityDiffPixels;\n\n    if (elementTopWithAllowedDiff < parentTopWithLabel) {\n      return false;\n    }\n\n    return (\n      (elementTopWithAllowedDiff >= parentTop &&\n        elementTopWithAllowedDiff <= parentBottom) ||\n      (elementBottomWithAllowedDiff >= parentTop &&\n        elementBottomWithAllowedDiff <= parentBottom)\n    );\n  });\n\n  return visibleElements || null;\n}\n\nexport function hasNextElementSibling(element: HTMLElement) {\n  return !!element.nextElementSibling;\n}\n\nfunction getLabelHeight(parentNode: HTMLElement) {\n  const labels = Array.from(\n    parentNode.querySelectorAll(asSelectors(ClassNames.label))\n  );\n\n  for (const label of labels) {\n    const height = label.getBoundingClientRect().height;\n    // return height if label is not hidden\n    if (height > 0) {\n      return height;\n    }\n  }\n\n  return DEFAULT_LABEL_HEIGHT;\n}\n", "import { DataEmoji } from '../dataUtils/DataTypes';\nimport {\n  emojiByUnified,\n  unifiedWithoutSkinTone\n} from '../dataUtils/emojiSelectors';\n\nimport { asSelectors, ClassNames } from './classNames';\nimport { firstVisibleElementInContainer } from './elementPositionInRow';\n\nexport type NullableElement = HTMLElement | null;\n\nexport const EmojiButtonSelector = `button${asSelectors(ClassNames.emoji)}`;\nexport const VisibleEmojiSelector = [\n  EmojiButtonSelector,\n  asSelectors(ClassNames.visible),\n  `:not(${asSelectors(ClassNames.hidden)})`\n].join('');\n\nexport function buttonFromTarget(\n  emojiElement: NullableElement\n): HTMLButtonElement | null {\n  return emojiElement?.closest(EmojiButtonSelector) ?? null;\n}\n\nexport function isEmojiButton(element: NullableElement): boolean {\n  if (!element) {\n    return false;\n  }\n\n  return element.matches(EmojiButtonSelector);\n}\n\nexport function emojiFromElement(\n  element: NullableElement\n): [DataEmoji, string] | [] {\n  const originalUnified = originalUnifiedFromEmojiElement(element);\n  const unified = unifiedFromEmojiElement(element);\n\n  if (!originalUnified) {\n    return [];\n  }\n\n  const emoji = emojiByUnified(unified ?? originalUnified);\n\n  if (!emoji) {\n    return [];\n  }\n\n  return [emoji, unified as string];\n}\n\nexport function isEmojiElement(element: NullableElement): boolean {\n  return Boolean(\n    element?.matches(EmojiButtonSelector) ||\n      element?.parentElement?.matches(EmojiButtonSelector)\n  );\n}\n\nexport function categoryLabelFromCategory(\n  category: NullableElement\n): NullableElement {\n  return category?.querySelector(asSelectors(ClassNames.label)) ?? null;\n}\n\nexport function closestCategoryLabel(\n  element: NullableElement\n): NullableElement {\n  const category = closestCategory(element);\n  return categoryLabelFromCategory(category);\n}\n\nexport function elementHeight(element: NullableElement): number {\n  return element?.clientHeight ?? 0;\n}\n\nexport function emojiTrueOffsetTop(element: NullableElement): number {\n  if (!element) {\n    return 0;\n  }\n\n  const button = buttonFromTarget(element);\n  const category = closestCategory(button);\n\n  // compensate for the label height\n  const labelHeight = categoryLabelHeight(category);\n\n  return elementOffsetTop(button) + elementOffsetTop(category) + labelHeight;\n}\n\nexport function categoryLabelHeight(category: NullableElement): number {\n  if (!category) {\n    return 0;\n  }\n\n  const categoryWithoutLabel = category.querySelector(\n    asSelectors(ClassNames.categoryContent)\n  );\n\n  return (\n    (category?.clientHeight ?? 0) - (categoryWithoutLabel?.clientHeight ?? 0)\n  );\n}\n\nexport function isEmojiBehindLabel(emoji: NullableElement): boolean {\n  if (!emoji) {\n    return false;\n  }\n\n  return (\n    emojiDistanceFromScrollTop(emoji) <\n    categoryLabelHeight(closestCategory(emoji))\n  );\n}\n\nexport function queryScrollBody(root: NullableElement): NullableElement {\n  if (!root) return null;\n\n  return root.matches(asSelectors(ClassNames.scrollBody))\n    ? root\n    : root.querySelector(asSelectors(ClassNames.scrollBody));\n}\n\nexport function emojiDistanceFromScrollTop(emoji: NullableElement): number {\n  if (!emoji) {\n    return 0;\n  }\n\n  return emojiTrueOffsetTop(emoji) - (closestScrollBody(emoji)?.scrollTop ?? 0);\n}\n\nexport function closestScrollBody(element: NullableElement): NullableElement {\n  if (!element) {\n    return null;\n  }\n\n  return element.closest(asSelectors(ClassNames.scrollBody)) ?? null;\n}\n\nexport function emojiTruOffsetLeft(element: NullableElement): number {\n  const button = buttonFromTarget(element);\n  const category = closestCategory(button);\n\n  return elementOffsetLeft(button) + elementOffsetLeft(category);\n}\n\nfunction elementOffsetTop(element: NullableElement): number {\n  return element?.offsetTop ?? 0;\n}\n\nfunction elementOffsetLeft(element: NullableElement): number {\n  return element?.offsetLeft ?? 0;\n}\n\nexport function unifiedFromEmojiElement(emoji: NullableElement): string | null {\n  return elementDataSetKey(buttonFromTarget(emoji), 'unified') ?? null;\n}\n\nexport function originalUnifiedFromEmojiElement(\n  emoji: NullableElement\n): string | null {\n  const unified = unifiedFromEmojiElement(emoji);\n\n  if (unified) {\n    return unifiedWithoutSkinTone(unified);\n  }\n  return null;\n}\n\nexport function allUnifiedFromEmojiElement(\n  emoji: NullableElement\n): { unified: string | null; originalUnified: string | null } {\n  if (!emoji) {\n    return {\n      unified: null,\n      originalUnified: null\n    };\n  }\n\n  return {\n    unified: unifiedFromEmojiElement(emoji),\n    originalUnified: originalUnifiedFromEmojiElement(emoji)\n  };\n}\n\nfunction elementDataSetKey(\n  element: NullableElement,\n  key: string\n): string | null {\n  return elementDataSet(element)[key] ?? null;\n}\n\nfunction elementDataSet(element: NullableElement): DOMStringMap {\n  return element?.dataset ?? {};\n}\n\nexport function isVisibleEmoji(element: HTMLElement) {\n  return element.classList.contains(ClassNames.visible);\n}\n\nexport function isHidden(element: NullableElement) {\n  if (!element) return true;\n\n  return element.classList.contains(ClassNames.hidden);\n}\n\nexport function allVisibleEmojis(parent: NullableElement) {\n  if (!parent) {\n    return [];\n  }\n\n  return Array.from(\n    parent.querySelectorAll(VisibleEmojiSelector)\n  ) as HTMLElement[];\n}\n\nexport function lastVisibleEmoji(element: NullableElement): NullableElement {\n  if (!element) return null;\n\n  const allEmojis = allVisibleEmojis(element);\n  const [last] = allEmojis.slice(-1);\n  if (!last) {\n    return null;\n  }\n\n  if (!isVisibleEmoji(last)) {\n    return prevVisibleEmoji(last);\n  }\n\n  return last;\n}\n\nexport function nextVisibleEmoji(element: HTMLElement): NullableElement {\n  const next = element.nextElementSibling as HTMLElement;\n\n  if (!next) {\n    return firstVisibleEmoji(nextCategory(element));\n  }\n\n  if (!isVisibleEmoji(next)) {\n    return nextVisibleEmoji(next);\n  }\n\n  return next;\n}\n\nexport function prevVisibleEmoji(element: HTMLElement): NullableElement {\n  const prev = element.previousElementSibling as HTMLElement;\n\n  if (!prev) {\n    return lastVisibleEmoji(prevCategory(element));\n  }\n\n  if (!isVisibleEmoji(prev)) {\n    return prevVisibleEmoji(prev);\n  }\n\n  return prev;\n}\n\nexport function firstVisibleEmoji(parent: NullableElement) {\n  if (!parent) {\n    return null;\n  }\n\n  const allEmojis = allVisibleEmojis(parent);\n\n  return firstVisibleElementInContainer(parent, allEmojis, 0.1);\n}\n\nexport function prevCategory(element: NullableElement): NullableElement {\n  const category = closestCategory(element);\n\n  if (!category) {\n    return null;\n  }\n\n  const prev = category.previousElementSibling as HTMLElement;\n\n  if (!prev) {\n    return null;\n  }\n\n  if (isHidden(prev)) {\n    return prevCategory(prev);\n  }\n\n  return prev;\n}\n\nexport function nextCategory(element: NullableElement): NullableElement {\n  const category = closestCategory(element);\n\n  if (!category) {\n    return null;\n  }\n\n  const next = category.nextElementSibling as HTMLElement;\n\n  if (!next) {\n    return null;\n  }\n\n  if (isHidden(next)) {\n    return nextCategory(next);\n  }\n\n  return next;\n}\n\nexport function closestCategory(element: NullableElement) {\n  if (!element) {\n    return null;\n  }\n  return element.closest(asSelectors(ClassNames.category)) as HTMLElement;\n}\n\nexport function closestCategoryContent(element: NullableElement) {\n  if (!element) {\n    return null;\n  }\n  return element.closest(\n    asSelectors(ClassNames.categoryContent)\n  ) as HTMLElement;\n}\n", "export function parseNativeEmoji(unified: string): string {\n  return unified\n    .split('-')\n    .map(hex => String.fromCodePoint(parseInt(hex, 16)))\n    .join('');\n}\n", "import { SkinTones, SuggestionMode } from '../types/exposedTypes';\n\nimport { DataEmoji } from './DataTypes';\nimport { emojiUnified } from './emojiSelectors';\n\nconst SUGGESTED_LS_KEY = 'epr_suggested';\n\ntype SuggestedItem = {\n  unified: string;\n  original: string;\n  count: number;\n};\n\ntype Suggested = SuggestedItem[];\n\nexport function getSuggested(mode?: SuggestionMode): Suggested {\n  try {\n    if (!window?.localStorage) {\n      return [];\n    }\n    const recent = JSON.parse(\n      window?.localStorage.getItem(SUGGESTED_LS_KEY) ?? '[]'\n    ) as Suggested;\n\n    if (mode === SuggestionMode.FREQUENT) {\n      return recent.sort((a, b) => b.count - a.count);\n    }\n\n    return recent;\n  } catch {\n    return [];\n  }\n}\n\nexport function setSuggested(emoji: DataEmoji, skinTone: SkinTones) {\n  const recent = getSuggested();\n\n  const unified = emojiUnified(emoji, skinTone);\n  const originalUnified = emojiUnified(emoji);\n\n  let existing = recent.find(({ unified: u }) => u === unified);\n\n  let nextList: SuggestedItem[];\n\n  if (existing) {\n    nextList = [existing].concat(recent.filter(i => i !== existing));\n  } else {\n    existing = {\n      unified,\n      original: originalUnified,\n      count: 0\n    };\n    nextList = [existing, ...recent];\n  }\n\n  existing.count++;\n\n  nextList.length = Math.min(nextList.length, 14);\n\n  try {\n    window?.localStorage.setItem(SUGGESTED_LS_KEY, JSON.stringify(nextList));\n    // Prevents the change from being seen immediately.\n  } catch {\n    // ignore\n  }\n}\n", "import {\n  Categories,\n  CategoryConfig,\n  CustomCategoryConfig\n} from '../config/categoryConfig';\nimport { CustomEmoji } from '../config/customEmojiConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\n\nexport function isCustomCategory(\n  category: CategoryConfig | CustomCategoryConfig\n): category is CustomCategoryConfig {\n  return category.category === Categories.CUSTOM;\n}\n\nexport function isCustomEmoji(emoji: Partial<DataEmoji>): emoji is CustomEmoji {\n  return emoji.imgUrl !== undefined;\n}\n", "import * as React from 'react';\nimport { useEffect, useRef } from 'react';\n\nimport {\n  emojiFromElement,\n  isEmojiElement,\n  NullableElement\n} from '../DomUtils/selectors';\nimport {\n  useActiveSkinToneState,\n  useDisallowClickRef,\n  useEmojiVariationPickerState,\n  useUpdateSuggested\n} from '../components/context/PickerContext';\nimport { GetEmojiUrl } from '../components/emoji/BaseEmojiProps';\nimport {\n  MOUSE_EVENT_SOURCE,\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  useOnEmojiClickConfig\n} from '../config/useConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport {\n  activeVariationFromUnified,\n  emojiHasVariations,\n  emojiNames,\n  emojiUnified\n} from '../dataUtils/emojiSelectors';\nimport { parseNativeEmoji } from '../dataUtils/parseNativeEmoji';\nimport { setSuggested } from '../dataUtils/suggested';\nimport { isCustomEmoji } from '../typeRefinements/typeRefinements';\nimport { EmojiClickData, SkinTones, EmojiStyle } from '../types/exposedTypes';\n\nimport { useCloseAllOpenToggles } from './useCloseAllOpenToggles';\nimport useSetVariationPicker from './useSetVariationPicker';\n\nexport function useMouseDownHandlers(\n  ContainerRef: React.MutableRefObject<NullableElement>,\n  mouseEventSource: MOUSE_EVENT_SOURCE\n) {\n  const mouseDownTimerRef = useRef<undefined | number>();\n  const setVariationPicker = useSetVariationPicker();\n  const disallowClickRef = useDisallowClickRef();\n  const [, setEmojiVariationPicker] = useEmojiVariationPickerState();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n  const [activeSkinTone] = useActiveSkinToneState();\n  const onEmojiClick = useOnEmojiClickConfig(mouseEventSource);\n  const [, updateSuggested] = useUpdateSuggested();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n  const activeEmojiStyle = useEmojiStyleConfig();\n\n  const onClick = React.useCallback(\n    function onClick(event: MouseEvent) {\n      if (disallowClickRef.current) {\n        return;\n      }\n\n      closeAllOpenToggles();\n\n      const [emoji, unified] = emojiFromEvent(event);\n\n      if (!emoji || !unified) {\n        return;\n      }\n\n      const skinToneToUse =\n        activeVariationFromUnified(unified) || activeSkinTone;\n\n      updateSuggested();\n      setSuggested(emoji, skinToneToUse);\n      onEmojiClick(\n        emojiClickOutput(emoji, skinToneToUse, activeEmojiStyle, getEmojiUrl),\n        event\n      );\n    },\n    [\n      activeSkinTone,\n      closeAllOpenToggles,\n      disallowClickRef,\n      onEmojiClick,\n      updateSuggested,\n      getEmojiUrl,\n      activeEmojiStyle\n    ]\n  );\n\n  const onMouseDown = React.useCallback(\n    function onMouseDown(event: MouseEvent) {\n      if (mouseDownTimerRef.current) {\n        clearTimeout(mouseDownTimerRef.current);\n      }\n\n      const [emoji] = emojiFromEvent(event);\n\n      if (!emoji || !emojiHasVariations(emoji)) {\n        return;\n      }\n\n      mouseDownTimerRef.current = window?.setTimeout(() => {\n        disallowClickRef.current = true;\n        mouseDownTimerRef.current = undefined;\n        closeAllOpenToggles();\n        setVariationPicker(event.target as HTMLElement);\n        setEmojiVariationPicker(emoji);\n      }, 500);\n    },\n    [\n      disallowClickRef,\n      closeAllOpenToggles,\n      setVariationPicker,\n      setEmojiVariationPicker\n    ]\n  );\n  const onMouseUp = React.useCallback(\n    function onMouseUp() {\n      if (mouseDownTimerRef.current) {\n        clearTimeout(mouseDownTimerRef.current);\n        mouseDownTimerRef.current = undefined;\n      } else if (disallowClickRef.current) {\n        // The problem we're trying to overcome here\n        // is that the emoji has both mouseup and click events\n        // and when releasing a mouseup event\n        // the click gets triggered too\n        // So we're disallowing the click event for a short time\n\n        requestAnimationFrame(() => {\n          disallowClickRef.current = false;\n        });\n      }\n    },\n    [disallowClickRef]\n  );\n\n  useEffect(() => {\n    if (!ContainerRef.current) {\n      return;\n    }\n    const confainerRef = ContainerRef.current;\n    confainerRef.addEventListener('click', onClick, {\n      passive: true\n    });\n\n    confainerRef.addEventListener('mousedown', onMouseDown, {\n      passive: true\n    });\n    confainerRef.addEventListener('mouseup', onMouseUp, {\n      passive: true\n    });\n\n    return () => {\n      confainerRef?.removeEventListener('click', onClick);\n      confainerRef?.removeEventListener('mousedown', onMouseDown);\n      confainerRef?.removeEventListener('mouseup', onMouseUp);\n    };\n  }, [ContainerRef, onClick, onMouseDown, onMouseUp]);\n}\n\nfunction emojiFromEvent(event: MouseEvent): [DataEmoji, string] | [] {\n  const target = event?.target as HTMLElement;\n  if (!isEmojiElement(target)) {\n    return [];\n  }\n\n  return emojiFromElement(target);\n}\n\nfunction emojiClickOutput(\n  emoji: DataEmoji,\n  activeSkinTone: SkinTones,\n  activeEmojiStyle: EmojiStyle,\n  getEmojiUrl: GetEmojiUrl\n): EmojiClickData {\n  const names = emojiNames(emoji);\n\n  if (isCustomEmoji(emoji)) {\n    const unified = emojiUnified(emoji);\n    return {\n      activeSkinTone,\n      emoji: unified,\n      getImageUrl() {\n        return emoji.imgUrl;\n      },\n      imageUrl: emoji.imgUrl,\n      isCustom: true,\n      names,\n      unified,\n      unifiedWithoutSkinTone: unified\n    };\n  }\n  const unified = emojiUnified(emoji, activeSkinTone);\n\n  return {\n    activeSkinTone,\n    emoji: parseNativeEmoji(unified),\n    getImageUrl(emojiStyle: EmojiStyle = activeEmojiStyle ?? EmojiStyle.APPLE) {\n      return getEmojiUrl(unified, emojiStyle);\n    },\n    imageUrl: getEmojiUrl(unified, activeEmojiStyle ?? EmojiStyle.APPLE),\n    isCustom: false,\n    names,\n    unified,\n    unifiedWithoutSkinTone: emojiUnified(emoji)\n  };\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\n\ninterface Props\n  extends React.DetailedHTMLProps<\n    React.ButtonHTMLAttributes<HTMLButtonElement>,\n    HTMLButtonElement\n  > {\n  className?: string;\n}\n\nexport function Button(props: Props) {\n  return (\n    <button\n      type=\"button\"\n      {...props}\n      className={cx(styles.button, props.className)}\n    >\n      {props.children}\n    </button>\n  );\n}\n\nconst styles = stylesheet.create({\n  button: {\n    '.': 'epr-btn',\n    cursor: 'pointer',\n    border: '0',\n    background: 'none',\n    outline: 'none'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  commonStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport { Button } from '../atoms/Button';\n\ntype ClickableEmojiButtonProps = Readonly<{\n  hidden?: boolean;\n  showVariations?: boolean;\n  hiddenOnSearch?: boolean;\n  emojiNames: string[];\n  children: React.ReactNode;\n  hasVariations: boolean;\n  unified?: string;\n  noBackground?: boolean;\n  className?: string;\n}>;\n\nexport function ClickableEmojiButton({\n  emojiNames,\n  unified,\n  hidden,\n  hiddenOnSearch,\n  showVariations = true,\n  hasVariations,\n  children,\n  className,\n  noBackground = false\n}: ClickableEmojiButtonProps) {\n  return (\n    <Button\n      className={cx(\n        styles.emoji,\n        hidden && commonStyles.hidden,\n        hiddenOnSearch && commonInteractionStyles.hiddenOnSearch,\n        {\n          [ClassNames.visible]: !hidden && !hiddenOnSearch\n        },\n        !!(hasVariations && showVariations) && styles.hasVariations,\n        noBackground && styles.noBackground,\n        className\n      )}\n      data-unified={unified}\n      aria-label={getAriaLabel(emojiNames)}\n      data-full-name={emojiNames}\n    >\n      {children}\n    </Button>\n  );\n}\n\nfunction getAriaLabel(emojiNames: string[]) {\n  return emojiNames[0].match('flag-')\n    ? emojiNames[1] ?? emojiNames[0]\n    : emojiNames[0];\n}\n\nconst styles = stylesheet.create({\n  emoji: {\n    '.': ClassNames.emoji,\n    position: 'relative',\n    width: 'var(--epr-emoji-fullsize)',\n    height: 'var(--epr-emoji-fullsize)',\n    boxSizing: 'border-box',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    maxWidth: 'var(--epr-emoji-fullsize)',\n    maxHeight: 'var(--epr-emoji-fullsize)',\n    borderRadius: '8px',\n    overflow: 'hidden',\n    transition: 'background-color 0.2s',\n    ':hover': {\n      backgroundColor: 'var(--epr-emoji-hover-color)'\n    },\n    ':focus': {\n      backgroundColor: 'var(--epr-focus-bg-color)'\n    }\n  },\n  noBackground: {\n    background: 'none',\n    ':hover': {\n      backgroundColor: 'transparent',\n      background: 'none'\n    },\n    ':focus': {\n      backgroundColor: 'transparent',\n      background: 'none'\n    }\n  },\n  hasVariations: {\n    '.': ClassNames.emojiHasVariations,\n    ':after': {\n      content: '',\n      display: 'block',\n      width: '0',\n      height: '0',\n      right: '0px',\n      bottom: '1px',\n      position: 'absolute',\n      borderLeft: '4px solid transparent',\n      borderRight: '4px solid transparent',\n      transform: 'rotate(135deg)',\n      borderBottom: '4px solid var(--epr-emoji-variation-indicator-color)',\n      zIndex: 'var(--epr-emoji-variations-indictator-z-index)'\n    },\n    ':hover:after': {\n      borderBottom: '4px solid var(--epr-emoji-variation-indicator-color-hover)'\n    }\n  }\n});\n", "import { ClassNames } from '../../DomUtils/classNames';\nimport { stylesheet } from '../../Stylesheet/stylesheet';\n\nexport const emojiStyles = stylesheet.create({\n  external: {\n    '.': ClassNames.external,\n    fontSize: '0'\n  },\n  common: {\n    alignSelf: 'center',\n    justifySelf: 'center',\n    display: 'block'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport { EmojiStyle } from '../../types/exposedTypes';\n\nimport { emojiStyles } from './emojiStyles';\n\nexport function EmojiImg({\n  emojiName,\n  style,\n  lazyLoad = false,\n  imgUrl,\n  onError,\n  className\n}: {\n  emojiName: string;\n  emojiStyle: EmojiStyle;\n  style: React.CSSProperties;\n  lazyLoad?: boolean;\n  imgUrl: string;\n    onError: () => void;\n  className?: string;\n}) {\n  return (\n    <img\n      src={imgUrl}\n      alt={emojiName}\n      className={cx(styles.emojiImag, emojiStyles.external, emojiStyles.common, className)}\n      loading={lazyLoad ? 'lazy' : 'eager'}\n      onError={onError}\n      style={style}\n    />\n  );\n}\n\nconst styles = stylesheet.create({\n  emojiImag: {\n    '.': 'epr-emoji-img',\n    maxWidth: 'var(--epr-emoji-fullsize)',\n    maxHeight: 'var(--epr-emoji-fullsize)',\n    minWidth: 'var(--epr-emoji-fullsize)',\n    minHeight: 'var(--epr-emoji-fullsize)',\n    padding: 'var(--epr-emoji-padding)'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport { parseNativeEmoji } from '../../dataUtils/parseNativeEmoji';\n\nimport { emojiStyles } from './emojiStyles';\n\nexport function NativeEmoji({\n  unified,\n  style,\n  className\n}: {\n  unified: string;\n  style: React.CSSProperties;\n  className?: string;\n}) {\n  return (\n    <span\n      className={cx(\n        styles.nativeEmoji,\n        emojiStyles.common,\n        emojiStyles.external,\n        className\n      )}\n      data-unified={unified}\n      style={style}\n    >\n      {parseNativeEmoji(unified)}\n    </span>\n  );\n}\n\nconst styles = stylesheet.create({\n  nativeEmoji: {\n    '.': 'epr-emoji-native',\n    fontFamily:\n      '\"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Segoe UI\", \"Apple Color Emoji\", \"Twemoji Mozilla\", \"Noto Color Emoji\", \"EmojiOne Color\", \"Android Emoji\"!important',\n    position: 'relative',\n    lineHeight: '100%',\n    fontSize: 'var(--epr-emoji-size)',\n    textAlign: 'center',\n    alignSelf: 'center',\n    justifySelf: 'center',\n    letterSpacing: '0',\n    padding: 'var(--epr-emoji-padding)'\n  }\n});\n", "import * as React from 'react';\n\nimport {\n  emojiByUnified,\n  emojiName,\n  emojiUrlByUnified\n} from '../../dataUtils/emojiSelectors';\nimport { isCustomEmoji } from '../../typeRefinements/typeRefinements';\nimport { EmojiStyle } from '../../types/exposedTypes';\nimport { useEmojisThatFailedToLoadState } from '../context/PickerContext';\n\nimport { BaseEmojiProps } from './BaseEmojiProps';\nimport { EmojiImg } from './EmojiImg';\nimport { NativeEmoji } from './NativeEmoji';\n\nexport function ViewOnlyEmoji({\n  emoji,\n  unified,\n  emojiStyle,\n  size,\n  lazyLoad,\n  getEmojiUrl = emojiUrlByUnified,\n  className\n}: BaseEmojiProps) {\n  const [, setEmojisThatFailedToLoad] = useEmojisThatFailedToLoadState();\n\n  const style = {} as React.CSSProperties;\n  if (size) {\n    style.width = style.height = style.fontSize = `${size}px`;\n  }\n\n  const emojiToRender = emoji ? emoji : emojiByUnified(unified);\n\n  if (!emojiToRender) {\n    return null;\n  }\n\n  if (isCustomEmoji(emojiToRender)) {\n    return (\n      <EmojiImg\n        style={style}\n        emojiName={unified}\n        emojiStyle={EmojiStyle.NATIVE}\n        lazyLoad={lazyLoad}\n        imgUrl={emojiToRender.imgUrl}\n        onError={onError}\n        className={className}\n      />\n    );\n  }\n\n  return (\n    <>\n      {emojiStyle === EmojiStyle.NATIVE ? (\n        <NativeEmoji unified={unified} style={style} className={className} />\n      ) : (\n        <EmojiImg\n          style={style}\n          emojiName={emojiName(emojiToRender)}\n          emojiStyle={emojiStyle}\n          lazyLoad={lazyLoad}\n          imgUrl={getEmojiUrl(unified, emojiStyle)}\n          onError={onError}\n          className={className}\n        />\n      )}\n    </>\n  );\n\n  function onError() {\n    setEmojisThatFailedToLoad(prev => new Set(prev).add(unified));\n  }\n}\n", "import * as React from 'react';\n\nimport { DataEmoji } from '../../dataUtils/DataTypes';\nimport { emojiHasVariations, emojiNames } from '../../dataUtils/emojiSelectors';\n\nimport { BaseEmojiProps } from './BaseEmojiProps';\nimport { ClickableEmojiButton } from './ClickableEmojiButton';\nimport { ViewOnlyEmoji } from './ViewOnlyEmoji';\n\ntype ClickableEmojiProps = Readonly<\n  BaseEmojiProps & {\n    hidden?: boolean;\n    showVariations?: boolean;\n    hiddenOnSearch?: boolean;\n    emoji: DataEmoji;\n    className?: string;\n    noBackground?: boolean;\n  }\n>;\n\nexport function ClickableEmoji({\n  emoji,\n  unified,\n  hidden,\n  hiddenOnSearch,\n  emojiStyle,\n  showVariations = true,\n  size,\n  lazyLoad,\n  getEmojiUrl,\n  className,\n  noBackground = false\n}: ClickableEmojiProps) {\n  const hasVariations = emojiHasVariations(emoji);\n\n  return (\n    <ClickableEmojiButton\n      hasVariations={hasVariations}\n      showVariations={showVariations}\n      hidden={hidden}\n      hiddenOnSearch={hiddenOnSearch}\n      emojiNames={emojiNames(emoji)}\n      unified={unified}\n      noBackground={noBackground}\n    >\n      <ViewOnlyEmoji\n        unified={unified}\n        emoji={emoji}\n        size={size}\n        emojiStyle={emojiStyle}\n        lazyLoad={lazyLoad}\n        getEmojiUrl={getEmojiUrl}\n        className={className}\n      />\n    </ClickableEmojiButton>\n  );\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { darkMode, stylesheet } from '../../Stylesheet/stylesheet';\nimport { Button } from '../atoms/Button';\nimport { useReactionsModeState } from '../context/PickerContext';\n\nimport Plus from './svg/plus.svg';\n\nexport function BtnPlus() {\n  const [, setReactionsMode] = useReactionsModeState();\n  return (\n    <Button\n      aria-label=\"Show all Emojis\"\n      title=\"Show all Emojis\"\n      tabIndex={0}\n      className={cx(styles.plusSign)}\n      onClick={() => setReactionsMode(false)}\n    />\n  );\n}\n\nconst styles = stylesheet.create({\n  plusSign: {\n    fontSize: '20px',\n    padding: '17px',\n    color: 'var(--epr-text-color)',\n    borderRadius: '50%',\n    textAlign: 'center',\n    lineHeight: '100%',\n    width: '20px',\n    height: '20px',\n    display: 'flex',\n    justifyContent: 'center',\n    alignItems: 'center',\n    transition: 'background-color 0.2s ease-in-out',\n    ':after': {\n      content: '',\n      minWidth: '20px',\n      minHeight: '20px',\n      backgroundImage: `url(${Plus})`,\n      backgroundColor: 'transparent',\n      backgroundRepeat: 'no-repeat',\n      backgroundSize: '20px',\n      backgroundPositionY: '0'\n    },\n    ':hover': {\n      color: 'var(--epr-highlight-color)',\n      backgroundColor: 'var(--epr-hover-bg-color-reduced-opacity)',\n      ':after': {\n        backgroundPositionY: '-20px'\n      }\n    },\n    ':focus': {\n      color: 'var(--epr-highlight-color)',\n      backgroundColor: 'var(--epr-hover-bg-color-reduced-opacity)',\n      ':after': {\n        backgroundPositionY: '-40px'\n      }\n    }\n  },\n  ...darkMode('plusSign', {\n    ':after': { backgroundPositionY: '-40px' },\n    ':hover:after': { backgroundPositionY: '-60px' }\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { commonStyles, stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  MOUSE_EVENT_SOURCE,\n  useEmojiStyleConfig,\n  useReactionsConfig,\n  useAllowExpandReactions,\n  useGetEmojiUrlConfig\n} from '../../config/useConfig';\nimport { DataEmoji } from '../../dataUtils/DataTypes';\nimport { emojiByUnified } from '../../dataUtils/emojiSelectors';\nimport { useMouseDownHandlers } from '../../hooks/useMouseDownHandlers';\nimport { useReactionsRef } from '../context/ElementRefContext';\nimport { useReactionsModeState } from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport { BtnPlus } from './BtnPlus';\n\nexport function Reactions() {\n  const [reactionsOpen] = useReactionsModeState();\n  const ReactionsRef = useReactionsRef();\n  const reactions = useReactionsConfig();\n  useMouseDownHandlers(ReactionsRef, MOUSE_EVENT_SOURCE.REACTIONS);\n  const emojiStyle = useEmojiStyleConfig();\n  const allowExpandReactions = useAllowExpandReactions();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  if (!reactionsOpen) {\n    return null;\n  }\n\n  return (\n    <ul\n      className={cx(styles.list, !reactionsOpen && commonStyles.hidden)}\n      ref={ReactionsRef}\n    >\n      {reactions.map(reaction => (\n        <li key={reaction}>\n          <ClickableEmoji\n            emoji={emojiByUnified(reaction) as DataEmoji}\n            emojiStyle={emojiStyle}\n            unified={reaction}\n            showVariations={false}\n            className={cx(styles.emojiButton)}\n            noBackground\n            getEmojiUrl={getEmojiUrl}\n          />\n        </li>\n      ))}\n      {allowExpandReactions ? (\n        <li>\n          <BtnPlus />\n        </li>\n      ) : null}\n    </ul>\n  );\n}\n\nconst styles = stylesheet.create({\n  list: {\n    listStyle: 'none',\n    margin: '0',\n    padding: '0 5px',\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    height: '100%'\n  },\n  emojiButton: {\n    ':hover': {\n      transform: 'scale(1.2)'\n    },\n    ':focus': {\n      transform: 'scale(1.2)'\n    },\n    ':active': {\n      transform: 'scale(1.1)'\n    },\n    transition: 'transform 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.5)'\n  }\n});\n", "import { useEffect } from 'react';\n\nimport { ElementRef } from '../components/context/ElementRefContext';\n\nimport { useCloseAllOpenToggles } from './useCloseAllOpenToggles';\n\nexport function useOnScroll(BodyRef: ElementRef) {\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  useEffect(() => {\n    const bodyRef = BodyRef.current;\n    if (!bodyRef) {\n      return;\n    }\n\n    bodyRef.addEventListener('scroll', onScroll, {\n      passive: true\n    });\n\n    function onScroll() {\n      closeAllOpenToggles();\n    }\n\n    return () => {\n      bodyRef?.removeEventListener('scroll', onScroll);\n    };\n  }, [BodyRef, closeAllOpenToggles]);\n}\n", "import { useEmojisThatFailedToLoadState } from '../components/context/PickerContext';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport { emojiUnified } from '../dataUtils/emojiSelectors';\n\nimport { useIsEmojiFiltered } from './useFilter';\n\nexport function useIsEmojiHidden(): (emoji: DataEmoji) => IsHiddenReturn {\n  const [emojisThatFailedToLoad] = useEmojisThatFailedToLoadState();\n  const isEmojiFiltered = useIsEmojiFiltered();\n\n  return (emoji: DataEmoji): IsHiddenReturn => {\n    const unified = emojiUnified(emoji);\n\n    const failedToLoad = emojisThatFailedToLoad.has(unified);\n    const filteredOut = isEmojiFiltered(unified);\n\n    return {\n      failedToLoad,\n      filteredOut,\n      hidden: failedToLoad || filteredOut\n    };\n  };\n}\n\ntype IsHiddenReturn = {\n  failedToLoad: boolean;\n  filteredOut: boolean;\n  hidden: boolean;\n};\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  commonStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport {\n  CategoryConfig,\n  categoryFromCategoryConfig,\n  categoryNameFromCategoryConfig\n} from '../../config/categoryConfig';\n\ntype Props = Readonly<{\n  categoryConfig: CategoryConfig;\n  children?: React.ReactNode;\n  hidden?: boolean;\n  hiddenOnSearch?: boolean;\n}>;\n\nexport function EmojiCategory({\n  categoryConfig,\n  children,\n  hidden,\n  hiddenOnSearch\n}: Props) {\n  const category = categoryFromCategoryConfig(categoryConfig);\n  const categoryName = categoryNameFromCategoryConfig(categoryConfig);\n\n  return (\n    <li\n      className={cx(\n        styles.category,\n        hidden && commonStyles.hidden,\n        hiddenOnSearch && commonInteractionStyles.hiddenOnSearch\n      )}\n      data-name={category}\n      aria-label={categoryName}\n    >\n      <h2 className={cx(styles.label)}>{categoryName}</h2>\n      <div className={cx(styles.categoryContent)}>{children}</div>\n    </li>\n  );\n}\n\nconst styles = stylesheet.create({\n  category: {\n    '.': ClassNames.category,\n    ':not(:has(.epr-visible))': {\n      display: 'none'\n    }\n  },\n  categoryContent: {\n    '.': ClassNames.categoryContent,\n    display: 'grid',\n    gridGap: '0',\n    gridTemplateColumns: 'repeat(auto-fill, var(--epr-emoji-fullsize))',\n    justifyContent: 'space-between',\n    margin: 'var(--epr-category-padding)',\n    position: 'relative'\n  },\n  label: {\n    '.': ClassNames.label,\n    alignItems: 'center',\n    // @ts-ignore - backdropFilter is not recognized.\n    backdropFilter: 'blur(3px)',\n    backgroundColor: 'var(--epr-category-label-bg-color)',\n    color: 'var(--epr-category-label-text-color)',\n    display: 'flex',\n    fontSize: '16px',\n    fontWeight: 'bold',\n    height: 'var(--epr-category-label-height)',\n    margin: '0',\n    padding: 'var(--epr-category-label-padding)',\n    position: 'sticky',\n    textTransform: 'capitalize',\n    top: '0',\n    width: '100%',\n    zIndex: 'var(--epr-category-label-z-index)'\n  }\n});\n", "import * as React from 'react';\n\nlet isEverMounted = false;\n\nexport function useIsEverMounted() {\n  const [isMounted, setIsMounted] = React.useState(isEverMounted);\n\n  React.useEffect(() => {\n    setIsMounted(true);\n    isEverMounted = true;\n  }, []);\n\n  return isMounted || isEverMounted;\n}\n", "import * as React from 'react';\n\nimport { CategoryConfig } from '../../config/categoryConfig';\nimport {\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  useSuggestedEmojisModeConfig\n} from '../../config/useConfig';\nimport { emojiByUnified } from '../../dataUtils/emojiSelectors';\nimport { getSuggested } from '../../dataUtils/suggested';\nimport { useIsEmojiDisallowed } from '../../hooks/useDisallowedEmojis';\nimport { useIsEverMounted } from '../../hooks/useIsEverMounted';\nimport { useUpdateSuggested } from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport { EmojiCategory } from './EmojiCategory';\n\ntype Props = Readonly<{\n  categoryConfig: CategoryConfig;\n}>;\n\nexport function Suggested({ categoryConfig }: Props) {\n  const [suggestedUpdated] = useUpdateSuggested();\n  const isMounted = useIsEverMounted();\n  const suggestedEmojisModeConfig = useSuggestedEmojisModeConfig();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n  const suggested = React.useMemo(\n    () => getSuggested(suggestedEmojisModeConfig) ?? [],\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [suggestedUpdated, suggestedEmojisModeConfig]\n  );\n  const emojiStyle = useEmojiStyleConfig();\n  const isEmojiDisallowed = useIsEmojiDisallowed();\n\n  if (!isMounted) {\n    return null;\n  }\n\n  return (\n    <EmojiCategory\n      categoryConfig={categoryConfig}\n      hiddenOnSearch\n      hidden={suggested.length === 0}\n    >\n      {suggested.map(suggestedItem => {\n        const emoji = emojiByUnified(suggestedItem.original);\n\n        if (!emoji) {\n          return null;\n        }\n\n        if (isEmojiDisallowed(emoji)) {\n          return null;\n        }\n\n        return (\n          <ClickableEmoji\n            showVariations={false}\n            unified={suggestedItem.unified}\n            emojiStyle={emojiStyle}\n            emoji={emoji}\n            key={suggestedItem.unified}\n            getEmojiUrl={getEmojiUrl}\n          />\n        );\n      })}\n    </EmojiCategory>\n  );\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  Categories,\n  CategoryConfig,\n  categoryFromCategoryConfig\n} from '../../config/categoryConfig';\nimport {\n  useCategoriesConfig,\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  useLazyLoadEmojisConfig,\n  useSkinTonesDisabledConfig\n} from '../../config/useConfig';\nimport { emojisByCategory, emojiUnified } from '../../dataUtils/emojiSelectors';\nimport { useIsEmojiDisallowed } from '../../hooks/useDisallowedEmojis';\nimport { useIsEmojiHidden } from '../../hooks/useIsEmojiHidden';\nimport {\n  useActiveSkinToneState,\n  useIsPastInitialLoad\n} from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport { EmojiCategory } from './EmojiCategory';\nimport { Suggested } from './Suggested';\n\nexport function EmojiList() {\n  const categories = useCategoriesConfig();\n  const renderdCategoriesCountRef = React.useRef(0);\n\n  return (\n    <ul className={cx(styles.emojiList)}>\n      {categories.map(categoryConfig => {\n        const category = categoryFromCategoryConfig(categoryConfig);\n\n        if (category === Categories.SUGGESTED) {\n          return <Suggested key={category} categoryConfig={categoryConfig} />;\n        }\n\n        return (\n          <React.Suspense key={category}>\n            <RenderCategory\n              category={category}\n              categoryConfig={categoryConfig}\n              renderdCategoriesCountRef={renderdCategoriesCountRef}\n            />\n          </React.Suspense>\n        );\n      })}\n    </ul>\n  );\n}\n\nfunction RenderCategory({\n  category,\n  categoryConfig,\n  renderdCategoriesCountRef\n}: {\n  category: Categories;\n  categoryConfig: CategoryConfig;\n  renderdCategoriesCountRef: React.MutableRefObject<number>;\n}) {\n  const isEmojiHidden = useIsEmojiHidden();\n  const lazyLoadEmojis = useLazyLoadEmojisConfig();\n  const emojiStyle = useEmojiStyleConfig();\n  const isPastInitialLoad = useIsPastInitialLoad();\n  const [activeSkinTone] = useActiveSkinToneState();\n  const isEmojiDisallowed = useIsEmojiDisallowed();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n  const showVariations = !useSkinTonesDisabledConfig();\n\n  // Small trick to defer the rendering of all emoji categories until the first category is visible\n  // This way the user gets to actually see something and not wait for the whole picker to render.\n  const emojisToPush =\n    !isPastInitialLoad && renderdCategoriesCountRef.current > 0\n      ? []\n      : emojisByCategory(category);\n\n  if (emojisToPush.length > 0) {\n    renderdCategoriesCountRef.current++;\n  }\n\n  let hiddenCounter = 0;\n\n  const emojis = emojisToPush.map(emoji => {\n    const unified = emojiUnified(emoji, activeSkinTone);\n    const { failedToLoad, filteredOut, hidden } = isEmojiHidden(emoji);\n\n    const isDisallowed = isEmojiDisallowed(emoji);\n\n    if (hidden || isDisallowed) {\n      hiddenCounter++;\n    }\n\n    if (isDisallowed) {\n      return null;\n    }\n\n    return (\n      <ClickableEmoji\n        showVariations={showVariations}\n        key={unified}\n        emoji={emoji}\n        unified={unified}\n        hidden={failedToLoad}\n        hiddenOnSearch={filteredOut}\n        emojiStyle={emojiStyle}\n        lazyLoad={lazyLoadEmojis}\n        getEmojiUrl={getEmojiUrl}\n      />\n    );\n  });\n\n  return (\n    <EmojiCategory\n      categoryConfig={categoryConfig}\n      // Indicates that there are no visible emojis\n      // Hence, the category should be hidden\n      hidden={hiddenCounter === emojis.length}\n    >\n      {emojis}\n    </EmojiCategory>\n  );\n}\n\nconst styles = stylesheet.create({\n  emojiList: {\n    '.': ClassNames.emojiList,\n    listStyle: 'none',\n    margin: '0',\n    padding: '0'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useEffect } from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport { focusFirstVisibleEmoji } from '../../DomUtils/keyboardNavigation';\nimport {\n  buttonFromTarget,\n  elementHeight,\n  emojiTrueOffsetTop,\n  emojiTruOffsetLeft\n} from '../../DomUtils/selectors';\nimport { darkMode, stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig\n} from '../../config/useConfig';\nimport {\n  emojiHasVariations,\n  emojiUnified,\n  emojiVariations\n} from '../../dataUtils/emojiSelectors';\nimport {\n  useAnchoredEmojiRef,\n  useBodyRef,\n  useSetAnchoredEmojiRef,\n  useVariationPickerRef\n} from '../context/ElementRefContext';\nimport { useEmojiVariationPickerState } from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport SVGTriangle from './svg/triangle.svg';\n\nenum Direction {\n  Up,\n  Down\n}\n\n// eslint-disable-next-line complexity\nexport function EmojiVariationPicker() {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  const VariationPickerRef = useVariationPickerRef();\n  const [emoji] = useEmojiVariationPickerState();\n  const emojiStyle = useEmojiStyleConfig();\n\n  const { getTop, getMenuDirection } = useVariationPickerTop(\n    VariationPickerRef\n  );\n  const setAnchoredEmojiRef = useSetAnchoredEmojiRef();\n  const getPointerStyle = usePointerStyle(VariationPickerRef);\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  const button = buttonFromTarget(AnchoredEmojiRef.current);\n\n  const visible = Boolean(\n    emoji &&\n      button &&\n      emojiHasVariations(emoji) &&\n      button.classList.contains(ClassNames.emojiHasVariations)\n  );\n\n  useEffect(() => {\n    if (!visible) {\n      return;\n    }\n\n    focusFirstVisibleEmoji(VariationPickerRef.current);\n  }, [VariationPickerRef, visible, AnchoredEmojiRef]);\n\n  let top, pointerStyle;\n\n  if (!visible && AnchoredEmojiRef.current) {\n    setAnchoredEmojiRef(null);\n  } else {\n    top = getTop();\n    pointerStyle = getPointerStyle();\n  }\n\n  return (\n    <div\n      ref={VariationPickerRef}\n      className={cx(\n        styles.variationPicker,\n        getMenuDirection() === Direction.Down && styles.pointingUp,\n        visible && styles.visible\n      )}\n      style={{ top }}\n    >\n      {visible && emoji\n        ? [emojiUnified(emoji)]\n            .concat(emojiVariations(emoji))\n            .slice(0, 6)\n            .map(unified => (\n              <ClickableEmoji\n                key={unified}\n                emoji={emoji}\n                unified={unified}\n                emojiStyle={emojiStyle}\n                showVariations={false}\n                getEmojiUrl={getEmojiUrl}\n              />\n            ))\n        : null}\n      <div className={cx(styles.pointer)} style={pointerStyle} />\n    </div>\n  );\n}\n\nfunction usePointerStyle(VariationPickerRef: React.RefObject<HTMLElement>) {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  return function getPointerStyle() {\n    const style: React.CSSProperties = {};\n    if (!VariationPickerRef.current) {\n      return style;\n    }\n\n    if (AnchoredEmojiRef.current) {\n      const button = buttonFromTarget(AnchoredEmojiRef.current);\n\n      const offsetLeft = emojiTruOffsetLeft(button);\n\n      if (!button) {\n        return style;\n      }\n\n      // half of the button\n      style.left = offsetLeft + button?.clientWidth / 2;\n    }\n\n    return style;\n  };\n}\n\nfunction useVariationPickerTop(\n  VariationPickerRef: React.RefObject<HTMLElement>\n) {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  const BodyRef = useBodyRef();\n  let direction = Direction.Up;\n\n  return {\n    getMenuDirection,\n    getTop\n  };\n\n  function getMenuDirection() {\n    return direction;\n  }\n\n  function getTop() {\n    direction = Direction.Up;\n    let emojiOffsetTop = 0;\n\n    if (!VariationPickerRef.current) {\n      return 0;\n    }\n\n    const height = elementHeight(VariationPickerRef.current);\n\n    if (AnchoredEmojiRef.current) {\n      const bodyRef = BodyRef.current;\n      const button = buttonFromTarget(AnchoredEmojiRef.current);\n\n      const buttonHeight = elementHeight(button);\n\n      emojiOffsetTop = emojiTrueOffsetTop(button);\n\n      const scrollTop = bodyRef?.scrollTop ?? 0;\n\n      if (scrollTop > emojiOffsetTop - height) {\n        direction = Direction.Down;\n        emojiOffsetTop += buttonHeight + height;\n      }\n    }\n\n    return emojiOffsetTop - height;\n  }\n}\n\nconst styles = stylesheet.create({\n  variationPicker: {\n    '.': ClassNames.variationPicker,\n    position: 'absolute',\n    right: '15px',\n    left: '15px',\n    padding: '5px',\n    boxShadow: '0px 2px 5px rgba(0, 0, 0, 0.2)',\n    borderRadius: '3px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-around',\n    opacity: '0',\n    visibility: 'hidden',\n    pointerEvents: 'none',\n    top: '-100%',\n    border: '1px solid var(--epr-picker-border-color)',\n    height: 'var(--epr-emoji-variation-picker-height)',\n    zIndex: 'var(--epr-skin-variation-picker-z-index)',\n    background: 'var(--epr-emoji-variation-picker-bg-color)',\n    transform: 'scale(0.9)',\n    transition: 'transform 0.1s ease-out, opacity 0.2s ease-out'\n  },\n  visible: {\n    opacity: '1',\n    visibility: 'visible',\n    pointerEvents: 'all',\n    transform: 'scale(1)'\n  },\n  pointingUp: {\n    '.': 'pointing-up',\n    transformOrigin: 'center 0%',\n    transform: 'scale(0.9)'\n  },\n  '.pointing-up': {\n    pointer: {\n      top: '0',\n      transform: 'rotate(180deg) translateY(100%) translateX(18px)'\n    }\n  },\n  pointer: {\n    '.': 'epr-emoji-pointer',\n    content: '',\n    position: 'absolute',\n    width: '25px',\n    height: '15px',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: '0 0',\n    backgroundSize: '50px 15px',\n    top: '100%',\n    transform: 'translateX(-18px)',\n    backgroundImage: `url(${SVGTriangle})`\n  },\n  ...darkMode('pointer', {\n    backgroundPosition: '-25px 0'\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport { MOUSE_EVENT_SOURCE } from '../../config/useConfig';\nimport { useOnMouseMove } from '../../hooks/useDisallowMouseMove';\nimport { useMouseDownHandlers } from '../../hooks/useMouseDownHandlers';\nimport { useOnScroll } from '../../hooks/useOnScroll';\nimport { useBodyRef } from '../context/ElementRefContext';\n\nimport { EmojiList } from './EmojiList';\nimport { EmojiVariationPicker } from './EmojiVariationPicker';\n\nexport function Body() {\n  const BodyRef = useBodyRef();\n  useOnScroll(BodyRef);\n  useMouseDownHandlers(BodyRef, MOUSE_EVENT_SOURCE.PICKER);\n  useOnMouseMove();\n\n  return (\n    <div\n      className={cx(styles.body, commonInteractionStyles.hiddenOnReactions)}\n      ref={BodyRef}\n    >\n      <EmojiVariationPicker />\n      <EmojiList />\n    </div>\n  );\n}\n\nconst styles = stylesheet.create({\n  body: {\n    '.': ClassNames.scrollBody,\n    flex: '1',\n    overflowY: 'scroll',\n    overflowX: 'hidden',\n    position: 'relative'\n  }\n});\n", "import { NullableElement } from './selectors';\n\nexport function detectEmojyPartiallyBelowFold(\n  button: HTMLButtonElement,\n  bodyRef: NullableElement\n): number {\n  if (!button || !bodyRef) {\n    return 0;\n  }\n\n  const buttonRect = button.getBoundingClientRect();\n  const bodyRect = bodyRef.getBoundingClientRect();\n\n  // If the element is obscured by at least half of its size\n  return bodyRect.height - (buttonRect.y - bodyRect.y);\n}\n", "import * as React from 'react';\nimport { useEffect } from 'react';\n\nimport { detectEmojyPartiallyBelowFold } from '../DomUtils/detectEmojyPartiallyBelowFold';\nimport { focusElement } from '../DomUtils/focusElement';\nimport {\n  allUnifiedFromEmojiElement,\n  buttonFromTarget\n} from '../DomUtils/selectors';\nimport { useBodyRef } from '../components/context/ElementRefContext';\nimport { PreviewEmoji } from '../components/footer/Preview';\n\nimport {\n  useAllowMouseMove,\n  useIsMouseDisallowed\n} from './useDisallowMouseMove';\n\nexport function useEmojiPreviewEvents(\n  allow: boolean,\n  setPreviewEmoji: React.Dispatch<React.SetStateAction<PreviewEmoji>>\n) {\n  const BodyRef = useBodyRef();\n  const isMouseDisallowed = useIsMouseDisallowed();\n  const allowMouseMove = useAllowMouseMove();\n\n  useEffect(() => {\n    if (!allow) {\n      return;\n    }\n    const bodyRef = BodyRef.current;\n\n    bodyRef?.addEventListener('keydown', onEscape, {\n      passive: true\n    });\n\n    bodyRef?.addEventListener('mouseover', onMouseOver, true);\n\n    bodyRef?.addEventListener('focus', onEnter, true);\n\n    bodyRef?.addEventListener('mouseout', onLeave, {\n      passive: true\n    });\n    bodyRef?.addEventListener('blur', onLeave, true);\n\n    function onEnter(e: FocusEvent) {\n      const button = buttonFromTarget(e.target as HTMLElement);\n\n      if (!button) {\n        return onLeave();\n      }\n\n      const { unified, originalUnified } = allUnifiedFromEmojiElement(button);\n\n      if (!unified || !originalUnified) {\n        return onLeave();\n      }\n\n      setPreviewEmoji({\n        unified,\n        originalUnified\n      });\n    }\n    function onLeave(e?: FocusEvent | MouseEvent) {\n      if (e) {\n        const relatedTarget = e.relatedTarget as HTMLElement;\n\n        if (!buttonFromTarget(relatedTarget)) {\n          return setPreviewEmoji(null);\n        }\n      }\n\n      setPreviewEmoji(null);\n    }\n    function onEscape(e: KeyboardEvent) {\n      if (e.key === 'Escape') {\n        setPreviewEmoji(null);\n      }\n    }\n\n    function onMouseOver(e: MouseEvent) {\n      if (isMouseDisallowed()) {\n        return;\n      }\n\n      const button = buttonFromTarget(e.target as HTMLElement);\n\n      if (button) {\n        const belowFoldByPx = detectEmojyPartiallyBelowFold(button, bodyRef);\n        const buttonHeight = button.getBoundingClientRect().height;\n        if (belowFoldByPx < buttonHeight) {\n          return handlePartiallyVisibleElementFocus(button, setPreviewEmoji);\n        }\n\n        focusElement(button);\n      }\n    }\n\n    return () => {\n      bodyRef?.removeEventListener('mouseover', onMouseOver);\n      bodyRef?.removeEventListener('mouseout', onLeave);\n      bodyRef?.removeEventListener('focus', onEnter, true);\n      bodyRef?.removeEventListener('blur', onLeave, true);\n      bodyRef?.removeEventListener('keydown', onEscape);\n    };\n  }, [BodyRef, allow, setPreviewEmoji, isMouseDisallowed, allowMouseMove]);\n}\n\nfunction handlePartiallyVisibleElementFocus(\n  button: HTMLElement,\n  setPreviewEmoji: React.Dispatch<React.SetStateAction<PreviewEmoji>>\n) {\n  const { unified, originalUnified } = allUnifiedFromEmojiElement(button);\n\n  if (!unified || !originalUnified) {\n    return;\n  }\n\n  (document.activeElement as HTMLElement)?.blur?.();\n\n  setPreviewEmoji({\n    unified,\n    originalUnified\n  });\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\n\nexport enum FlexDirection {\n  ROW = 'FlexRow',\n  COLUMN = 'FlexColumn'\n}\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n  direction?: FlexDirection;\n}>;\n\nexport default function Flex({\n  children,\n  className,\n  style = {},\n  direction = FlexDirection.ROW\n}: Props) {\n  return (\n    <div\n      style={{ ...style }}\n      className={cx(styles.flex, className, styles[direction])}\n    >\n      {children}\n    </div>\n  );\n}\n\nconst styles = stylesheet.create({\n  flex: {\n    display: 'flex'\n  },\n  [FlexDirection.ROW]: {\n    flexDirection: 'row'\n  },\n  [FlexDirection.COLUMN]: {\n    flexDirection: 'column'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\n\ntype Props = Readonly<{\n  className?: string;\n  style?: React.CSSProperties;\n}>;\n\nexport default function Space({ className, style = {} }: Props) {\n  return <div style={{ flex: 1, ...style }} className={cx(className)} />;\n}\n", "import * as React from 'react';\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}>;\n\nexport default function Absolute({ children, className, style }: Props) {\n  return (\n    <div style={{ ...style, position: 'absolute' }} className={className}>\n      {children}\n    </div>\n  );\n}\n", "import * as React from 'react';\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}>;\n\nexport default function Relative({ children, className, style }: Props) {\n  return (\n    <div style={{ ...style, position: 'relative' }} className={className}>\n      {children}\n    </div>\n  );\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../../Stylesheet/stylesheet';\nimport { skinTonesNamed } from '../../../data/skinToneVariations';\nimport { SkinTones } from '../../../types/exposedTypes';\nimport { Button } from '../../atoms/Button';\n\ntype Props = {\n  isOpen: boolean;\n  onClick: () => void;\n  isActive: boolean;\n  skinToneVariation: SkinTones;\n  style?: React.CSSProperties;\n};\n\n// eslint-disable-next-line complexity\nexport function BtnSkinToneVariation({\n  isOpen,\n  onClick,\n  isActive,\n  skinToneVariation,\n  style\n}: Props) {\n  return (\n    <Button\n      style={style}\n      onClick={onClick}\n      className={cx(\n        `epr-tone-${skinToneVariation}`,\n        styles.tone,\n        !isOpen && styles.closedTone,\n        isActive && styles.active\n      )}\n      aria-pressed={isActive}\n      aria-label={`Skin tone ${skinTonesNamed[skinToneVariation as SkinTones]}`}\n    ></Button>\n  );\n}\n\nconst styles = stylesheet.create({\n  closedTone: {\n    opacity: '0',\n    zIndex: '0'\n  },\n  active: {\n    '.': 'epr-active',\n    zIndex: '1',\n    opacity: '1'\n  },\n  tone: {\n    '.': 'epr-tone',\n    width: 'var(--epr-skin-tone-size)',\n    display: 'block',\n    cursor: 'pointer',\n    borderRadius: '4px',\n    height: 'var(--epr-skin-tone-size)',\n    position: 'absolute',\n    right: '0',\n    transition: 'transform 0.3s ease-in-out, opacity 0.35s ease-in-out',\n    zIndex: '0',\n    border: '1px solid var(--epr-skin-tone-outer-border-color)',\n    boxShadow: 'inset 0px 0px 0 1px var(--epr-skin-tone-inner-border-color)',\n    ':hover': {\n      boxShadow: '0 0 0 3px var(--epr-active-skin-hover-color), inset 0px 0px 0 1px var(--epr-skin-tone-inner-border-color)'\n    },\n    ':focus': {\n      boxShadow: '0 0 0 3px var(--epr-focus-bg-color)'\n    },\n    '&.epr-tone-neutral': {\n      backgroundColor: '#ffd225'\n    },\n    '&.epr-tone-1f3fb': {\n      backgroundColor: '#ffdfbd'\n    },\n    '&.epr-tone-1f3fc': {\n      backgroundColor: '#e9c197'\n    },\n    '&.epr-tone-1f3fd': {\n      backgroundColor: '#c88e62'\n    },\n    '&.epr-tone-1f3fe': {\n      backgroundColor: '#a86637'\n    },\n    '&.epr-tone-1f3ff': {\n      backgroundColor: '#60463a'\n    }\n  }\n});\n", "/* eslint-disable complexity */\nimport { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../../DomUtils/classNames';\nimport { stylesheet } from '../../../Stylesheet/stylesheet';\nimport {\n  useOnSkinToneChangeConfig,\n  useSkinTonesDisabledConfig\n} from '../../../config/useConfig';\nimport skinToneVariations from '../../../data/skinToneVariations';\nimport { useCloseAllOpenToggles } from '../../../hooks/useCloseAllOpenToggles';\nimport { useFocusSearchInput } from '../../../hooks/useFocus';\nimport Absolute from '../../Layout/Absolute';\nimport Relative from '../../Layout/Relative';\nimport { useSkinTonePickerRef } from '../../context/ElementRefContext';\nimport {\n  useActiveSkinToneState,\n  useSkinToneFanOpenState\n} from '../../context/PickerContext';\n\nimport { BtnSkinToneVariation } from './BtnSkinToneVariation';\n\nconst ITEM_SIZE = 28;\n\ntype Props = {\n  direction?: SkinTonePickerDirection;\n};\n\nexport function SkinTonePickerMenu() {\n  return (\n    <Relative style={{ height: ITEM_SIZE }}>\n      <Absolute style={{ bottom: 0, right: 0 }}>\n        <SkinTonePicker direction={SkinTonePickerDirection.VERTICAL} />\n      </Absolute>\n    </Relative>\n  );\n}\n\nexport function SkinTonePicker({\n  direction = SkinTonePickerDirection.HORIZONTAL\n}: Props) {\n  const SkinTonePickerRef = useSkinTonePickerRef();\n  const isDisabled = useSkinTonesDisabledConfig();\n  const [isOpen, setIsOpen] = useSkinToneFanOpenState();\n  const [activeSkinTone, setActiveSkinTone] = useActiveSkinToneState();\n  const onSkinToneChange = useOnSkinToneChangeConfig();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n  const focusSearchInput = useFocusSearchInput();\n\n  if (isDisabled) {\n    return null;\n  }\n\n  const fullWidth = `${ITEM_SIZE * skinToneVariations.length}px`;\n\n  const expandedSize = isOpen ? fullWidth : ITEM_SIZE + 'px';\n\n  const vertical = direction === SkinTonePickerDirection.VERTICAL;\n\n  return (\n    <Relative\n      className={cx(\n        styles.skinTones,\n        vertical && styles.vertical,\n        isOpen && styles.open,\n        vertical && isOpen && styles.verticalShadow\n      )}\n      style={\n        vertical\n          ? { flexBasis: expandedSize, height: expandedSize }\n          : { flexBasis: expandedSize }\n      }\n    >\n      <div className={cx(styles.select)} ref={SkinTonePickerRef}>\n        {skinToneVariations.map((skinToneVariation, i) => {\n          const active = skinToneVariation === activeSkinTone;\n\n          return (\n            <BtnSkinToneVariation\n              key={skinToneVariation}\n              skinToneVariation={skinToneVariation}\n              isOpen={isOpen}\n              style={{\n                transform: cx(\n                  vertical\n                    ? `translateY(-${i * (isOpen ? ITEM_SIZE : 0)}px)`\n                    : `translateX(-${i * (isOpen ? ITEM_SIZE : 0)}px)`,\n                  isOpen && active && 'scale(1.3)'\n                )\n              }}\n              isActive={active}\n              onClick={() => {\n                if (isOpen) {\n                  setActiveSkinTone(skinToneVariation);\n                  onSkinToneChange(skinToneVariation);\n                  focusSearchInput();\n                } else {\n                  setIsOpen(true);\n                }\n                closeAllOpenToggles();\n              }}\n            />\n          );\n        })}\n      </div>\n    </Relative>\n  );\n}\n\nexport enum SkinTonePickerDirection {\n  VERTICAL = ClassNames.vertical,\n  HORIZONTAL = ClassNames.horizontal\n}\n\nconst styles = stylesheet.create({\n  skinTones: {\n    '.': 'epr-skin-tones',\n    '--': {\n      '--epr-skin-tone-size': '15px'\n    },\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'flex-end',\n    transition: 'all 0.3s ease-in-out',\n    padding: '10px 0'\n  },\n  vertical: {\n    padding: '9px',\n    alignItems: 'flex-end',\n    flexDirection: 'column',\n    borderRadius: '6px',\n    border: '1px solid var(--epr-bg-color)'\n  },\n  verticalShadow: {\n    boxShadow: '0px 0 7px var(--epr-picker-border-color)'\n  },\n  open: {\n    // @ts-ignore - backdropFilter is not recognized.\n    backdropFilter: 'blur(5px)',\n    background: 'var(--epr-skin-tone-picker-menu-color)',\n    '.epr-active': {\n      border: '1px solid var(--epr-active-skin-tone-indicator-border-color)'\n    }\n  },\n  select: {\n    '.': 'epr-skin-tone-select',\n    position: 'relative',\n    width: 'var(--epr-skin-tone-size)',\n    height: 'var(--epr-skin-tone-size)'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useState } from 'react';\n\nimport {\n  commonInteractionStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport {\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  usePreviewConfig\n} from '../../config/useConfig';\nimport {\n  emojiByUnified,\n  emojiName,\n  emojiUnified\n} from '../../dataUtils/emojiSelectors';\nimport { useEmojiPreviewEvents } from '../../hooks/useEmojiPreviewEvents';\nimport { useIsSkinToneInPreview } from '../../hooks/useShouldShowSkinTonePicker';\nimport Flex from '../Layout/Flex';\nimport Space from '../Layout/Space';\nimport {\n  useEmojiVariationPickerState,\n  useReactionsModeState\n} from '../context/PickerContext';\nimport { ViewOnlyEmoji } from '../emoji/ViewOnlyEmoji';\nimport { SkinTonePickerMenu } from '../header/SkinTonePicker/SkinTonePicker';\n\nexport function Preview() {\n  const previewConfig = usePreviewConfig();\n  const isSkinToneInPreview = useIsSkinToneInPreview();\n  const [reactionsOpen] = useReactionsModeState();\n\n  if (!previewConfig.showPreview) {\n    return null;\n  }\n\n  return (\n    <Flex\n      className={cx(\n        styles.preview,\n        commonInteractionStyles.hiddenOnReactions,\n        reactionsOpen && styles.hideOnReactions\n      )}\n    >\n      <PreviewBody />\n      <Space />\n      {isSkinToneInPreview ? <SkinTonePickerMenu /> : null}\n    </Flex>\n  );\n}\n\nexport function PreviewBody() {\n  const previewConfig = usePreviewConfig();\n  const [previewEmoji, setPreviewEmoji] = useState<PreviewEmoji>(null);\n  const emojiStyle = useEmojiStyleConfig();\n  const [variationPickerEmoji] = useEmojiVariationPickerState();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  useEmojiPreviewEvents(previewConfig.showPreview, setPreviewEmoji);\n\n  const emoji = emojiByUnified(\n    previewEmoji?.unified ?? previewEmoji?.originalUnified\n  );\n\n  const show = emoji != null && previewEmoji != null;\n\n  return <PreviewContent />;\n\n  function PreviewContent() {\n    const defaultEmoji =\n      variationPickerEmoji ?? emojiByUnified(previewConfig.defaultEmoji);\n    if (!defaultEmoji) {\n      return null;\n    }\n    const defaultText = variationPickerEmoji\n      ? emojiName(variationPickerEmoji)\n      : previewConfig.defaultCaption;\n\n    return (\n      <>\n        <div>\n          {show ? (\n            <ViewOnlyEmoji\n              unified={previewEmoji?.unified as string}\n              emoji={emoji}\n              emojiStyle={emojiStyle}\n              size={45}\n              getEmojiUrl={getEmojiUrl}\n              className={cx(styles.emoji)}\n            />\n          ) : defaultEmoji ? (\n            <ViewOnlyEmoji\n              unified={emojiUnified(defaultEmoji)}\n              emoji={defaultEmoji}\n              emojiStyle={emojiStyle}\n              size={45}\n              getEmojiUrl={getEmojiUrl}\n              className={cx(styles.emoji)}\n            />\n          ) : null}\n        </div>\n        <div className={cx(styles.label)}>\n          {show ? emojiName(emoji) : defaultText}\n        </div>\n      </>\n    );\n  }\n}\n\nexport type PreviewEmoji = null | {\n  unified: string;\n  originalUnified: string;\n};\n\nconst styles = stylesheet.create({\n  preview: {\n    alignItems: 'center',\n    borderTop: '1px solid var(--epr-preview-border-color)',\n    height: 'var(--epr-preview-height)',\n    padding: '0 var(--epr-horizontal-padding)',\n    position: 'relative',\n    zIndex: 'var(--epr-preview-z-index)'\n  },\n  label: {\n    color: 'var(--epr-preview-text-color)',\n    fontSize: 'var(--epr-preview-text-size)',\n    padding: 'var(--epr-preview-text-padding)',\n    textTransform: 'capitalize'\n  },\n  emoji: {\n    padding: '0'\n  },\n  hideOnReactions: {\n    opacity: '0',\n    transition: 'opacity 0.5s ease-in-out'\n  }\n});\n", "export function categoryNameFromDom($category: Element | null): string | null {\n  return $category?.getAttribute('data-name') ?? null;\n}\n", "import { useEffect } from 'react';\n\nimport { categoryNameFromDom } from '../DomUtils/categoryNameFromDom';\nimport { asSelectors, ClassNames } from '../DomUtils/classNames';\nimport { useBodyRef } from '../components/context/ElementRefContext';\n\nexport function useActiveCategoryScrollDetection(\n  setActiveCategory: (category: string) => void\n) {\n  const BodyRef = useBodyRef();\n\n  useEffect(() => {\n    const visibleCategories = new Map();\n    const bodyRef = BodyRef.current;\n    const observer = new IntersectionObserver(\n      entries => {\n        if (!bodyRef) {\n          return;\n        }\n\n        for (const entry of entries) {\n          const id = categoryNameFromDom(entry.target);\n          visibleCategories.set(id, entry.intersectionRatio);\n        }\n\n        const ratios = Array.from(visibleCategories);\n        const lastCategory = ratios[ratios.length - 1];\n\n        if (lastCategory[1] == 1) {\n          return setActiveCategory(lastCategory[0]);\n        }\n\n        for (const [id, ratio] of ratios) {\n          if (ratio) {\n            setActiveCategory(id);\n            break;\n          }\n        }\n      },\n      {\n        threshold: [0, 1]\n      }\n    );\n    bodyRef?.querySelectorAll(asSelectors(ClassNames.category)).forEach(el => {\n      observer.observe(el);\n    });\n  }, [BodyRef, setActiveCategory]);\n}\n", "import { scrollTo } from '../DomUtils/scrollTo';\nimport { NullableElement } from '../DomUtils/selectors';\nimport {\n  useBodyRef,\n  usePickerMainRef\n} from '../components/context/ElementRefContext';\n\nexport function useScrollCategoryIntoView() {\n  const BodyRef = useBodyRef();\n  const PickerMainRef = usePickerMainRef();\n\n  return function scrollCategoryIntoView(category: string): void {\n    if (!BodyRef.current) {\n      return;\n    }\n    const $category = BodyRef.current?.querySelector(\n      `[data-name=\"${category}\"]`\n    ) as NullableElement;\n\n    if (!$category) {\n      return;\n    }\n\n    const offsetTop = $category.offsetTop || 0;\n\n    scrollTo(PickerMainRef.current, offsetTop);\n  };\n}\n", "import { useCustomEmojisConfig } from '../config/useConfig';\n\nexport function useShouldHideCustomEmojis() {\n  const customCategoryConfig = useCustomEmojisConfig();\n\n  if (!customCategoryConfig) {\n    return false;\n  }\n\n  return customCategoryConfig.length === 0;\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  darkMode,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport {\n  CategoryConfig,\n  categoryNameFromCategoryConfig\n} from '../../config/categoryConfig';\nimport { Button } from '../atoms/Button';\n\nimport SVGNavigation from './svg/CategoryNav.svg';\n\ntype Props = {\n  isActiveCategory: boolean;\n  category: string;\n  allowNavigation: boolean;\n  onClick: () => void;\n  categoryConfig: CategoryConfig;\n};\n\nexport function CategoryButton({\n  isActiveCategory,\n  category,\n  allowNavigation,\n  categoryConfig,\n  onClick\n}: Props) {\n  return (\n    <Button\n      tabIndex={allowNavigation ? 0 : -1}\n      className={cx(\n        styles.catBtn,\n        commonInteractionStyles.categoryBtn,\n        `epr-icn-${category}`,\n        {\n          [ClassNames.active]: isActiveCategory\n        }\n      )}\n      onClick={onClick}\n      aria-label={categoryNameFromCategoryConfig(categoryConfig)}\n      aria-selected={isActiveCategory}\n      role=\"tab\"\n      aria-controls=\"epr-category-nav-id\"\n    />\n  );\n}\n\nconst DarkActivePositionY = {\n  backgroundPositionY: 'calc(var(--epr-category-navigation-button-size) * 3)'\n};\nconst DarkPositionY = {\n  backgroundPositionY: 'calc(var(--epr-category-navigation-button-size) * 2)'\n};\n\nconst DarkInactivePosition = {\n  ':not(.epr-search-active)': {\n    catBtn: {\n      ':hover': DarkActivePositionY,\n      '&.epr-active': DarkActivePositionY\n    }\n  }\n};\n\nconst styles = stylesheet.create({\n  catBtn: {\n    '.': 'epr-cat-btn',\n    display: 'inline-block',\n    transition: 'opacity 0.2s ease-in-out',\n    position: 'relative',\n    height: 'var(--epr-category-navigation-button-size)',\n    width: 'var(--epr-category-navigation-button-size)',\n    backgroundSize: 'calc(var(--epr-category-navigation-button-size) * 10)',\n    outline: 'none',\n    backgroundPosition: '0 0',\n    backgroundImage: `url(${SVGNavigation})`,\n    ':focus:before': {\n      content: '',\n      position: 'absolute',\n      top: '-2px',\n      left: '-2px',\n      right: '-2px',\n      bottom: '-2px',\n      border: '2px solid var(--epr-category-icon-active-color)',\n      borderRadius: '50%'\n    },\n    '&.epr-icn-suggested': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -8)'\n    },\n    '&.epr-icn-custom': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -9)'\n    },\n    '&.epr-icn-activities': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -4)'\n    },\n    '&.epr-icn-animals_nature': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -1)'\n    },\n    '&.epr-icn-flags': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -7)'\n    },\n    '&.epr-icn-food_drink': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -2)'\n    },\n    '&.epr-icn-objects': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -5)'\n    },\n    '&.epr-icn-smileys_people': {\n      backgroundPositionX: '0px'\n    },\n    '&.epr-icn-symbols': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -6)'\n    },\n    '&.epr-icn-travel_places': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -3)'\n    }\n  },\n  ...darkMode('catBtn', DarkPositionY),\n  '.epr-dark-theme': {\n    ...DarkInactivePosition\n  },\n  '.epr-auto-theme': {\n    ...DarkInactivePosition\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useState } from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport { categoryFromCategoryConfig } from '../../config/categoryConfig';\nimport { useCategoriesConfig } from '../../config/useConfig';\nimport { useActiveCategoryScrollDetection } from '../../hooks/useActiveCategoryScrollDetection';\nimport useIsSearchMode from '../../hooks/useIsSearchMode';\nimport { useScrollCategoryIntoView } from '../../hooks/useScrollCategoryIntoView';\nimport { useShouldHideCustomEmojis } from '../../hooks/useShouldHideCustomEmojis';\nimport { isCustomCategory } from '../../typeRefinements/typeRefinements';\nimport { useCategoryNavigationRef } from '../context/ElementRefContext';\n\nimport { CategoryButton } from './CategoryButton';\n\nexport function CategoryNavigation() {\n  const [activeCategory, setActiveCategory] = useState<string | null>(null);\n  const scrollCategoryIntoView = useScrollCategoryIntoView();\n  useActiveCategoryScrollDetection(setActiveCategory);\n  const isSearchMode = useIsSearchMode();\n\n  const categoriesConfig = useCategoriesConfig();\n  const CategoryNavigationRef = useCategoryNavigationRef();\n  const hideCustomCategory = useShouldHideCustomEmojis();\n\n  return (\n    <div\n      className={cx(styles.nav)}\n      role=\"tablist\"\n      aria-label=\"Category navigation\"\n      id=\"epr-category-nav-id\"\n      ref={CategoryNavigationRef}\n    >\n      {categoriesConfig.map(categoryConfig => {\n        const category = categoryFromCategoryConfig(categoryConfig);\n        const isActiveCategory = category === activeCategory;\n\n        if (isCustomCategory(categoryConfig) && hideCustomCategory) {\n          return null;\n        }\n\n        const allowNavigation = !isSearchMode && !isActiveCategory;\n\n        return (\n          <CategoryButton\n            key={category}\n            category={category}\n            isActiveCategory={isActiveCategory}\n            allowNavigation={allowNavigation}\n            categoryConfig={categoryConfig}\n            onClick={() => {\n              scrollCategoryIntoView(category);\n              setTimeout(() => {\n                setActiveCategory(category);\n              }, 10);\n            }}\n          />\n        );\n      })}\n    </div>\n  );\n}\n\nconst styles = stylesheet.create({\n  nav: {\n    '.': 'epr-category-nav',\n    display: 'flex',\n    flexDirection: 'row',\n    justifyContent: 'space-around',\n    padding: 'var(--epr-header-padding)'\n  },\n  '.epr-search-active': {\n    nav: {\n      opacity: '0.3',\n      cursor: 'default',\n      pointerEvents: 'none'\n    }\n  },\n  '.epr-main:has(input:not(:placeholder-shown))': {\n    nav: {\n      opacity: '0.3',\n      cursor: 'default',\n      pointerEvents: 'none'\n    }\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport {\n  commonInteractionStyles,\n  darkMode,\n  stylesheet\n} from '../../../Stylesheet/stylesheet';\nimport { useClearSearch } from '../../../hooks/useFilter';\nimport { Button } from '../../atoms/Button';\n\nimport SVGTimes from './svg/times.svg';\n\nexport function BtnClearSearch() {\n  const clearSearch = useClearSearch();\n\n  return (\n    <Button\n      className={cx(\n        styles.btnClearSearch,\n        commonInteractionStyles.visibleOnSearchOnly\n      )}\n      onClick={clearSearch}\n      aria-label=\"Clear\"\n      title=\"Clear\"\n    >\n      <div className={cx(styles.icnClearnSearch)} />\n    </Button>\n  );\n}\n\nconst HoverDark = {\n  ':hover': {\n    '> .epr-icn-clear-search': {\n      backgroundPositionY: '-60px'\n    }\n  }\n};\n\nconst styles = stylesheet.create({\n  btnClearSearch: {\n    '.': 'epr-btn-clear-search',\n    position: 'absolute',\n    right: 'var(--epr-search-bar-inner-padding)',\n    height: '30px',\n    width: '30px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    top: '50%',\n    transform: 'translateY(-50%)',\n    padding: '0',\n    borderRadius: '50%',\n    ':hover': {\n      background: 'var(--epr-hover-bg-color)'\n    },\n    ':focus': {\n      background: 'var(--epr-hover-bg-color)'\n    }\n  },\n  icnClearnSearch: {\n    '.': 'epr-icn-clear-search',\n    backgroundColor: 'transparent',\n    backgroundRepeat: 'no-repeat',\n    backgroundSize: '20px',\n    height: '20px',\n    width: '20px',\n    backgroundImage: `url(${SVGTimes})`,\n    ':hover': {\n      backgroundPositionY: '-20px'\n    },\n    ':focus': {\n      backgroundPositionY: '-20px'\n    }\n  },\n  ...darkMode('icnClearnSearch', {\n    backgroundPositionY: '-40px'\n  }),\n  ...darkMode('btnClearSearch', HoverDark)\n});\n", "import * as React from 'react';\n\nimport { ClassNames, asSelectors } from '../../../DomUtils/classNames';\nimport { getNormalizedSearchTerm } from '../../../hooks/useFilter';\n\nconst SCOPE = `${asSelectors(ClassNames.emojiPicker)} ${asSelectors(\n  ClassNames.emojiList\n)}`;\n\nconst EMOJI_BUTTON = ['button', asSelectors(ClassNames.emoji)].join('');\nconst CATEGORY = asSelectors(ClassNames.category);\n\nexport function CssSearch({ value }: { value: undefined | string }) {\n  if (!value) {\n    return null;\n  }\n\n  const q = genQuery(value);\n\n  return (\n    <style>{`\n    ${SCOPE} ${EMOJI_BUTTON} {\n      display: none;\n    }\n\n\n    ${SCOPE} ${q} {\n      display: flex;\n    }\n\n    ${SCOPE} ${CATEGORY}:not(:has(${q})) {\n      display: none;\n    }\n  `}</style>\n  );\n}\n\nfunction genQuery(value: string): string {\n  return [\n    EMOJI_BUTTON,\n    '[data-full-name*=\"',\n    getNormalizedSearchTerm(value),\n    '\"]'\n  ].join('');\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { darkMode, stylesheet } from '../../../Stylesheet/stylesheet';\n\nimport SVGMagnifier from './svg/magnifier.svg';\n\nexport function IcnSearch() {\n  return <div className={cx(styles.icnSearch)} />;\n}\n\nconst styles = stylesheet.create({\n  icnSearch: {\n    '.': 'epr-icn-search',\n    content: '',\n    position: 'absolute',\n    top: '50%',\n    left: 'var(--epr-search-bar-inner-padding)',\n    transform: 'translateY(-50%)',\n    width: '20px',\n    height: '20px',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: '0 0',\n    backgroundSize: '20px',\n    backgroundImage: `url(${SVGMagnifier})`\n  },\n  ...darkMode('icnSearch', {\n    backgroundPositionY: '-20px'\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useState } from 'react';\n\nimport { darkMode, stylesheet } from '../../../Stylesheet/stylesheet';\nimport {\n  useAutoFocusSearchConfig,\n  useSearchDisabledConfig,\n  useSearchPlaceHolderConfig\n} from '../../../config/useConfig';\nimport { useCloseAllOpenToggles } from '../../../hooks/useCloseAllOpenToggles';\nimport { useFilter } from '../../../hooks/useFilter';\nimport { useIsSkinToneInSearch } from '../../../hooks/useShouldShowSkinTonePicker';\nimport Flex from '../../Layout/Flex';\nimport Relative from '../../Layout/Relative';\nimport { useSearchInputRef } from '../../context/ElementRefContext';\nimport { SkinTonePicker } from '../SkinTonePicker/SkinTonePicker';\n\nimport { BtnClearSearch } from './BtnClearSearch';\nimport { CssSearch } from './CssSearch';\nimport { IcnSearch } from './IcnSearch';\nimport SVGTimes from './svg/times.svg';\n\nexport function SearchContainer() {\n  const searchDisabled = useSearchDisabledConfig();\n\n  const isSkinToneInSearch = useIsSkinToneInSearch();\n\n  if (searchDisabled) {\n    return null;\n  }\n\n  return (\n    <Flex className={cx(styles.overlay)}>\n      <Search />\n\n      {isSkinToneInSearch ? <SkinTonePicker /> : null}\n    </Flex>\n  );\n}\n\nexport function Search() {\n  const [inc, setInc] = useState(0);\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n  const SearchInputRef = useSearchInputRef();\n  const placeholder = useSearchPlaceHolderConfig();\n  const autoFocus = useAutoFocusSearchConfig();\n  const { statusSearchResults, searchTerm, onChange } = useFilter();\n\n  const input = SearchInputRef?.current;\n  const value = input?.value;\n\n  return (\n    <Relative className={cx(styles.searchContainer)}>\n      <CssSearch value={value} />\n      <input\n        // eslint-disable-next-line jsx-a11y/no-autofocus\n        autoFocus={autoFocus}\n        aria-label={'Type to search for an emoji'}\n        onFocus={closeAllOpenToggles}\n        className={cx(styles.search)}\n        type=\"text\"\n        aria-controls=\"epr-search-id\"\n        placeholder={placeholder}\n        onChange={event => {\n          setInc(inc + 1);\n          setTimeout(() => {\n            onChange(event?.target?.value ?? value);\n          });\n        }}\n        ref={SearchInputRef}\n      />\n      {searchTerm ? (\n        <div\n          role=\"status\"\n          className={cx('epr-status-search-results', styles.visuallyHidden)}\n          aria-live=\"polite\"\n          id=\"epr-search-id\"\n          aria-atomic=\"true\"\n        >\n          {statusSearchResults}\n        </div>\n      ) : null}\n      <IcnSearch />\n      <BtnClearSearch />\n    </Relative>\n  );\n}\n\nconst styles = stylesheet.create({\n  overlay: {\n    padding: 'var(--epr-header-padding)',\n    zIndex: 'var(--epr-header-overlay-z-index)'\n  },\n  searchContainer: {\n    '.': 'epr-search-container',\n    flex: '1',\n    display: 'block',\n    minWidth: '0'\n  },\n  visuallyHidden: {\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(50%)',\n    height: '1px',\n    overflow: 'hidden',\n    position: 'absolute',\n    whiteSpace: 'nowrap',\n    width: '1px'\n  },\n  search: {\n    outline: 'none',\n    transition: 'all 0.2s ease-in-out',\n    color: 'var(--epr-search-input-text-color)',\n    borderRadius: 'var(--epr-search-input-border-radius)',\n    padding: 'var(--epr-search-input-padding)',\n    height: 'var(--epr-search-input-height)',\n    backgroundColor: 'var(--epr-search-input-bg-color)',\n    border: '1px solid var(--epr-search-input-bg-color)',\n    width: '100%',\n    ':focus': {\n      backgroundColor: 'var(--epr-search-input-bg-color-active)',\n      border: '1px solid var(--epr-search-border-color)'\n    },\n    '::placeholder': {\n      color: 'var(--epr-search-input-placeholder-color)'\n    }\n  },\n\n  btnClearSearch: {\n    '.': 'epr-btn-clear-search',\n    position: 'absolute',\n    right: 'var(--epr-search-bar-inner-padding)',\n    height: '30px',\n    width: '30px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    top: '50%',\n    transform: 'translateY(-50%)',\n    padding: '0',\n    borderRadius: '50%',\n    ':hover': {\n      background: 'var(--epr-hover-bg-color)'\n    },\n    ':focus': {\n      background: 'var(--epr-hover-bg-color)'\n    }\n  },\n  icnClearnSearch: {\n    '.': 'epr-icn-clear-search',\n    backgroundColor: 'transparent',\n    backgroundRepeat: 'no-repeat',\n    backgroundSize: '20px',\n    height: '20px',\n    width: '20px',\n    backgroundImage: `url(${SVGTimes})`,\n    ':hover': {\n      backgroundPositionY: '-20px'\n    },\n    ':focus': {\n      backgroundPositionY: '-20px'\n    }\n  },\n  ...darkMode('icnClearnSearch', {\n    backgroundPositionY: '-40px'\n  }),\n  ...darkMode('btnClearSearch', {\n    ':hover > .epr-icn-clear-search': {\n      backgroundPositionY: '-60px'\n    }\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { commonInteractionStyles } from '../../Stylesheet/stylesheet';\nimport Relative from '../Layout/Relative';\nimport { CategoryNavigation } from '../navigation/CategoryNavigation';\n\nimport { SearchContainer } from './Search/Search';\n\nexport function Header() {\n  return (\n    <Relative\n      className={cx('epr-header', commonInteractionStyles.hiddenOnReactions)}\n    >\n      <SearchContainer />\n      <CategoryNavigation />\n    </Relative>\n  );\n}\n", "import * as React from 'react';\n\nimport { PickerStyleTag } from './Stylesheet/stylesheet';\nimport { Reactions } from './components/Reactions/Reactions';\nimport { Body } from './components/body/Body';\nimport { ElementRefContextProvider } from './components/context/ElementRefContext';\nimport { PickerConfigProvider } from './components/context/PickerConfigContext';\nimport { useReactionsModeState } from './components/context/PickerContext';\nimport { Preview } from './components/footer/Preview';\nimport { Header } from './components/header/Header';\nimport PickerMain from './components/main/PickerMain';\nimport { compareConfig } from './config/compareConfig';\nimport { useAllowExpandReactions, useOpenConfig } from './config/useConfig';\n\nimport { PickerProps } from './index';\n\nfunction EmojiPicker(props: PickerProps) {\n  return (\n    <ElementRefContextProvider>\n      <PickerStyleTag />\n      <PickerConfigProvider {...props}>\n        <ContentControl />\n      </PickerConfigProvider>\n    </ElementRefContextProvider>\n  );\n}\n\nfunction ContentControl() {\n  const [reactionsDefaultOpen] = useReactionsModeState();\n  const allowExpandReactions = useAllowExpandReactions();\n\n  const [renderAll, setRenderAll] = React.useState(!reactionsDefaultOpen);\n  const isOpen = useOpenConfig();\n\n  React.useEffect(() => {\n    if (reactionsDefaultOpen && !allowExpandReactions) {\n      return;\n    }\n\n    if (!renderAll) {\n      setRenderAll(true);\n    }\n  }, [renderAll, allowExpandReactions, reactionsDefaultOpen]);\n\n  if (!isOpen) {\n    return null;\n  }\n\n  return (\n    <PickerMain>\n      <Reactions />\n      <ExpandedPickerContent renderAll={renderAll} />\n    </PickerMain>\n  );\n}\n\nfunction ExpandedPickerContent({ renderAll }: { renderAll: boolean }) {\n  if (!renderAll) {\n    return null;\n  }\n\n  return (\n    <>\n      <Header />\n      <Body />\n      <Preview />\n    </>\n  );\n}\n\n// eslint-disable-next-line complexity\nexport default React.memo(EmojiPicker, compareConfig);\n", "import * as React from 'react';\n\nexport default class ErrorBoundary extends React.Component<\n  { children: React.ReactNode },\n  { hasError: boolean }\n> {\n  constructor(props: { children: React.ReactNode }) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError() {\n    return { hasError: true };\n  }\n\n  componentDidCatch(error: Error, errorInfo: any) {\n    // eslint-disable-next-line no-console\n    console.error('Emoji Picker React failed to render:', error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return null;\n    }\n\n    return this.props.children;\n  }\n}\n", "import * as React from 'react';\n\nimport { EmojiStyle } from '../../types/exposedTypes';\n\nimport { GetEmojiUrl } from './BaseEmojiProps';\nimport { ViewOnlyEmoji } from './ViewOnlyEmoji';\n\nexport function ExportedEmoji({\n  unified,\n  size = 32,\n  emojiStyle = EmojiStyle.APPLE,\n  lazyLoad = false,\n  getEmojiUrl,\n  emojiUrl\n}: {\n  unified: string;\n  emojiStyle?: EmojiStyle;\n  size?: number;\n  lazyLoad?: boolean;\n  getEmojiUrl?: GetEmojiUrl;\n  emojiUrl?: string;\n}) {\n  if (!unified && !emojiUrl && !getEmojiUrl) {\n    return null;\n  }\n\n  return (\n    <ViewOnlyEmoji\n      unified={unified}\n      size={size}\n      emojiStyle={emojiStyle}\n      lazyLoad={lazyLoad}\n      getEmojiUrl={emojiUrl ? () => emojiUrl : getEmojiUrl}\n    />\n  );\n}\n", "import * as React from 'react';\n\nimport EmojiPickerReact from './EmojiPickerReact';\nimport ErrorBoundary from './components/ErrorBoundary';\nimport { PickerConfig } from './config/config';\nimport {\n  MutableConfigContext,\n  useDefineMutableConfig\n} from './config/mutableConfig';\n\nexport { ExportedEmoji as Emoji } from './components/emoji/ExportedEmoji';\n\nexport {\n  EmojiStyle,\n  SkinTones,\n  Theme,\n  Categories,\n  EmojiClickData,\n  SuggestionMode,\n  SkinTonePickerLocation\n} from './types/exposedTypes';\n\nexport interface PickerProps extends PickerConfig {}\n\nexport default function EmojiPicker(props: PickerProps) {\n  const MutableConfigRef = useDefineMutableConfig({\n    onEmojiClick: props.onEmojiClick,\n    onReactionClick: props.onReactionClick,\n    onSkinToneChange: props.onSkinToneChange,\n  });\n\n  return (\n    <ErrorBoundary>\n      <MutableConfigContext.Provider value={MutableConfigRef}>\n        <EmojiPickerReact {...props} />\n      </MutableConfigContext.Provider>\n    </ErrorBoundary>\n  );\n}\n"], "mappings": ";;;;;;;;;;;AAAO,SAAS,QAAW,GAAiB;AAC1C,SAAO,CAAC,EAAE,OAAO,CAAkB;AACrC;ACAO,SAAS,iBAAiB,UAA2B;AAC1D,SAAO,SAAS,WAAW,GAAG;AAChC;AAEO,SAAS,iBAAiB,UAA2B;AAC1D,SACE,SAAS,QAAQ,MAChB,aAAa,OACX,SAAS,SAAS,KAAK,SAAS,SAAS,SAAS,MAAM,GAAG,CAAC,CAAC,KAC9D,yBAAyB,QAAQ;AAEvC;AAEO,SAAS,gBACd,UACA,OACiB;AACjB,UACG,SAAS,KAAK,KAAK,OAAO,UAAU,aACrC,CAAC,eAAe,QAAQ,KACxB,CAAC,iBAAiB,QAAQ,KAC1B,CAAC,aAAa,QAAQ;AAE1B;AAEO,SAAS,aAAa,UAA2B;AACtD,SAAO,SAAS,WAAW,QAAQ;AACrC;AAEO,SAAS,cAAc,UAA2B;AACvD,SAAO,aAAa;AACtB;AAEO,SAAS,eAAe,UAA2B;AACxD,SAAO,aAAa;AACtB;AAEO,SAAS,SAAS,OAAiC;AACxD,SAAO,QAAQ,OAAO;AACxB;AAMO,SAAS,yBACd,OACuB;AACvB,SAAO,SAAS,KAAK,MAAM,MAAM,WAAW,GAAG,KAAK,iBAAiB,KAAK;AAC5E;ACnDO,SAAS,WAAW,KAAgB,YAAoB,IAAY;AACzE,SAAO,IAAI,OAAO,OAAO,EAAE,KAAK,SAAS;AAC3C;ACDO,SAAS,WAAW,QAAgB,MAAsB;AAC/D,MAAI,OAAO;AACX,MAAI,KAAK,WAAW;AAAG,WAAO,KAAK,SAAS;AAC5C,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAM,OAAO,KAAK,WAAW,CAAC;AAC9B,YAAQ,QAAQ,KAAK,OAAO;AAC5B,WAAO,OAAO;EAChB;AACA,SAAO,GAAG,UAAU,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;AAC/C;ACTO,SAAS,oBAAoB,UAAkB,OAAuB;AAC3E,MAAI,aAAa,WAAW;AAC1B,WAAO,IAAI,KAAK;EAClB;AAEA,SAAO;AACT;AAEO,SAAS,gBAAgB,KAAqB;AACnD,SAAO,IAAI,QAAQ,mBAAmB,OAAO,EAAE,YAAY;AAC7D;AAEO,SAAS,eAAe,UAAkB,OAAuB;AACtE,SAAO,GAAG,QAAQ,IAAI,KAAK;AAC7B;AAEO,SAAS,QAAQ,KAAqB;AAC3C,SAAO,MAAM,IAAI,GAAG,KAAK;AAC3B;AAEO,SAAS,aAAa,MAAc,MAAsB;AAC/D,SAAO,OAAO,GAAG,IAAI;EAAK,IAAI,KAAK;AACrC;ACXO,IAAM,OAAN,MAAM,MAAK;EAKhB,YACU,OACD,UACA,OACC,UACR;AAJQ,SAAA,QAAA;AACD,SAAA,WAAA;AACA,SAAA,QAAA;AACC,SAAA,WAAA;AAER,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,SAAS,eAAe,UAAU,KAAK;AAC5C,UAAM,mBAAmB,KAAK,SAAS,cAAc;MACnD,KAAK,SAAS;IAChB;AACA,SAAK,OAAO,KAAK,SAAS,gBACrB,KAAK,SAAS,iBACf,WAAW,KAAK,MAAM,MAAM,KAAK,MAAM;AAC3C,SAAK,MAAM,WAAW,CAAC,KAAK,QAAQ,kBAAkB,KAAK,IAAI,CAAC;EAClE;EAEO,WAAmB;AACxB,QAAI,YAAY,eAAe,KAAK,SAAS,eAAe;MAC1D,OAAO,KAAK;IACd,CAAC;AAED,gBAAY,eAAe,KAAK,SAAS,gBAAgB;MACvD,MAAM;IACR,CAAC;AAED,WAAO,GAAG,SAAS,KAAK,MAAK,QAAQ,KAAK,UAAU,KAAK,KAAK,CAAC;EACjE;EAEA,OAAO,QAAQ,UAAkB,OAAuB;AACtD,UAAM,sBAAsB,gBAAgB,QAAQ;AACpD,WACE;MACE;MACA,oBAAoB,UAAU,KAAK;IACrC,IAAI;EAER;AACF;AAEO,SAAS,eACd,WACA,EAAE,OAAO,IAAI,QAAQ,GAAG,IAAuC,CAAC,GACxD;AACR,QAAM,SAAS,UAAU,OAAO,CAACA,YAAW,YAAY;AACtD,QAAI,iBAAiB,OAAO,GAAG;AAC7B,aAAOA,aAAY;IACrB;AAEA,QAAI,yBAAyB,OAAO,GAAG;AACrC,aAAOA,aAAY,QAAQ,MAAM,CAAC;IACpC;AAEA,WAAO,WAAW,CAACA,YAAW,OAAO,GAAG,GAAG;EAG7C,GAAG,IAAI;AAGP,SAAO,WAAW,CAAC,QAAQ,QAAQ,KAAK,CAAC,GAAG,GAAG;AACjD;AAEO,IAAM,WAAN,MAAM,UAAS;EAMpB,YACU,OACR,YAA2B,MAC3B;IACE;IACA;EACF,IAGI,CAAC,GACL;AATQ,SAAA,QAAA;AANV,SAAO,gBAA0B,CAAC;AAClC,SAAO,iBAAgC;AACvC,SAAO,YAA2B;AAClC,SAAO,iBAA2B,CAAC;AAajC,SAAK,gBAAgB,gBAAgB,QAAQ,aAAa,IAAI,CAAC;AAC/D,SAAK,iBAAiB,iBAAiB,QAAQ,cAAc,IAAI,CAAC;AAClE,SAAK,SAAS,SAAS;EACzB;EAEQ,SAAS,WAAoC;AACnD,QAAI,CAAC,WAAW;AACd,aAAO;IACT;AAEA,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,YAAY;AACjB,WAAK,iBAAiB;QACpB,KAAK,MAAM;;QAEX,YAAY,KAAK,MAAM;MACzB;IACF;AAEA,WAAO;EACT;EAEA,IAAI,gBAAyB;AAC3B,WAAO,KAAK,cAAc,SAAS,KAAK,KAAK,eAAe,SAAS;EACvE;EAEA,SAAS,WAA6B;AACpC,WAAO,IAAI,UAAS,KAAK,OAAO,WAAW;MACzC,eAAe,KAAK;MACpB,gBAAgB,KAAK;IACvB,CAAC;EACH;EAEA,gBAAgB,cAAgC;AAC9C,WAAO,IAAI,UAAS,KAAK,OAAO,KAAK,gBAAgB;MACnD,gBAAgB,KAAK;MACrB,eAAe,KAAK,cAAc,OAAO,YAAY;IACvD,CAAC;EACH;EAEA,iBAAiB,eAAiC;AAChD,WAAO,IAAI,UAAS,KAAK,OAAO,KAAK,gBAAgB;MACnD,eAAe,KAAK;MACpB,gBAAgB,KAAK,eAAe,OAAO,aAAa;IAC1D,CAAC;EACH;EAEA,WAAW,UAAkB,OAAqB;AAChD,WAAO,IAAI,KAAK,KAAK,OAAO,UAAU,OAAO,IAAI;EACnD;AACF;AC9IO,IAAM,QAAN,MAAY;EAYjB,YACS,MACC,UACR;AAFO,SAAA,OAAA;AACC,SAAA,WAAA;AAVV,SAAQ,eAA6B,CAAC;AAGtC,SAAQ,gBAAwC,CAAC;AACjD,SAAQ,QAAgB;AACxB,SAAO,QAAQ;AAOb,SAAK,KAAK,WAAW,IAAI;AAEzB,SAAK,WAAW,KAAK,eAAe;EACtC;EAEA,WAAmB;AACjB,WAAO,KAAK;EACd;EAEA,OAAO,KAAmB;AACxB,SAAK,QAAQ,aAAa,KAAK,OAAO,GAAG;EAC3C;EAEA,QAAc;AACZ,SAAK;AAEL,QAAI,CAAC,KAAK,UAAU;AAClB;IACF;AAEA,SAAK,SAAS,YAAY,KAAK;EACjC;EAEA,YAAqB;AACnB,WAAO,CAAC,CAAC,KAAK;EAChB;EAEA,iBAA+C;AAE7C,QACE,OAAO,aAAa,eACpB,KAAK,UAAU;IAEf,KAAK,aAAa,MAClB;AACA,aAAO,KAAK;IACd;AAEA,UAAM,WAAW,SAAS,cAAc,OAAO;AAC/C,aAAS,OAAO;AAChB,aAAS,KAAK,KAAK;AACnB,KAAC,KAAK,YAAY,SAAS,MAAM,YAAY,QAAQ;AACrD,WAAO;EACT;EAEA,QAAQ,MAAoB;AAC1B,UAAM,cAAc,KAAK,cAAc,KAAK,GAAG;AAE/C,QAAI,SAAS,WAAW,GAAG;AACzB,aAAO;IACT;AAEA,SAAK,cAAc,KAAK,GAAG,IAAI,KAAK;AACpC,SAAK,aAAa,KAAK,IAAI,IAAI,CAAC,KAAK,UAAU,KAAK,KAAK;AAEzD,SAAK,OAAO,KAAK,SAAS,CAAC;AAC3B,WAAO,KAAK;EACd;AACF;AC/EO,SAAS,MACd,KACA,IACM;AACN,aAAW,OAAO,KAAK;AACrB,OAAG,IAAI,KAAK,GAAG,IAAI,GAAG,CAAC;EACzB;AACF;ACLO,SAAS,MAAM,MAAyB;AAC7C,QAAM,UAAU,KAAK,OAAO,CAACC,UAAmB,QAAQ;AACtD,QAAI,eAAe,KAAK;AACtBA,eAAQ,KAAK,GAAG,GAAG;IACrB,WAAW,OAAO,QAAQ,UAAU;AAClCA,eAAQ,KAAK,GAAG;IAClB,WAAW,MAAM,QAAQ,GAAG,GAAG;AAC7BA,eAAQ,KAAK,GAAG,GAAG,GAAG,CAAC;IACzB,WAAW,OAAO,QAAQ,UAAU;AAElC,aAAO,QAAQ,GAAG,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC5C,YAAI,OAAO;AACTA,mBAAQ,KAAK,GAAG;QAClB;MACF,CAAC;IACH;AAEA,WAAOA;EACT,GAAG,CAAC,CAAa;AAEjB,SAAO,WAAW,SAAS,GAAG,EAAE,KAAK;AACvC;ACEO,SAAS,YACd,MACA,UACmB;AACnB,QAAM,QAAQ,IAAI,MAAM,MAAM,QAAQ;AAEtC,SAAO;IACL;IACA,UAAU,MAAM,SAAS,KAAK,KAAK;IACnC,WAAW,MAAM,UAAU,KAAK,KAAK;EACvC;AAEA,WAAS,OAAyBC,SAA6B;AAC7D,UAAM,eAAgC,CAAC;AAEvC,yBAAqB,OAAOA,SAAQ,IAAI,SAAS,KAAK,CAAC,EAAE;MACvD,CAAC,CAAC,WAAWA,UAAQ,QAAQ,MAAM;AACjC,sBAAc,OAAOA,UAAkB,QAAQ,EAAE;UAC/C,CAAC,cAAc;AACb,2BAAe,WAAgB,SAAS;UAC1C;QACF;MACF;IACF;AAKA,UAAM,MAAM;AAEZ,WAAO;AAEP,aAAS,eAAeC,OAAS,WAAmB;AAClD,mBAAaA,KAA6B,IACxC,aAAaA,KAA6B,KAAK,oBAAI,IAAY;AACjE,mBAAaA,KAA6B,EAAE,IAAI,SAAS;IAC3D;EACF;AACF;AAIA,SAAS,qBACP,OACAD,SACA,UACA;AACA,QAAM,SAA4C,CAAC;AAEnD,QAAMA,SAAQ,CAAC,KAAa,UAAU;AACpC,QAAI,iBAAiB,GAAG,GAAG;AACzB,aAAO;QACL;QACA;QACA,SAAS,gBAAgB,GAAG;MAC9B,EAAE,QAAQ,CAAC,SAAS,OAAO,KAAK,IAAI,CAAC;IACvC;AAIA,WAAO,KAAK,CAAC,KAAKA,QAAO,GAAG,GAAG,SAAS,SAAS,GAAG,CAAC,CAAC;EACxD,CAAC;AAED,SAAO;AACT;AAEA,SAAS,cACP,OACAA,SACA,UACU;AACV,QAAM,SAAmB,oBAAI,IAAY;AAEzC,QAAMA,SAAQ,CAAC,UAAU,UAAU;AACjC,QAAI,MAA8B,CAAC;AAGnC,QAAI,iBAAiB,QAAQ,GAAG;AAC9B,YAAM;QACJ;QACA;QACA,SAAS,iBAAiB,QAAQ;MACpC;IAEF,WAAW,cAAc,QAAQ,GAAG;AAClC,YAAM,QAAQ,KAAoB;IACpC,WAAW,aAAa,QAAQ,GAAG;AACjC,YAAM,iBAAiB,OAAO,OAAiB,UAAU,QAAQ;IAGnE,WAAW,eAAe,QAAQ,GAAG;AACnC,YAAM,kBAAkB,OAAO,OAA6B,QAAQ;IAGtE,WAAW,gBAAgB,UAAU,KAAK,GAAG;AAC3C,YAAM,OAAO,SAAS,WAAW,UAAU,KAAK;AAChD,YAAM,QAAQ,IAAI;AAClB,aAAO,IAAI,KAAK,IAAI;IACtB;AAEA,WAAO,aAAa,KAAK,MAAM;EACjC,CAAC;AAED,SAAO;AACT;AAEA,SAAS,aAAa,MAA8B,IAAiB;AACnE,OAAK,QAAQ,CAAC,cAAc,GAAG,IAAI,SAAS,CAAC;AAC7C,SAAO;AACT;AAGA,SAAS,kBACP,OACAA,SACA,UACA;AACA,QAAM,UAAoB,oBAAI,IAAY;AAE1C,QAAM,YAAsB,CAAC;AAC7B,QAAMA,SAAQ,CAAC,UAAkB,UAAU;AACzC,QAAI,gBAAgB,UAAU,KAAK,GAAG;AACpC,gBAAU,KAAK,KAAK,QAAQ,UAAU,KAAK,CAAC;AAC5C;IACF;AACA,UAAM,MAAM,cAAc,OAAO,SAAS,CAAC,GAAG,QAAQ;AACtD,iBAAa,KAAK,OAAO;EAC3B,CAAC;AAED,MAAI,CAAC,SAAS,gBAAgB;AAC5B,WAAO;EACT;AAEA,MAAI,UAAU,QAAQ;AACpB,UAAM,SAAS,UAAU,KAAK,GAAG;AACjC,UAAM;MACJ,GAAG,eAAe,SAAS,eAAe;QACxC,OAAO,SAAS;MAClB,CAAC,CAAC,KAAK,MAAM;IACf;EACF;AAEA,UAAQ,IAAI,SAAS,cAAc;AACnC,SAAO;AACT;AAEA,SAAS,iBACP,OACAA,SACA,YACA,UACA;AACA,QAAM,OAAO,aAAa,IAAI;AAI9B,QAAM,SAAS,cAAc,OAAOA,SAAQ,QAAQ;AAEpD,QAAM,OAAO,GAAG;AAEhB,SAAO;AACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1LA,IAAYE;CAAZ,SAAYA,aAAU;AACpBA,EAAAA,YAAAA,gBAAAA,IAAAA;AACAA,EAAAA,YAAAA,cAAAA,IAAAA;AACAA,EAAAA,YAAAA,QAAAA,IAAAA;AACAA,EAAAA,YAAAA,SAAAA,IAAAA;AACAA,EAAAA,YAAAA,QAAAA,IAAAA;AACAA,EAAAA,YAAAA,OAAAA,IAAAA;AACAA,EAAAA,YAAAA,UAAAA,IAAAA;AACAA,EAAAA,YAAAA,OAAAA,IAAAA;AACAA,EAAAA,YAAAA,iBAAAA,IAAAA;AACAA,EAAAA,YAAAA,oBAAAA,IAAAA;AACAA,EAAAA,YAAAA,YAAAA,IAAAA;AACAA,EAAAA,YAAAA,WAAAA,IAAAA;AACAA,EAAAA,YAAAA,UAAAA,IAAAA;AACAA,EAAAA,YAAAA,aAAAA,IAAAA;AACAA,EAAAA,YAAAA,MAAAA,IAAAA;AACAA,EAAAA,YAAAA,UAAAA,IAAAA;AACAA,EAAAA,YAAAA,YAAAA,IAAAA;AACAA,EAAAA,YAAAA,iBAAAA,IAAAA;AACAA,EAAAA,YAAAA,WAAAA,IAAAA;AACAA,EAAAA,YAAAA,WAAAA,IAAAA;AACF,GArBYA,eAAAA,aAAU,CAAA,EAAA;SAuBNC,cAAWA;oCAAIC,aAAwB,IAAAC,MAAAC,IAAA,GAAAC,OAAA,GAAAA,OAAAD,MAAAC,QAAA;AAAxBH,eAAwBG,IAAA,IAAAC,UAAAD,IAAA;;AACrD,SAAOH,WAAWK,IAAI,SAAAC,GAAC;AAAA,WAAA,MAAQA;GAAG,EAAEC,KAAK,EAAE;AAC7C;ACpBO,IAAMC,aAAaC,YAAY,OAAO,IAAI;AAEjD,IAAMC,SAAS;EACbC,SAAS;EACTC,SAAS;EACTC,eAAe;EACfC,YAAY;EACZC,UAAU;;AAGL,IAAMC,eAAeR,WAAWS,OAAO;EAC5CP,QAAMQ,SAAA;IACJ,KAAKpB,WAAWY;KACbA,MAAM;CAEZ;AAEM,IAAMS,qBAAiBC,mBAAW,SAASD,kBAAcA;AAC9D,aACEC,4BAAAA,SAAAA;IACEC,0BAAwB;IACxBC,yBAAyB;MAAEC,QAAQf,WAAWgB,SAAQ;;;AAG5D,CAAC;AAEM,IAAMC,0BAA0BjB,WAAWS,OAAO;EACvD,aAAa;IACX,uCAAuC;MACrCS,aAAa;QACX,UAAU;UACRd,SAAS;UACTe,qBAAqB;;;MAGzBC,gBAAcV,SAAA;QACZ,KAAKpB,WAAW8B;SACblB,MAAM;;IAGb,iCAAiC;MAC/BmB,qBAAqBnB;;;EAGzBoB,mBAAmB;IACjBC,YAAY;;EAEd,kBAAkB;IAChBD,mBAAmB;MACjBE,QAAQ;MACRC,OAAO;MACPrB,SAAS;MACTC,eAAe;MACfE,UAAU;;;EAGd,6CAA6C;IAC3CW,aAAa;MACX,UAAU;QACRd,SAAS;QACTe,qBAAqB;;MAEvB,gBAAgB;QACdf,SAAS;QACTe,qBAAqB;;;IAGzBE,qBAAmBX,SAAA;MACjB,KAAK;OACFR,MAAM;;CAGd;AAED,SAAgBwB,SAASC,KAAaC,OAAa;;AACjD,SAAO;IACL,oBAAiBC,gBAAA,CAAA,GAAAA,cACdF,GAAG,IAAGC,OAAKC;IAEd,oBAAiBC,gBAAA,CAAA,GAAAA,cACdH,GAAG,IAAG;MACL,uCAAuCC;OACxCE;;AAGP;ACvFA,SAAgBC,cAAcC,MAAoBC,MAAkB;;AAClE,MAAMC,oBAAgBC,qBAAGH,KAAKI,iBAAY,OAAAD,qBAAI,CAAA;AAC9C,MAAME,oBAAgBC,qBAAGL,KAAKG,iBAAY,OAAAE,qBAAI,CAAA;AAC9C,SACEN,KAAKO,SAASN,KAAKM,QACnBP,KAAKQ,iBAAiBP,KAAKO,gBAC3BR,KAAKS,yBAAyBR,KAAKQ,wBACnCT,KAAKU,sBAAsBT,KAAKS,qBAChCV,KAAKW,sBAAsBV,KAAKU,qBAChCX,KAAKY,oBAAoBX,KAAKW,mBAC9BZ,KAAKa,sBAAsBZ,KAAKY,qBAChCb,KAAKc,oBAAoBb,KAAKa,mBAC9Bd,KAAKe,eAAed,KAAKc,cACzBf,KAAKgB,UAAUf,KAAKe,SACpBhB,KAAKiB,wBAAwBhB,KAAKgB,uBAClCjB,KAAKkB,mBAAmBjB,KAAKiB,kBAC7BlB,KAAKmB,cAAclB,KAAKkB,aACxBnB,KAAKR,WAAWS,KAAKT,UACrBQ,KAAKP,UAAUQ,KAAKR,SACpBO,KAAKoB,UAAUnB,KAAKmB,SACpBpB,KAAKqB,mBAAmBpB,KAAKoB,kBAC7BrB,KAAKsB,2BAA2BrB,KAAKqB,0BACrCpB,iBAAiBqB,WAAWlB,iBAAiBkB;AAEjD;AC3BO,IAAMC,oBAAoB;EAC/B;EACA;EACA;EACA;EACA;EACA;EACA;;AAAQ;ICIEC;CAAZ,SAAYA,iBAAc;AACxBA,EAAAA,gBAAAA,QAAAA,IAAAA;AACAA,EAAAA,gBAAAA,UAAAA,IAAAA;AACF,GAHYA,mBAAAA,iBAAc,CAAA,EAAA;AAK1B,IAAYC;CAAZ,SAAYA,aAAU;AACpBA,EAAAA,YAAAA,QAAAA,IAAAA;AACAA,EAAAA,YAAAA,OAAAA,IAAAA;AACAA,EAAAA,YAAAA,SAAAA,IAAAA;AACAA,EAAAA,YAAAA,QAAAA,IAAAA;AACAA,EAAAA,YAAAA,UAAAA,IAAAA;AACF,GANYA,eAAAA,aAAU,CAAA,EAAA;AAQtB,IAAYC;CAAZ,SAAYA,QAAK;AACfA,EAAAA,OAAAA,MAAAA,IAAAA;AACAA,EAAAA,OAAAA,OAAAA,IAAAA;AACAA,EAAAA,OAAAA,MAAAA,IAAAA;AACF,GAJYA,UAAAA,QAAK,CAAA,EAAA;AAMjB,IAAYC;CAAZ,SAAYA,YAAS;AACnBA,EAAAA,WAAAA,SAAAA,IAAAA;AACAA,EAAAA,WAAAA,OAAAA,IAAAA;AACAA,EAAAA,WAAAA,cAAAA,IAAAA;AACAA,EAAAA,WAAAA,QAAAA,IAAAA;AACAA,EAAAA,WAAAA,aAAAA,IAAAA;AACAA,EAAAA,WAAAA,MAAAA,IAAAA;AACF,GAPYA,cAAAA,YAAS,CAAA,EAAA;AASrB,IAAYC;CAAZ,SAAYA,aAAU;AACpBA,EAAAA,YAAAA,WAAAA,IAAAA;AACAA,EAAAA,YAAAA,QAAAA,IAAAA;AACAA,EAAAA,YAAAA,gBAAAA,IAAAA;AACAA,EAAAA,YAAAA,gBAAAA,IAAAA;AACAA,EAAAA,YAAAA,YAAAA,IAAAA;AACAA,EAAAA,YAAAA,eAAAA,IAAAA;AACAA,EAAAA,YAAAA,YAAAA,IAAAA;AACAA,EAAAA,YAAAA,SAAAA,IAAAA;AACAA,EAAAA,YAAAA,SAAAA,IAAAA;AACAA,EAAAA,YAAAA,OAAAA,IAAAA;AACF,GAXYA,eAAAA,aAAU,CAAA,EAAA;AAatB,IAAYC;CAAZ,SAAYA,yBAAsB;AAChCA,EAAAA,wBAAAA,QAAAA,IAAAA;AACAA,EAAAA,wBAAAA,SAAAA,IAAAA;AACF,GAHYA,2BAAAA,yBAAsB,CAAA,EAAA;;AChDlC,IAAMC,oBAAkC,CACtCF,WAAWG,WACXH,WAAWI,QACXJ,WAAWK,gBACXL,WAAWM,gBACXN,WAAWO,YACXP,WAAWQ,eACXR,WAAWS,YACXT,WAAWU,SACXV,WAAWW,SACXX,WAAWY,KAAK;AAGX,IAAMC,kBAAkC;EAC7CC,MAAM;EACNC,UAAUf,WAAWG;;AAQvB,IAAMa,oBAAgBC,oBAAA,CAAA,GAAAA,kBACnBjB,WAAWG,SAAS,IAAG;EACtBY,UAAUf,WAAWG;EACrBW,MAAM;GACPG,kBACAjB,WAAWI,MAAM,IAAG;EACnBW,UAAUf,WAAWI;EACrBU,MAAM;GACPG,kBACAjB,WAAWK,cAAc,IAAG;EAC3BU,UAAUf,WAAWK;EACrBS,MAAM;GACPG,kBACAjB,WAAWM,cAAc,IAAG;EAC3BS,UAAUf,WAAWM;EACrBQ,MAAM;GACPG,kBACAjB,WAAWO,UAAU,IAAG;EACvBQ,UAAUf,WAAWO;EACrBO,MAAM;GACPG,kBACAjB,WAAWQ,aAAa,IAAG;EAC1BO,UAAUf,WAAWQ;EACrBM,MAAM;GACPG,kBACAjB,WAAWS,UAAU,IAAG;EACvBM,UAAUf,WAAWS;EACrBK,MAAM;GACPG,kBACAjB,WAAWU,OAAO,IAAG;EACpBK,UAAUf,WAAWU;EACrBI,MAAM;GACPG,kBACAjB,WAAWW,OAAO,IAAG;EACpBI,UAAUf,WAAWW;EACrBG,MAAM;GACPG,kBACAjB,WAAWY,KAAK,IAAG;EAClBG,UAAUf,WAAWY;EACrBE,MAAM;GACPG;AAGH,SAAgBC,qBACdC,WAA8C;AAE9C,SAAOjB,kBAAkBlE,IAAI,SAAA+E,UAAQ;AACnC,WAAAlE,SAAA,CAAA,GACKmE,iBAAiBD,QAAQ,GACxBI,aAAaA,UAAUJ,QAAQ,KAAKI,UAAUJ,QAAQ,CAAC;GAE9D;AACH;AAEA,SAAgBK,2BAA2BL,UAAwB;AACjE,SAAOA,SAASA;AAClB;AAEA,SAAgBM,+BAA+BN,UAAwB;AACrE,SAAOA,SAASD;AAClB;AAWA,SAAgBQ,sBACdC,sBACAJ,WAAAA;;MADAI,yBAAAA,QAAAA;AAAAA,2BAA2C,CAAA;;AAAE,MAC7CJ,cAAAA,QAAAA;AAAAA,gBAAqC,CAAA;;AAErC,MAAMK,QAAQ,CAAA;AAEd,MAAIL,UAAUM,mBAAmB7B,eAAe8B,QAAQ;AACtDF,UAAMxB,WAAWG,SAAS,IAAIU;;AAGhC,MAAMc,OAAOT,qBAAqBM,KAAK;AACvC,MAAI,GAAAI,wBAACL,yBAAoB,QAApBK,sBAAsBlC,SAAQ;AACjC,WAAOiC;;AAGT,SAAOJ,qBAAqBvF,IAAI,SAAA+E,UAAQ;AACtC,QAAI,OAAOA,aAAa,UAAU;AAChC,aAAOc,wBAAwBd,UAAUS,MAAMT,QAAQ,CAAC;;AAG1D,WAAAlE,SAAA,CAAA,GACKgF,wBAAwBd,SAASA,UAAUS,MAAMT,SAASA,QAAQ,CAAC,GACnEA,QAAQ;GAEd;AACH;AAEA,SAASc,wBACPd,UACAe,UAAAA;MAAAA,aAAAA,QAAAA;AAAAA,eAA2B,CAAA;;AAE3B,SAAOC,OAAOC,OAAOhB,iBAAiBD,QAAQ,GAAGe,QAAQ;AAC3D;AChIA,IAAMG,gBACJ;AACF,IAAMC,mBACJ;AACF,IAAMC,kBACJ;AACF,IAAMC,iBACJ;AAEF,SAAgBC,OAAOnD,YAAsB;AAC3C,UAAQA,YAAU;IAChB,KAAKW,WAAWyC;AACd,aAAOH;IACT,KAAKtC,WAAW0C;AACd,aAAOH;IACT,KAAKvC,WAAW2C;AACd,aAAON;IACT,KAAKrC,WAAW4C;IAChB;AACE,aAAOR;;AAEb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrBA,IAAMS,qBAAqB,CACzB3C,UAAU4C,SACV5C,UAAU6C,OACV7C,UAAU8C,cACV9C,UAAU+C,QACV/C,UAAUgD,aACVhD,UAAUiD,IAAI;AAGT,IAAMC,iBAAiBlB,OAAOmB,QAAQnD,SAAS,EAAEoD,OACtD,SAACC,KAAGC,MAAA;MAAGvF,MAAGuF,KAAA,CAAA,GAAEtF,QAAKsF,KAAA,CAAA;AACfD,MAAIrF,KAAK,IAAID;AACb,SAAOsF;AACT,GACA,CAAA,CAA4B;AAGvB,IAAME,kBAGTZ,mBAAmBS,OACrB,SAACI,QAAQC,UAAQ;AAAA,MAAAC;AAAA,SACf1B,OAAOC,OAAOuB,SAAME,iBAAA,CAAA,GAAAA,eACjBD,QAAQ,IAAGA,UAAQC,eAAA;AACpB,GACJ,CAAA,CAAE;ACzBJ,IAAYC;CAAZ,SAAYA,kBAAe;AACzBA,EAAAA,iBAAAA,MAAAA,IAAAA;AACAA,EAAAA,iBAAAA,SAAAA,IAAAA;AACAA,EAAAA,iBAAAA,YAAAA,IAAAA;AACAA,EAAAA,iBAAAA,UAAAA,IAAAA;AACAA,EAAAA,iBAAAA,QAAAA,IAAAA;AACF,GANYA,oBAAAA,kBAAe,CAAA,EAAA;ACCpB,IAAMC,yBAAoC,CAAA;AAEjDC,WAAW,WAAA;AACTC,YAAUV,OAAO,SAACW,aAAaC,OAAK;AAClCC,eAAWD,KAAK;AAChB,WAAOD;KACNH,sBAAmC;AACxC,CAAC;AAID,SAAgBK,WAAWD,OAAgB;AACzC,MAAME,mBAAmBC,WAAWH,KAAK,EACtCI,KAAI,EACJjI,KAAK,EAAE,EACPkI,YAAW,EACXC,QAAQ,gBAAgB,EAAE,EAC1BC,MAAM,EAAE;AAEXL,mBAAiBM,QAAQ,SAAAC,OAAI;;AAC3Bb,2BAAuBa,KAAI,KAACC,wBAAGd,uBAAuBa,KAAI,MAAC,OAAAC,wBAAI,CAAA;AAE/Dd,2BAAuBa,KAAI,EAAEE,aAAaX,KAAK,CAAC,IAAIA;GACrD;AACH;SCfgBG,WAAWH,OAAe;;AACxC,UAAAY,wBAAOZ,MAAML,gBAAgB5C,IAAI,MAAC,OAAA6D,wBAAI,CAAA;AACxC;AAEA,SAAgBC,QAAQb,OAAgB;AACtC,SAAOc,WAAWd,MAAML,gBAAgBoB,QAAQ,CAAC;AACnD;AAEA,SAAgBC,UAAUhB,OAAgB;AACxC,MAAI,CAACA,OAAO;AACV,WAAO;;AAGT,SAAOG,WAAWH,KAAK,EAAE,CAAC;AAC5B;AAEA,SAAgBiB,uBAAuBC,SAAe;AACpD,MAAMC,QAAQD,QAAQX,MAAM,GAAG;AAC/B,MAAAa,gBAAmBD,MAAME,OAAO,GAAG,CAAC,GAA7B5B,WAAQ2B,cAAA,CAAA;AAEf,MAAI7B,gBAAgBE,QAAQ,GAAG;AAC7B,WAAO0B,MAAMhJ,KAAK,GAAG;;AAGvB,SAAO+I;AACT;AAEA,SAAgBP,aAAaX,OAAkBP,UAAiB;;AAC9D,MAAMyB,UAAUlB,MAAML,gBAAgBuB,OAAO;AAE7C,MAAI,CAACzB,YAAY,CAAC6B,mBAAmBtB,KAAK,GAAG;AAC3C,WAAOkB;;AAGT,UAAAK,wBAAOC,sBAAsBxB,OAAOP,QAAQ,MAAC,OAAA8B,wBAAIL;AACnD;AAEA,SAAgBO,iBAAiBzE,UAAoB;;AAEnD,UAAA0E,mBAAOC,UAAM,OAAA,SAANA,OAAS3E,QAAQ,MAAC,OAAA0E,mBAAI,CAAA;AAC/B;AAGA,SAAgBE,kBACdV,SACA/F,YAAsB;AAEtB,SAAA,KAAUmD,OAAOnD,UAAU,IAAI+F,UAAO;AACxC;AAEA,SAAgBW,gBAAgB7B,OAAgB;;AAC9C,UAAA8B,yBAAO9B,MAAML,gBAAgBoC,UAAU,MAAC,OAAAD,yBAAI,CAAA;AAC9C;AAEA,SAAgBR,mBAAmBtB,OAAgB;AACjD,SAAO6B,gBAAgB7B,KAAK,EAAErE,SAAS;AACzC;AAEA,SAAgB6F,sBACdxB,OACAP,UAAiB;AAEjB,SAAOA,WACHoC,gBAAgB7B,KAAK,EAAEgC,KAAK,SAAAC,WAAS;AAAA,WAAIA,UAAUC,SAASzC,QAAQ;OACpEkB,aAAaX,KAAK;AACxB;AAEA,SAAgBmC,eAAejB,SAAgB;AAC7C,MAAI,CAACA,SAAS;AACZ;;AAGF,MAAIkB,mBAAmBlB,OAAO,GAAG;AAC/B,WAAOkB,mBAAmBlB,OAAO;;AAGnC,MAAMmB,kBAAkBpB,uBAAuBC,OAAO;AACtD,SAAOkB,mBAAmBC,eAAe;AAC3C;AAEO,IAAMvC,YAAwB9B,OAAOsE,OAAOX,MAAM,EAAEvB,KAAI;AAE/D,SAAgBmC,gBAAgB/H,cAA2B;AACzDmH,SAAO1F,WAAWI,MAAM,EAAEV,SAAS;AAEnCnB,eAAagG,QAAQ,SAAAR,OAAK;AACxB,QAAMwC,YAAYC,qBAAqBzC,KAAK;AAE5C2B,WAAO1F,WAAWI,MAAM,EAAEqG,KAAKF,SAAkB;AAEjD,QAAIJ,mBAAmBI,UAAU7C,gBAAgBuB,OAAO,CAAC,GAAG;AAC1D;;AAGFpB,cAAU4C,KAAKF,SAAS;AACxBJ,uBAAmBI,UAAU7C,gBAAgBuB,OAAO,CAAC,IAAIsB;AACzDvC,eAAWuC,SAAS;GACrB;AACH;AAEA,SAASC,qBAAqBzC,OAAkB;;AAC9C,SAAAV,OAAA,CAAA,GAAAA,KACGK,gBAAgB5C,IAAI,IAAGiD,MAAM2C,MAAM1K,IAAI,SAAA8E,MAAI;AAAA,WAAIA,KAAKsD,YAAW;MAAGf,KAClEK,gBAAgBuB,OAAO,IAAGlB,MAAM4C,GAAGvC,YAAW,GAAEf,KAChDK,gBAAgBoB,QAAQ,IAAG,KAAGzB,KAC9BK,gBAAgBkD,MAAM,IAAG7C,MAAM6C,QAAMvD;AAE1C;AAEA,IAAM8C,qBAEF,CAAA;AAEJvC,WAAW,WAAA;AACTC,YAAUV,OAAO,SAACU,YAAWgD,OAAK;AAChChD,IAAAA,WAAUa,aAAamC,KAAK,CAAC,IAAIA;AAEjC,QAAIxB,mBAAmBwB,KAAK,GAAG;AAC7BjB,sBAAgBiB,KAAK,EAAEtC,QAAQ,SAAAyB,WAAS;AACtCnC,QAAAA,WAAUmC,SAAS,IAAIa;OACxB;;AAGH,WAAOhD;KACNsC,kBAAkB;AACvB,CAAC;AAED,SAAgBW,2BAA2B7B,SAAe;AACxD,MAAA8B,iBAA8B9B,QAAQX,MAAM,GAAG,GAAtC0C,oBAAiBD,eAAA,CAAA;AAC1B,SAAOrE,mBAAmBuD,SAASe,iBAAiB,IAChDA,oBACA;AACN;ACxHA,IAAMC,uBAAuB,CAAC,aAAa,aAAa,WAAW;AAE5D,IAAMC,6BAA6B;AACnC,IAAMC,kCAAkC;AACxC,IAAMC,wBACX;AACK,IAAMC,kCACX,aAAaD;AACR,IAAME,wCACX,eAAeF;AAEjB,SAAgBG,YACdC,YAAAA;;MAAAA,eAAAA,QAAAA;AAAAA,iBAA2B,CAAA;;AAE3B,MAAM7F,OAAO8F,iBAAgB;AAE7B,MAAMC,gBAAgB3F,OAAOC,OAC3BL,KAAK+F,gBAAaC,wBAClBH,WAAWE,kBAAa,OAAAC,wBAAI,CAAA,CAAE;AAEhC,MAAMC,SAAS7F,OAAOC,OAAOL,MAAM6F,UAAU;AAE7C,MAAMK,aAAavG,sBAAsBkG,WAAWK,YAAY;IAC9DpG,gBAAgBmG,OAAOxI;GACxB;AAEDwI,SAAOE,aAAavD,QAAQ,SAACR,OAAK;AAChC6D,WAAOG,cAAcC,IAAIjE,KAAK;GAC/B;AAEDuC,mBAAe2B,uBAACL,OAAOrJ,iBAAY,OAAA0J,uBAAI,CAAA,CAAE;AAEzC,MAAMxI,yBAAyBmI,OAAOpI,iBAClCS,uBAAuBiI,UACvBN,OAAOnI;AAEX,SAAA5C,SAAA,CAAA,GACK+K,QAAM;IACTC;IACAH;IACAjI;;AAEJ;AAEA,SAAgBgI,mBAAgBA;AAC9B,SAAO;IACLxI,iBAAiB;IACjB4I,YAAY3G,qBAAoB;IAChC5B,WAAW;IACXf,cAAc,CAAA;IACdQ,iBAAiBgB,UAAU4C;IAC3BzD,YAAYW,WAAW4C;IACvB9D,cAAc;IACdwJ,aAAaxC;IACbhI,QAAQ;IACR0B,gBAAgB;IAChBqI,eAAa7K,SAAA,CAAA,GACRuL,iBAAiB;IAEtB5I,gBAAgB;IAChBX,mBAAmBqI;IACnBpI,mBAAmBoI;IACnBzH,wBAAwBQ,uBAAuBoI;IAC/CrJ,mBAAmB;IACnBO,OAAO,CAAA;IACPH,qBAAqBQ,eAAe0I;IACpCnJ,OAAOW,MAAM8C;IACbmF,eAAe,IAAIQ,IAAYtB,oBAAoB;IACnDrJ,OAAO;IACPgB,sBAAsB;IACtB4J,WAAW7I;IACXjB,MAAM;IACN+J,sBAAsB;IACtBX,cAAc,CAAA;;AAElB;AAqCA,IAAMM,oBAAmC;EACvCM,cAAc;EACdC,gBAAgB;EAChBC,aAAa;;;AC5Hf,IAAMC,oBAAgB9L,4BACpB0K,iBAAgB,CAAE;AAGpB,SAAgBqB,qBAAoBzF,MAAA;MAAG0F,WAAQ1F,KAAR0F,UAAanB,SAAMoB,8BAAA3F,MAAA4F,SAAA;AACxD,MAAMC,eAAeC,aAAavB,MAAM;AAExC,aACE7K,4BAAC8L,cAAcO,UAAQ;IAACrL,OAAOmL;KAC5BH,QAAQ;AAGf;AAEA,SAAgBI,aAAavB,QAAoB;;AAC/C,MAAAyB,sBAAwCtM,uBAAe,WAAA;AAAA,WACrDwK,YAAYK,MAAM;MADbsB,eAAYG,gBAAA,CAAA,GAAEC,kBAAeD,gBAAA,CAAA;AAIpCtM,8BAAgB,WAAA;AACd,QAAImB,cAAcgL,cAActB,MAAM,GAAG;AACvC;;AAEF0B,oBAAgB/B,YAAYK,MAAM,CAAC;KAGlC,EAAAK,uBACDL,OAAOrJ,iBAAY,OAAA,SAAnB0J,qBAAqBvI,QACrBkI,OAAOlJ,MACPkJ,OAAOjJ,cACPiJ,OAAOhJ,sBACPgJ,OAAO/I,mBACP+I,OAAO9I,mBACP8I,OAAO7I,iBACP6I,OAAO5I,mBACP4I,OAAO3I,iBACP2I,OAAO1I,YACP0I,OAAOzI,OACPyI,OAAOxI,qBACPwI,OAAOvI,gBACPuI,OAAOtI,WACPsI,OAAOjK,QACPiK,OAAOhK,OACPgK,OAAOpI,gBACPoI,OAAOnI,wBACPmI,OAAOa,oBAAoB,CAC5B;AAED,SAAOS;AACT;AAEA,SAAgBK,kBAAeA;AAC7B,aAAOxM,yBAAiB8L,aAAa;AACvC;SClEgBW,kBACdC,cACAC,OAAAA;MAAAA,UAAAA,QAAAA;AAAAA,YAAgB;;AAEhB,MAAAC,gBAA0BC,uBAAYH,YAAY,GAA3CI,QAAKF,UAAA,CAAA,GAAEG,WAAQH,UAAA,CAAA;AACtB,MAAMI,YAAQC,qBAAsB,IAAI;AAExC,WAASC,kBAAkBlM,OAAQ;AACjC,WAAO,IAAImM,QAAW,SAAAC,SAAO;;AAC3B,UAAIJ,MAAMK,SAAS;AACjBC,qBAAaN,MAAMK,OAAO;;AAG5BL,YAAMK,WAAOE,UAAGC,WAAM,OAAA,SAAND,QAAQ1G,WAAW,WAAA;AACjCkG,iBAAS/L,KAAK;AACdoM,gBAAQpM,KAAK;SACZ2L,KAAK;KACT;;AAGH,SAAO,CAACG,OAAOI,iBAAiB;AAClC;SCrBgBO,qBAAkBA;AAC9B,MAAMzC,gBAAgB0C,iBAAgB;AACtC,SAAO,SAAC/F,eAAoB;AAAA,WAAKqD,cAAc2C,IAAIhG,aAAY;;AACjE;SCQciG,sBAAmBA;AACjC,MAAMC,0BAAsBZ,qBAAgC,CAAA,CAAE;AAC9D,MAAMa,qBAAqBC,sBAAqB;AAEhD,aAAOC,sBAAQ,WAAA;AACb,QAAMpM,eAAekG,WAAU,KAAIgG,kBAAoB;AAEvD,QAAI,CAACA,sBAAsBG,OAAOC,MAAMtM,YAAY,GAAG;AACrD,aAAOiM,oBAAoBR;;AAG7B,WAAOvG,UAAUV,OAAO,SAAC+H,kBAAkBnH,OAAK;AAC9C,UAAIoH,oBAAoBpH,OAAOpF,YAAY,GAAG;AAC5CuM,yBAAiBxG,aAAaX,KAAK,CAAC,IAAI;;AAG1C,aAAOmH;OACNN,oBAAoBR,OAAO;KAC7B,CAACS,kBAAkB,CAAC;AACzB;AAEA,SAAgBO,uBAAoBA;AAClC,MAAMF,mBAAmBP,oBAAmB;AAC5C,MAAMU,kBAAkBb,mBAAkB;AAE1C,SAAO,SAASc,kBAAkBvH,OAAgB;AAChD,QAAMkB,UAAUD,uBAAuBN,aAAaX,KAAK,CAAC;AAE1D,WAAOwH,QAAQL,iBAAiBjG,OAAO,KAAKoG,gBAAgBpG,OAAO,CAAC;;AAExE;AAEA,SAASkG,oBACPpH,OACAyH,gBAAsB;AAEtB,SAAO5G,QAAQb,KAAK,IAAIyH;AAC1B;SC/CgBC,mBACdC,UAAuD;AAEvDC,8BAAU,WAAA;AACRD,aAAS,IAAI;KACZ,CAACA,QAAQ,CAAC;AACf;SCMgBE,sBAAqBvI,MAAA;MAAG0F,WAAQ1F,KAAR0F;AACtC,MAAMmC,mBAAmBP,oBAAmB;AAC5C,MAAM5L,kBAAkB8M,yBAAwB;AAChD,MAAMjN,uBAAuBkN,uBAAsB;AAGnD,MAAMC,gBAAYhP,qBAA0B4G,sBAAsB;AAClE,MAAMqI,uBAAmBjP,qBAAsB,KAAK;AACpD,MAAMkP,uBAAmBlP,qBAAsB,KAAK;AACpD,MAAMmP,0BAAsBnP,qBAC1BmO,gBAAgB;AAGlB,MAAMiB,uBAAuB3C,kBAAkB4C,KAAKC,IAAG,GAAI,GAAG;AAC9D,MAAMC,aAAa9C,kBAAkB,IAAI,GAAG;AAC5C,MAAM+C,2BAAuB3C,uBAAkB,KAAK;AACpD,MAAM4C,qBAAiB5C,uBAAoB7K,eAAe;AAC1D,MAAM0N,0BAAsB7C,uBAA8B,IAAI;AAC9D,MAAM8C,kCAA8B9C,uBAAsB,oBAAIrB,IAAG,CAAE;AACnE,MAAMoE,gCAA4B/C,uBAA2B,IAAI;AACjE,MAAMgD,yBAAqBhD,uBAAShL,oBAAoB;AACxD,MAAA+K,gBAAkDC,uBAAS,KAAK,GAAzDiD,oBAAiBlD,UAAA,CAAA,GAAEmD,uBAAoBnD,UAAA,CAAA;AAE9C8B,qBAAmBqB,oBAAoB;AAEvC,aACE/P,4BAACgQ,cAAc3D,UAAQ;IACrBrL,OAAO;MACL0O;MACAD;MACAR;MACAC;MACAC;MACAS;MACAD;MACAX;MACAc;MACAP;MACAC;MACAJ;MACAS;;KAGD7D,QAAQ;AAGf;AAIA,IAAMgE,oBAAgBhQ,4BAcnB;EACD0P,qBAAqB,CAAC,MAAM,WAAA;EAAA,CAAQ;EACpCD,gBAAgB,CAACzM,UAAU4C,SAAS,WAAA;EAAA,CAAQ;EAC5CqJ,kBAAkB;IAAE5B,SAAS;;EAC7B6B,kBAAkB;IAAE7B,SAAS;;EAC7B8B,qBAAqB;IAAE9B,SAAS,CAAA;;EAChCuC,2BAA2B,CAAC,MAAM,WAAA;EAAA,CAAQ;EAC1CD,6BAA6B,CAAC,oBAAInE,IAAG,GAAI,WAAA;EAAA,CAAQ;EACjDwD,WAAW;IAAE3B,SAAS,CAAA;;EACtByC,mBAAmB;EACnBP,YAAY,CAAC,IAAI,WAAA;AAAA,WAAM,IAAIpC,QAAgB,WAAA;AAAA,aAAM8C;;;EACjDT,sBAAsB,CAAC,OAAO,WAAA;EAAA,CAAQ;EACtCJ,sBAAsB,CAACC,KAAKC,IAAG,GAAI,WAAA;EAAA,CAAQ;EAC3CO,oBAAoB,CAAC,OAAO,WAAA;EAAA,CAAQ;CACrC;AAMD,SAAgBK,eAAYA;AAC1B,MAAAC,wBAAsBnQ,yBAAiBgQ,aAAa,GAA5ChB,YAASmB,kBAATnB;AACR,SAAOA;AACT;AAEA,SAAgBoB,sBAAmBA;AACjC,MAAAC,yBAA6BrQ,yBAAiBgQ,aAAa,GAAnDf,mBAAgBoB,mBAAhBpB;AACR,SAAOA;AACT;AAEA,SAAgBqB,sBAAmBA;AACjC,MAAAC,yBAA6BvQ,yBAAiBgQ,aAAa,GAAnDd,mBAAgBqB,mBAAhBrB;AACR,SAAOA;AACT;AAEA,SAAgBsB,wBAAqBA;AACnC,MAAAC,yBAA+BzQ,yBAAiBgQ,aAAa,GAArDH,qBAAkBY,mBAAlBZ;AACR,SAAOA;AACT;AAEA,SAAgBa,qBAAkBA;AAChC,MAAAC,yBAAuB3Q,yBAAiBgQ,aAAa,GAA7CT,aAAUoB,mBAAVpB;AACR,SAAOA;AACT;AAEA,SAAgBqB,yBAAsBA;AAIpC,MAAAC,yBAA2B7Q,yBAAiBgQ,aAAa,GAAjDP,iBAAcoB,mBAAdpB;AACR,SAAOA;AACT;AAEA,SAAgBqB,iCAA8BA;AAC5C,MAAAC,yBAAwC/Q,yBAAiBgQ,aAAa,GAA9DL,8BAA2BoB,mBAA3BpB;AACR,SAAOA;AACT;AAEA,SAAgBqB,uBAAoBA;AAClC,MAAAC,yBAA8BjR,yBAAiBgQ,aAAa,GAApDF,oBAAiBmB,mBAAjBnB;AACR,SAAOA;AACT;AAEA,SAAgBoB,+BAA4BA;AAC1C,MAAAC,yBAAsCnR,yBAAiBgQ,aAAa,GAA5DJ,4BAAyBuB,mBAAzBvB;AACR,SAAOA;AACT;AAEA,SAAgBwB,0BAAuBA;AACrC,MAAAC,0BAAiCrR,yBAAiBgQ,aAAa,GAAvDR,uBAAoB6B,oBAApB7B;AACR,SAAOA;AACT;AAEA,SAKgB8B,qBAAkBA;AAChC,MAAAC,0BAAiCvR,yBAAiBgQ,aAAa,GAAvDZ,uBAAoBmC,oBAApBnC;AAER,MAAOoC,mBAAwCpC,qBAAoB,CAAA,GAA1CqC,qBAAsBrC,qBAAoB,CAAA;AACnE,SAAO,CACLoC,kBACA,SAASE,kBAAeA;AACtBD,uBAAmBpC,KAAKC,IAAG,CAAE;GAC9B;AAEL;AC7JO,IAAMqC,uBAAuB3R,aAAAA,QAAM4R,cAExC,CAAA,CAA2C;AAE7C,SAAgBC,mBAAgBA;AAC9B,MAAMC,gBAAgB9R,aAAAA,QAAM+R,WAAWJ,oBAAoB;AAC3D,SAAOG;AACT;AAEA,SAAgBE,uBACdnH,QAAqB;AAErB,MAAMoH,mBAAmBjS,aAAAA,QAAMiN,OAAsB;IACnDiF,cAAcrH,OAAOqH,gBAAgBC;IACrCC,iBAAiBvH,OAAOuH,mBAAmBvH,OAAOqH;IAClDG,kBAAkBxH,OAAOwH,oBAAoBF;GAC9C;AAEDnS,eAAAA,QAAM4O,UAAU,WAAA;AACdqD,qBAAiB5E,QAAQ6E,eAAerH,OAAOqH,gBAAgBC;AAC/DF,qBAAiB5E,QAAQ+E,kBACvBvH,OAAOuH,mBAAmBvH,OAAOqH;KAClC,CAACrH,OAAOqH,cAAcrH,OAAOuH,eAAe,CAAC;AAEhDpS,eAAAA,QAAM4O,UAAU,WAAA;AACdqD,qBAAiB5E,QAAQgF,mBACvBxH,OAAOwH,oBAAoBF;KAC5B,CAACtH,OAAOwH,gBAAgB,CAAC;AAE5B,SAAOJ;AACT;AAEA,SAASE,YAASA;AAAAA;ACjBlB,IAAYG;CAAZ,SAAYA,qBAAkB;AAC5BA,EAAAA,oBAAAA,WAAAA,IAAAA;AACAA,EAAAA,oBAAAA,QAAAA,IAAAA;AACF,GAHYA,uBAAAA,qBAAkB,CAAA,EAAA;AAK9B,SAAgBC,6BAA0BA;;AACxC,MAAAC,mBAAiDhG,gBAAe,GAAxD1K,oBAAiB0Q,iBAAjB1Q,mBAAmBC,oBAAiByQ,iBAAjBzQ;AAC3B,UAAA0Q,QACE,CAAC3Q,mBAAmBC,iBAAiB,EAAEiH,KACrC,SAAA0J,GAAC;AAAA,WAAIA,MAAMvI;SACZ,OAAAsI,QAAItI;AAET;AAEA,SAAgB2E,2BAAwBA;AACtC,MAAA6D,oBAA4BnG,gBAAe,GAAnCxK,kBAAe2Q,kBAAf3Q;AACR,SAAOA;AACT;AAEA,SAAgB4Q,0BAAuBA;AACrC,MAAAC,oBAAiCrG,gBAAe,GAAxCd,uBAAoBmH,kBAApBnH;AACR,SAAOA;AACT;AAEA,SAAgBoH,6BAA0BA;AACxC,MAAAC,oBAA8BvG,gBAAe,GAArCvK,oBAAiB8Q,kBAAjB9Q;AACR,SAAOA;AACT;AAEA,SAAgB+Q,sBAAmBA;AACjC,MAAAC,oBAAuBzG,gBAAe,GAA9BrK,aAAU8Q,kBAAV9Q;AACR,SAAOA;AACT;AAEA,SAAgB+Q,2BAAwBA;AACtC,MAAAC,oBAA4B3G,gBAAe,GAAnCtK,kBAAeiR,kBAAfjR;AACR,SAAOA;AACT;AAEA,SAAgBkR,sBAAmBA;AACjC,MAAAC,oBAAuB7G,gBAAe,GAA9B1B,aAAUuI,kBAAVvI;AACR,SAAOA;AACT;AAEA,SAAgBwI,wBAAqBA;AACnC,MAAAC,oBAAyB/G,gBAAe,GAAhChL,eAAY+R,kBAAZ/R;AACR,SAAOA;AACT;AAEA,SAAgBgS,gBAAaA;AAC3B,MAAAC,oBAAiBjH,gBAAe,GAAxB7K,OAAI8R,kBAAJ9R;AACR,SAAOA;AACT;AAEA,SAAgB+R,sBACdC,kBAAoC;AAEpC,MAAAC,oBAAoB/B,iBAAgB,GAA5BxE,UAAOuG,kBAAPvG;AACR,MAAAwG,wBAA6BrD,sBAAqB,GAAzCsD,mBAAgBD,sBAAA,CAAA;AAEzB,MAAME,UAAU1G,QAAQ6E,gBAAiB,WAAA;EAAA;AACzC,MAAQE,kBAAoB/E,QAApB+E;AAER,MAAIuB,qBAAqBrB,mBAAmB0B,aAAa5B,iBAAiB;AACxE,WAAO,WAAA;AAAA,eAAAtT,OAAAE,UAAA2D,QAAIsR,OAAI,IAAApV,MAAAC,IAAA,GAAAC,OAAA,GAAAA,OAAAD,MAAAC,QAAA;AAAJkV,aAAIlV,IAAA,IAAAC,UAAAD,IAAA;;AAAA,aACbqT,gBAAe8B,MAAA,QAAID,KAAIE,OAAA,CAAE;QACvBC,qBAAqB,SAAAA,sBAAAA;AACnBN,2BAAiB,SAAAO,GAAC;AAAA,mBAAIA;;;OAEzB,CAAA,CAAA;;;AAGL,SAAO,WAAA;uCAAIJ,OAAI,IAAApV,MAAAyV,KAAA,GAAAC,QAAA,GAAAA,QAAAD,OAAAC,SAAA;AAAJN,WAAIM,KAAA,IAAAvV,UAAAuV,KAAA;;AACbR,YAAOG,MAAA,QAAID,KAAIE,OAAA,CAAE;MACfC,qBAAqB,SAAAA,sBAAAA;AACnBN,yBAAiB,IAAI;;KAExB,CAAA,CAAA;;AAEL;AAEA,SAAgBU,4BAAyBA;AACvC,MAAAC,qBAAoB5C,iBAAgB,GAA5BxE,UAAOoH,mBAAPpH;AAER,SAAOA,QAAQgF,oBAAqB,WAAA;EAAA;AACtC;AAEA,SAAgBqC,mBAAgBA;AAC9B,MAAAC,qBAA0BnI,gBAAe,GAAjC7B,gBAAagK,mBAAbhK;AACR,SAAOA;AACT;AAEA,SAAgBiK,iBAAcA;AAC5B,MAAAC,qBAAkBrI,gBAAe,GAAzBpK,QAAKyS,mBAALzS;AAER,SAAOA;AACT;AAEA,SAAgB0S,+BAA4BA;AAC1C,MAAAC,qBAAgCvI,gBAAe,GAAvCnK,sBAAmB0S,mBAAnB1S;AACR,SAAOA;AACT;AAEA,SAAgB2S,0BAAuBA;AACrC,MAAAC,qBAA2BzI,gBAAe,GAAlClK,iBAAc2S,mBAAd3S;AACR,SAAOA;AACT;AAEA,SAAgB4S,qBAAkBA;AAChC,MAAAC,qBAAsB3I,gBAAe,GAA7BjK,YAAS4S,mBAAT5S;AACR,SAAOA;AACT;AAEA,SAAgB6S,iBAAcA;AAC5B,MAAAC,qBAAiC7I,gBAAe,GAAxC5L,SAAMyU,mBAANzU,QAAQC,QAAKwU,mBAALxU,OAAO2B,QAAK6S,mBAAL7S;AACvB,SAAA1C,SAAA;IAASc,QAAQ0U,aAAa1U,MAAM;IAAGC,OAAOyU,aAAazU,KAAK;KAAM2B,KAAK;AAC7E;AAEA,SAAgBuM,yBAAsBA;AACpC,MAAAwG,qBAAiC/I,gBAAe,GAAxC3K,uBAAoB0T,mBAApB1T;AACR,SAAOA;AACT;AAEA,SAAgBkM,wBAAqBA;AACnC,MAAAyH,qBAAyBhJ,gBAAe,GAAhC5K,eAAY4T,mBAAZ5T;AACR,SAAOA;AACT;AAEA,SAAgB6T,0BAAuBA;AACrC,MAAAC,qBAA2BlJ,gBAAe,GAAlC/J,iBAAciT,mBAAdjT;AACR,SAAOA;AACT;AAEA,SAAgBkT,kCAA+BA;AAC7C,MAAAC,qBAAmCpJ,gBAAe,GAA1C9J,yBAAsBkT,mBAAtBlT;AACR,SAAOA;AACT;AAEA,SAAgBgL,mBAAgBA;AAC9B,MAAAmI,qBAA0BrJ,gBAAe,GAAjCxB,gBAAa6K,mBAAb7K;AACR,SAAOA;AACT;AAEA,SAAgB8K,qBAAkBA;AAChC,MAAAC,qBAAsBvJ,gBAAe,GAA7Bf,YAASsK,mBAATtK;AACR,SAAOA;AACT;AAEA,SAAgBuK,uBAAoBA;AAIlC,MAAAC,qBAAwBzJ,gBAAe,GAA/BpB,cAAW6K,mBAAX7K;AACR,SAAOA;AACT;AAEA,SAASkK,aAAaY,iBAAiC;AACrD,SAAO,OAAOA,oBAAoB,WAC3BA,kBAAe,OAClBA;AACN;AAEA,SAAgBC,uBAAuBC,oBAA0B;AAC/D,MAAMC,aAAaD,qBAAqB;AACxC,MAAME,WAAWF,qBAAqB;AAEtC,MAAIC,YAAY;AACd,WAAOC,WACH/L,sCAAsCjD,QACpC,MACA8O,mBAAmBG,SAAQ,CAAE,IAE/BjM;;AAGN,SAAOF;AACT;SCvMwBoM,kBAAeA;AACrC,MAAAC,sBAAqB/F,mBAAkB,GAAhCnB,aAAUkH,oBAAA,CAAA;AAEjB,SAAO,CAAC,CAAClH;AACX;SCJgBmH,aAAaC,SAAwB;AACnD,MAAI,CAACA,SAAS;AACZ;;AAGFC,wBAAsB,WAAA;AACpBD,YAAQE,MAAK;GACd;AACH;AAEA,SAAgBC,wBAAwBH,SAAwB;AAC9D,MAAI,CAACA,QAAS;AAEd,MAAMvV,OAAOuV,QAAQI;AAErBL,eAAatV,IAAI;AACnB;AAEA,SAAgB4V,wBAAwBL,SAAwB;AAC9D,MAAI,CAACA,QAAS;AAEd,MAAMtV,OAAOsV,QAAQM;AAErBP,eAAarV,IAAI;AACnB;AAEA,SAAgB6V,uBAAuBP,SAAwB;AAC7D,MAAI,CAACA,QAAS;AAEd,MAAMQ,QAAQR,QAAQS;AAEtBV,eAAaS,KAAK;AACpB;SChCgBE,mBAAgBA;AAC9B,SAAOC,SAASC;AAClB;SCCgBC,0BAAyBlR,MAAA;MACvC0F,WAAQ1F,KAAR0F;AAIA,MAAMyL,oBAAgBzX,qBAA0B,IAAI;AACpD,MAAM0X,uBAAmB1X,qBAA0B,IAAI;AACvD,MAAM2X,cAAU3X,qBAA6B,IAAI;AACjD,MAAM4X,qBAAiB5X,qBAA+B,IAAI;AAC1D,MAAM6X,wBAAoB7X,qBAA6B,IAAI;AAC3D,MAAM8X,4BAAwB9X,qBAA6B,IAAI;AAC/D,MAAM+X,yBAAqB/X,qBAA6B,IAAI;AAC5D,MAAMgY,mBAAehY,qBAA+B,IAAI;AAExD,aACEA,4BAACiY,kBAAkB5L,UAAQ;IACzBrL,OAAO;MACL0W;MACAC;MACAG;MACAL;MACAG;MACAC;MACAE;MACAC;;KAGDhM,QAAQ;AAGf;AAiBA,IAAMiM,wBAAoBjY,4BAAiC;EACzD0X,sBAAkB1X,wBAAe;EACjC2X,aAAS3X,wBAAe;EACxB8X,2BAAuB9X,wBAAe;EACtCyX,mBAAezX,wBAAe;EAC9B4X,oBAAgB5X,wBAAe;EAC/B6X,uBAAmB7X,wBAAe;EAClC+X,wBAAoB/X,wBAAe;EACnCgY,kBAAchY,wBAAe;CAC9B;AAED,SAASkY,gBAAaA;AACpB,aAAOlY,yBAAiBiY,iBAAiB;AAC3C;AAEA,SAAgBE,mBAAgBA;AAC9B,SAAOD,cAAa,EAAG,eAAe;AACxC;AAEA,SAAgBE,sBAAmBA;AACjC,SAAOF,cAAa,EAAG,kBAAkB;AAC3C;AAEA,SAAgBG,yBAAsBA;AACpC,MAAMX,mBAAmBU,oBAAmB;AAC5C,SAAO,SAACE,QAAuB;AAC7B,QAAIA,WAAW,QAAQZ,iBAAiBrK,YAAY,MAAM;AACxDqJ,mBAAagB,iBAAiBrK,OAAO;;AAGvCqK,qBAAiBrK,UAAUiL;;AAE/B;AAEA,SAAgBC,aAAUA;AACxB,SAAOL,cAAa,EAAG,SAAS;AAClC;AAEA,SAAgBM,kBAAeA;AAC7B,SAAON,cAAa,EAAG,cAAc;AACvC;AAEA,SAAgBO,oBAAiBA;AAC/B,SAAOP,cAAa,EAAG,gBAAgB;AACzC;AAEA,SAAgBQ,uBAAoBA;AAClC,SAAOR,cAAa,EAAG,mBAAmB;AAC5C;AAEA,SAAgBS,2BAAwBA;AACtC,SAAOT,cAAa,EAAG,uBAAuB;AAChD;AAEA,SAAgBU,wBAAqBA;AACnC,SAAOV,cAAa,EAAG,oBAAoB;AAC7C;SC7FgBW,SAASC,MAAuBC,KAAAA;MAAAA,QAAAA,QAAAA;AAAAA,UAAc;;AAC5D,MAAMC,WAAWC,gBAAgBH,IAAI;AAErC,MAAI,CAACE,UAAU;AACb;;AAGFpC,wBAAsB,WAAA;AACpBoC,aAASE,YAAYH;GACtB;AACH;AAEA,SAAgBI,SAASL,MAAuBM,IAAU;AACxD,MAAMJ,WAAWC,gBAAgBH,IAAI;AAErC,MAAI,CAACE,UAAU;AACb;;AAGFpC,wBAAsB,WAAA;AACpBoC,aAASE,YAAYF,SAASE,YAAYE;GAC3C;AACH;AAEA,SAAgBC,cAAWA;AACzB,MAAM1B,UAAUY,WAAU;AAE1B,aAAOe,0BACL,SAACP,KAAW;AACVnC,0BAAsB,WAAA;AACpB,UAAIe,QAAQtK,SAAS;AACnBsK,gBAAQtK,QAAQ6L,YAAYH;;KAE/B;KAEH,CAACpB,OAAO,CAAC;AAEb;AAEA,SAAgB4B,sBAAsBvS,OAAsB;AAC1D,MAAI,CAACA,SAAS,CAACwS,mBAAmBxS,KAAK,GAAG;AACxC;;AAGF,MAAIA,MAAMyS,QAAQ9a,YAAYD,WAAWgb,eAAe,CAAC,GAAG;AAC1D;;AAGF,MAAMC,aAAaC,kBAAkB5S,KAAK;AAC1C,MAAMoS,KAAKS,2BAA2B7S,KAAK;AAC3CmS,WAASQ,YAAY,EAAEG,oBAAoBC,gBAAgB/S,KAAK,CAAC,IAAIoS,GAAG;AAC1E;SC1CgBY,uBAAuBC,QAAuB;AAC5D,MAAMjT,QAAQkT,kBAAkBD,MAAM;AACtCvD,eAAa1P,KAAK;AAClBuS,wBAAsBvS,KAAK;AAC7B;AAEA,SAAgBmT,+BAA+BF,QAAuB;AACpE,MAAMG,aAAaF,kBAAkBD,MAAM;AAE3CvD,eAAa0D,UAAU;AACvBA,gBAAU,OAAA,SAAVA,WAAYC,MAAK;AACnB;AAEA,SAAgBC,sBAAsBL,QAAuB;AAC3DvD,eAAa6D,iBAAiBN,MAAM,CAAC;AACvC;AAEA,SAAgBO,sBAAsB7D,SAAwB;AAC5D,MAAI,CAACA,SAAS;AACZ;;AAGF,MAAMtV,OAAOoZ,iBAAiB9D,OAAO;AAErC,MAAI,CAACtV,MAAM;AACT,WAAO2Y,uBAAuBU,aAAa/D,OAAO,CAAC;;AAGrDD,eAAarV,IAAI;AACjBkY,wBAAsBlY,IAAI;AAC5B;AAEA,SAAgBsZ,sBAAsBhE,SAAwB;AAC5D,MAAI,CAACA,SAAS;AACZ;;AAGF,MAAMvV,OAAOwZ,iBAAiBjE,OAAO;AAErC,MAAI,CAACvV,MAAM;AACT,WAAOkZ,sBAAsBO,aAAalE,OAAO,CAAC;;AAGpDD,eAAatV,IAAI;AACjBmY,wBAAsBnY,IAAI;AAC5B;AAEA,SAAgB0Z,0BACdnE,SACAoE,QAAkB;AAElB,MAAI,CAACpE,SAAS;AACZ;;AAGF,MAAMvV,OAAO4Z,qBAAqBrE,OAAO;AAEzC,MAAI,CAACvV,MAAM;AACT,WAAO2Z,OAAM;;AAGfrE,eAAatV,IAAI;AACjBmY,wBAAsBnY,IAAI;AAC5B;AAEA,SAAgB6Z,4BAA4BtE,SAAwB;AAClE,MAAI,CAACA,SAAS;AACZ;;AAGF,MAAMtV,OAAO6Z,uBAAuBvE,OAAO;AAE3C,SAAOD,aAAarV,IAAI;AAC1B;AAEA,SAAS2Z,qBAAqBrE,SAAoB;AAChD,MAAI,CAACA,SAAS;AACZ,WAAO;;AAGT,MAAMwE,kBAAkBC,uBAAuBzE,OAAO;AACtD,MAAM3S,WAAW+V,gBAAgBoB,eAAe;AAChD,MAAME,aAAaC,kBAAkBH,iBAAiBxE,OAAO;AAC7D,MAAM4E,MAAMC,UAAUL,iBAAiBxE,OAAO;AAC9C,MAAM8E,aAAaC,kBAAkBP,iBAAiBxE,OAAO;AAE7D,MAAI4E,QAAQ,GAAG;AACb,QAAMI,sBAAsBd,aAAa7W,QAAQ;AAEjD,QAAI,CAAC2X,qBAAqB;AACxB,aAAO;;AAGT,WAAOC;MACLC,iBAAiBF,mBAAmB;MACpC;;MACAF;MACAJ;IAAU;;AAId,SAAOS,oBACLD,iBAAiBV,eAAe,GAChCI,KACAE,YACAJ,UAAU;AAEd;AAEA,SAASH,uBAAuBvE,SAAoB;AAClD,MAAI,CAACA,SAAS;AACZ,WAAO;;AAGT,MAAMwE,kBAAkBC,uBAAuBzE,OAAO;AACtD,MAAM3S,WAAW+V,gBAAgBoB,eAAe;AAChD,MAAME,aAAaC,kBAAkBH,iBAAiBxE,OAAO;AAC7D,MAAM4E,MAAMC,UAAUL,iBAAiBxE,OAAO;AAC9C,MAAM8E,aAAaC,kBAAkBP,iBAAiBxE,OAAO;AAC7D,MAAI,CAACoF,WAAWZ,iBAAiBxE,OAAO,GAAG;AACzC,QAAMqF,sBAAsBtB,aAAa1W,QAAQ;AAEjD,QAAI,CAACgY,qBAAqB;AACxB,aAAO;;AAGT,WAAOJ,gBACLC,iBAAiBG,mBAAmB,GACpC,GACAP,YACAJ,UAAU;;AAId,MAAMY,gBAAgBC,oBACpBL,iBAAiBV,eAAe,GAChCI,KACAE,YACAJ,UAAU;AAGZ,SAAOY;AACT;SC/JgBE,yBAAsBA;AACpC,MAAAC,wBAA8ClL,6BAA4B,GAAnEwI,kBAAe0C,sBAAA,CAAA,GAAEC,qBAAkBD,sBAAA,CAAA;AAC1C,MAAAE,wBAA8ClL,wBAAuB,GAA9DmL,kBAAeD,sBAAA,CAAA,GAAEE,qBAAkBF,sBAAA,CAAA;AAE1C,MAAMG,0BAAsBnD,0BAAY,WAAA;AACtC,QAAII,iBAAiB;AACnB2C,yBAAmB,IAAI;;AAGzB,QAAIE,iBAAiB;AACnBC,yBAAmB,KAAK;;KAEzB,CACD9C,iBACA6C,iBACAF,oBACAG,kBAAkB,CACnB;AAED,SAAOC;AACT;AAEA,SAAgBC,oBAAiBA;AAC/B,MAAAC,yBAA0BzL,6BAA4B,GAA/CwI,kBAAeiD,uBAAA,CAAA;AACtB,MAAAC,yBAA0BxL,wBAAuB,GAA1CmL,kBAAeK,uBAAA,CAAA;AAEtB,SAAO,SAASC,iBAAcA;AAC5B,WAAO,CAAC,CAACnD,mBAAmB6C;;AAEhC;SC/BgBO,uBAAoBA;AAClC,MAAMC,mBAAmBzM,oBAAmB;AAC5C,SAAO,SAAS0M,oBAAiBA;AAC/BD,qBAAiB1P,UAAU;;AAE/B;AAEA,SAAgB4P,oBAAiBA;AAC/B,MAAMF,mBAAmBzM,oBAAmB;AAC5C,SAAO,SAAS4M,iBAAcA;AAC5BH,qBAAiB1P,UAAU;;AAE/B;AAEA,SAAgB8P,uBAAoBA;AAClC,MAAMJ,mBAAmBzM,oBAAmB;AAC5C,SAAO,SAAS8M,oBAAiBA;AAC/B,WAAOL,iBAAiB1P;;AAE5B;AAEA,SAAgBgQ,iBAAcA;AAC5B,MAAM1F,UAAUY,WAAU;AAC1B,MAAM2E,iBAAiBD,kBAAiB;AACxC,MAAMG,oBAAoBD,qBAAoB;AAE9CvO,8BAAU,WAAA;AACR,QAAM0O,UAAU3F,QAAQtK;AACxBiQ,eAAO,OAAA,SAAPA,QAASC,iBAAiB,aAAaC,aAAa;MAClDC,SAAS;KACV;AAED,aAASD,cAAWA;AAClB,UAAIJ,kBAAiB,GAAI;AACvBF,uBAAc;;;AAGlB,WAAO,WAAA;AACLI,iBAAO,OAAA,SAAPA,QAASI,oBAAoB,aAAaF,WAAW;;KAEtD,CAAC7F,SAASuF,gBAAgBE,iBAAiB,CAAC;AACjD;SCrCgBO,sBAAmBA;AACjC,MAAM/F,iBAAiBa,kBAAiB;AAExC,aAAOa,0BAAY,WAAA;AACjB5C,iBAAakB,eAAevK,OAAO;KAClC,CAACuK,cAAc,CAAC;AACrB;AAEA,SAAgBgG,yBAAsBA;AACpC,MAAM/F,oBAAoBa,qBAAoB;AAE9C,aAAOY,0BAAY,WAAA;AACjB,QAAI,CAACzB,kBAAkBxK,SAAS;AAC9B;;AAGF6J,2BAAuBW,kBAAkBxK,OAAO;KAC/C,CAACwK,iBAAiB,CAAC;AACxB;AAEA,SAAgBgG,6BAA0BA;AACxC,MAAM/F,wBAAwBa,yBAAwB;AAEtD,aAAOW,0BAAY,WAAA;AACjB,QAAI,CAACxB,sBAAsBzK,SAAS;AAClC;;AAGF6J,2BAAuBY,sBAAsBzK,OAAO;KACnD,CAACyK,qBAAqB,CAAC;AAC5B;ACvBA,SAASgG,kBAAeA;AACtB,MAAM9O,YAAYkB,aAAY;AAE9B,SAAO,SAAS6N,UACdC,QAA6D;AAE7D,QAAI,OAAOA,WAAW,YAAY;AAChC,aAAOD,UAAUC,OAAOhP,UAAU3B,OAAO,CAAC;;AAG5C2B,cAAU3B,UAAU2Q;;AAExB;AAEA,SAAgBC,iBAAcA;AAC5B,MAAMC,cAAcC,eAAc;AAClC,MAAMvG,iBAAiBa,kBAAiB;AACxC,MAAM2F,mBAAmBT,oBAAmB;AAE5C,SAAO,SAASU,cAAWA;AACzB,QAAIzG,eAAevK,SAAS;AAC1BuK,qBAAevK,QAAQrM,QAAQ;;AAGjCkd,gBAAY,EAAE;AACdE,qBAAgB;;AAEpB;AAEA,SAAgBE,kBAAeA;AAC7B,MAAM1G,iBAAiBa,kBAAiB;AACxC,MAAMyF,cAAcC,eAAc;AAElC,SAAO,SAASI,aAAaC,KAAW;AACtC,QAAI5G,eAAevK,SAAS;AAC1BuK,qBAAevK,QAAQrM,QAAK,KAAM4W,eAAevK,QAAQrM,QAAQwd;AACjEN,kBAAYO,wBAAwB7G,eAAevK,QAAQrM,KAAK,CAAC;WAC5D;AACLkd,kBAAYO,wBAAwBD,GAAG,CAAC;;;AAG9C;AAEA,SAAgBE,YAASA;AACvB,MAAM9G,iBAAiBa,kBAAiB;AACxC,MAAMzJ,YAAYkB,aAAY;AAC9B,MAAMyO,eAAeb,gBAAe;AACpC,MAAMI,cAAcC,eAAc;AAElC,MAAA1H,sBAAqB/F,mBAAkB,GAAhCnB,aAAUkH,oBAAA,CAAA;AACjB,MAAMmI,sBAAsBC,uBAC1B7P,UAAU3B,SACVkC,UAAU;AAGZ,SAAO;IACLuP;IACAvP;IACAqI;IACAgH;;AAGF,WAASE,SAASC,YAAkB;AAClC,QAAMC,SAAShQ,UAAU3B;AAEzB,QAAM4R,YAAYF,WAAW1X,YAAW;AAExC,QAAI2X,UAAM,QAANA,OAASC,SAAS,KAAKA,UAAUtc,UAAU,GAAG;AAChD,aAAOub,YAAYe,SAAS;;AAG9B,QAAMC,eAAeC,iBAAiBF,WAAWD,MAAM;AAEvD,QAAI,CAACE,cAAc;AAGjB,aAAOhB,YAAYe,SAAS;;AAG9BN,iBAAa,SAAAtR,SAAO;AAAA,UAAA3G;AAAA,aAClB1B,OAAOC,OAAOoI,UAAO3G,iBAAA,CAAA,GAAAA,eAClBuY,SAAS,IAAGG,2BAA2BF,cAAcD,SAAS,GAACvY,eAAA;;AAGpEwX,gBAAYe,SAAS;;AAEzB;AAEA,SAASd,iBAAcA;AACrB,MAAAkB,uBAA0B3O,mBAAkB,GAAnC4O,gBAAaD,qBAAA,CAAA;AACtB,MAAM5H,gBAAgBU,iBAAgB;AAEtC,SAAO,SAAS+F,YAAY3O,YAAkB;AAC5CqH,0BAAsB,WAAA;AACpB0I,oBAAc/P,aAAaA,cAAU,OAAA,SAAVA,WAAYlI,YAAW,IAAKkI,UAAU,EAAEgQ,KACjE,WAAA;AACE1G,iBAASpB,cAAcpK,SAAS,CAAC;OAClC;KAEJ;;AAEL;AAEA,SAAS+R,2BACPzW,SACA6W,SAAe;AAEf,MAAMC,WAAuB,CAAA;AAE7B,WAAWvX,WAAWS,SAAQ;AAC5B,QAAM3B,QAAQ2B,QAAOT,OAAO;AAE5B,QAAIwX,SAAS1Y,OAAOwY,OAAO,GAAG;AAC5BC,eAASvX,OAAO,IAAIlB;;;AAIxB,SAAOyY;AACT;AAEA,SAASC,SAAS1Y,OAAkBwY,SAAe;AACjD,SAAOrY,WAAWH,KAAK,EAAE2Y,KAAK,SAAA5b,MAAI;AAAA,WAAIA,KAAKmF,SAASsW,OAAO;;AAC7D;AAEA,SAAgBI,qBAAkBA;AAChC,MAAAC,gBAA4B3P,aAAY,GAAvB8O,SAAMa,cAAfxS;AACR,MAAAyS,uBAAqBpP,mBAAkB,GAAhCnB,aAAUuQ,qBAAA,CAAA;AAEjB,SAAO,SAAA5X,SAAO;AAAA,WAAI6X,4BAA4B7X,SAAS8W,QAAQzP,UAAU;;AAC3E;AAEA,SAASwQ,4BACP7X,SACA8W,QACAzP,YAAkB;;AAElB,MAAI,CAACyP,UAAU,CAACzP,YAAY;AAC1B,WAAO;;AAGT,SAAO,GAAAyQ,qBAAChB,OAAOzP,UAAU,MAAC,QAAlByQ,mBAAqB9X,OAAO;AACtC;AAIA,SAASiX,iBACPK,SACAS,MAAuC;AAEvC,MAAI,CAACA,MAAM;AACT,WAAO;;AAGT,MAAIA,KAAKT,OAAO,GAAG;AACjB,WAAOS,KAAKT,OAAO;;AAGrB,MAAMU,qBAAqBlb,OAAOmb,KAAKF,IAAI,EACxCG,KAAK,SAACC,GAAGC,GAAC;AAAA,WAAKA,EAAE3d,SAAS0d,EAAE1d;KAC5BqG,KAAK,SAAAjI,KAAG;AAAA,WAAIye,QAAQtW,SAASnI,GAAG;;AAEnC,MAAImf,oBAAoB;AACtB,WAAOD,KAAKC,kBAAkB;;AAGhC,SAAO;AACT;AAEA,SAAgBzB,wBAAwBD,KAAW;AACjD,MAAI,CAACA,OAAO,OAAOA,QAAQ,UAAU;AACnC,WAAO;;AAGT,SAAOA,IAAI+B,KAAI,EAAGlZ,YAAW;AAC/B;AAEA,SAASwX,uBACP2B,aACAjR,YAAkB;;AAElB,MAAI,EAACiR,eAAW,QAAXA,YAAcjR,UAAU,GAAG,QAAO;AAEvC,MAAM6G,uBACJqK,kBAAAzb,OAAOmB,QAAQqa,eAAW,OAAA,SAAXA,YAAcjR,UAAU,CAAC,MAAC,OAAA,SAAzCkR,gBAA2C9d,WAAU;AAEvD,SAAOwT,uBAAuBC,kBAAkB;AAClD;SCtMwBsK,wBAAqBA;AAC3C,MAAMC,sBAAsBtI,uBAAsB;AAClD,MAAA+D,wBAAoClL,6BAA4B,GAAvD0P,0BAAuBxE,sBAAA,CAAA;AAEhC,SAAO,SAASC,mBAAmB1F,SAAwB;AACzD,QAAAkK,oBAAgBC,iBAAiBnK,OAAO,GAAjC3P,QAAK6Z,kBAAA,CAAA;AAEZ,QAAI7Z,OAAO;AACT2Z,0BAAoBhK,OAAO;AAC3BiK,8BAAwB5Z,KAAK;;;AAGnC;SCLgB+Z,wBAAqBA;AACnC,MAAMC,+BAA+BrL,gCAA+B;AAEpE,SAAOqL,iCAAiC9d,uBAAuBoI;AACjE;AAEA,SAAgB2V,yBAAsBA;AACpC,MAAMD,+BAA+BrL,gCAA+B;AAEpE,SAAOqL,iCAAiC9d,uBAAuBiI;AACjE;ACyBA,IAAK+V;CAAL,SAAKA,iBAAc;AACjBA,EAAAA,gBAAAA,WAAAA,IAAAA;AACAA,EAAAA,gBAAAA,SAAAA,IAAAA;AACAA,EAAAA,gBAAAA,WAAAA,IAAAA;AACAA,EAAAA,gBAAAA,YAAAA,IAAAA;AACAA,EAAAA,gBAAAA,QAAAA,IAAAA;AACAA,EAAAA,gBAAAA,OAAAA,IAAAA;AACAA,EAAAA,gBAAAA,OAAAA,IAAAA;AACF,GARKA,mBAAAA,iBAAc,CAAA,EAAA;AAUnB,SAAgBC,wBAAqBA;AACnCC,8BAA2B;AAC3BC,+BAA4B;AAC5BC,kCAA+B;AAC/BC,sCAAmC;AACnCC,wBAAqB;AACvB;AAEA,SAASJ,8BAA2BA;AAClC,MAAM3J,gBAAgBU,iBAAgB;AACtC,MAAMkG,cAAcJ,eAAc;AAClC,MAAMpF,YAAWQ,YAAW;AAC5B,MAAMzB,iBAAiBa,kBAAiB;AACxC,MAAM2F,mBAAmBT,oBAAmB;AAC5C,MAAMd,iBAAiBH,kBAAiB;AACxC,MAAMM,oBAAoBF,qBAAoB;AAE9C,MAAML,sBAAsBN,uBAAsB;AAElD,MAAMsF,gBAAYzT,sBAChB,WAAA;AAAA,WACE,SAASyT,WAAUC,OAAoB;AACrC,UAAQ3gB,MAAQ2gB,MAAR3gB;AAERic,wBAAiB;AACjB,cAAQjc,KAAG;QAET,KAAKmgB,eAAeS;AAClBD,gBAAME,eAAc;AACpB,cAAI/E,eAAc,GAAI;AACpBJ,gCAAmB;AACnB;;AAEF4B,sBAAW;AACXxF,UAAAA,UAAS,CAAC;AACVuF,2BAAgB;AAChB;;;KAGR,CACEvF,WACAwF,aACA5B,qBACA2B,kBACAvB,gBACAG,iBAAiB,CAClB;AAGHpO,8BAAU,WAAA;AACR,QAAMvB,UAAUoK,cAAcpK;AAE9B,QAAI,CAACA,SAAS;AACZ;;AAGFA,YAAQkQ,iBAAiB,WAAWkE,SAAS;AAE7C,WAAO,WAAA;AACLpU,cAAQqQ,oBAAoB,WAAW+D,SAAS;;KAEjD,CAAChK,eAAeG,gBAAgBiB,WAAU4I,SAAS,CAAC;AACzD;AAEA,SAASJ,+BAA4BA;AACnC,MAAMQ,sBAAsBjE,uBAAsB;AAClD,MAAMnG,gBAAgBU,iBAAgB;AACtC,MAAMR,UAAUY,WAAU;AAC1B,MAAMX,iBAAiBa,kBAAiB;AACxC,MAAA6D,wBAAoClL,wBAAuB,GAAlD0Q,0BAAuBxF,sBAAA,CAAA;AAChC,MAAMyF,wBAAwBC,yBAAwB;AACtD,MAAMC,qBAAqBlB,sBAAqB;AAEhD,MAAMU,gBAAYzT,sBAChB,WAAA;AAAA,WACE,SAASyT,WAAUC,OAAoB;AACrC,UAAQ3gB,MAAQ2gB,MAAR3gB;AAER,cAAQA,KAAG;QACT,KAAKmgB,eAAegB;AAClB,cAAI,CAACD,oBAAoB;AACvB;;AAEFP,gBAAME,eAAc;AACpBE,kCAAwB,IAAI;AAC5BD,8BAAmB;AACnB;QACF,KAAKX,eAAeiB;AAClBT,gBAAME,eAAc;AACpBG,gCAAqB;AACrB;QACF,KAAKb,eAAekB;AAClBV,gBAAME,eAAc;AACpBzH,yCAA+BxC,QAAQtK,OAAO;AAC9C;;;KAGR,CACEwU,qBACAE,uBACAD,yBACAnK,SACAsK,kBAAkB,CACnB;AAGHrT,8BAAU,WAAA;AACR,QAAMvB,UAAUuK,eAAevK;AAE/B,QAAI,CAACA,SAAS;AACZ;;AAGFA,YAAQkQ,iBAAiB,WAAWkE,SAAS;AAE7C,WAAO,WAAA;AACLpU,cAAQqQ,oBAAoB,WAAW+D,SAAS;;KAEjD,CAAChK,eAAeG,gBAAgB6J,SAAS,CAAC;AAC/C;AAEA,SAASH,kCAA+BA;AACtC,MAAMzJ,oBAAoBa,qBAAoB;AAC9C,MAAM0F,mBAAmBT,oBAAmB;AAC5C,MAAM/F,iBAAiBa,kBAAiB;AACxC,MAAMsJ,wBAAwBC,yBAAwB;AACtD,MAAApF,yBAA4BxL,wBAAuB,GAA5CiR,SAAMzF,uBAAA,CAAA,GAAE0F,YAAS1F,uBAAA,CAAA;AACxB,MAAM2F,sBAAsBtB,uBAAsB;AAClD,MAAMgB,qBAAqBlB,sBAAqB;AAChD,MAAMyB,SAASC,UAAS;AAExB,MAAMhB,gBAAYzT,sBAChB,WAAA;AAAA;;MAEE,SAASyT,WAAUC,OAAoB;AACrC,YAAQ3gB,MAAQ2gB,MAAR3gB;AAER,YAAIkhB,oBAAoB;AACtB,kBAAQlhB,KAAG;YACT,KAAKmgB,eAAewB;AAClBhB,oBAAME,eAAc;AACpB,kBAAI,CAACS,QAAQ;AACX,uBAAOjE,iBAAgB;;AAEzBuE,gCAAkBvE,gBAAgB;AAClC;YACF,KAAK8C,eAAegB;AAClBR,oBAAME,eAAc;AACpB,kBAAI,CAACS,QAAQ;AACX,uBAAOjE,iBAAgB;;AAEzBwE,gCAAiB;AACjB;YACF,KAAK1B,eAAeiB;AAClBT,oBAAME,eAAc;AACpB,kBAAIS,QAAQ;AACVC,0BAAU,KAAK;;AAEjBP,oCAAqB;AACrB;YACF;AACES,qBAAOd,KAAK;AACZ;;;AAIN,YAAIa,qBAAqB;AACvB,kBAAQxhB,KAAG;YACT,KAAKmgB,eAAe2B;AAClBnB,oBAAME,eAAc;AACpB,kBAAI,CAACS,QAAQ;AACX,uBAAOjE,iBAAgB;;AAEzBuE,gCAAkBvE,gBAAgB;AAClC;YACF,KAAK8C,eAAeiB;AAClBT,oBAAME,eAAc;AACpB,kBAAI,CAACS,QAAQ;AACX,uBAAOjE,iBAAgB;;AAEzBwE,gCAAiB;AACjB;YACF;AACEJ,qBAAOd,KAAK;AACZ;;;;;KAIV,CACEW,QACAjE,kBACAkE,WACAP,uBACAS,QACAD,qBACAN,kBAAkB,CACnB;AAGHrT,8BAAU,WAAA;AACR,QAAMvB,UAAUwK,kBAAkBxK;AAElC,QAAI,CAACA,SAAS;AACZ;;AAGFA,YAAQkQ,iBAAiB,WAAWkE,SAAS;AAE7C,WAAO,WAAA;AACLpU,cAAQqQ,oBAAoB,WAAW+D,SAAS;;KAEjD,CAAC5J,mBAAmBD,gBAAgByK,QAAQZ,SAAS,CAAC;AAC3D;AAEA,SAASF,sCAAmCA;AAC1C,MAAMnD,mBAAmBT,oBAAmB;AAC5C,MAAM7F,wBAAwBa,yBAAwB;AACtD,MAAMhB,UAAUY,WAAU;AAC1B,MAAMiK,SAASC,UAAS;AAExB,MAAMhB,gBAAYzT,sBAChB,WAAA;AAAA,WACE,SAASyT,WAAUC,OAAoB;AACrC,UAAQ3gB,MAAQ2gB,MAAR3gB;AAER,cAAQA,KAAG;QACT,KAAKmgB,eAAe2B;AAClBnB,gBAAME,eAAc;AACpBxD,2BAAgB;AAChB;QACF,KAAK8C,eAAegB;AAClBR,gBAAME,eAAc;AACpB5K,kCAAwBK,iBAAgB,CAAE;AAC1C;QACF,KAAK6J,eAAewB;AAClBhB,gBAAME,eAAc;AACpB9K,kCAAwBO,iBAAgB,CAAE;AAC1C;QACF,KAAK6J,eAAeiB;AAClBT,gBAAME,eAAc;AACpB5H,iCAAuBrC,QAAQtK,OAAO;AACtC;QACF;AACEmV,iBAAOd,KAAK;AACZ;;;KAGR,CAAC/J,SAASyG,kBAAkBoE,MAAM,CAAC;AAGrC5T,8BAAU,WAAA;AACR,QAAMvB,UAAUyK,sBAAsBzK;AAEtC,QAAI,CAACA,SAAS;AACZ;;AAGFA,YAAQkQ,iBAAiB,WAAWkE,SAAS;AAE7C,WAAO,WAAA;AACLpU,cAAQqQ,oBAAoB,WAAW+D,SAAS;;KAEjD,CAAC3J,uBAAuBH,SAAS8J,SAAS,CAAC;AAChD;AAEA,SAASD,wBAAqBA;AAC5B,MAAM7J,UAAUY,WAAU;AAC1B,MAAMuK,eAAeC,gBAAe;AACpC,MAAM1G,qBAAqBqE,sBAAqB;AAChD,MAAM7D,iBAAiBH,kBAAiB;AACxC,MAAMD,sBAAsBN,uBAAsB;AAElD,MAAMqG,SAASC,UAAS;AAExB,MAAMhB,gBAAYzT,sBAChB,WAAA;AAAA;;MAEE,SAASyT,WAAUC,OAAoB;AACrC,YAAQ3gB,MAAQ2gB,MAAR3gB;AAER,YAAMwW,gBAAgByL,iBAAiB3L,iBAAgB,CAAE;AAEzD,gBAAQtW,KAAG;UACT,KAAKmgB,eAAegB;AAClBR,kBAAME,eAAc;AACpBpH,kCAAsBjD,aAAa;AACnC;UACF,KAAK2J,eAAewB;AAClBhB,kBAAME,eAAc;AACpBjH,kCAAsBpD,aAAa;AACnC;UACF,KAAK2J,eAAeiB;AAClBT,kBAAME,eAAc;AACpB,gBAAI/E,eAAc,GAAI;AACpBJ,kCAAmB;AACnB;;AAEFxB,wCAA4B1D,aAAa;AACzC;UACF,KAAK2J,eAAe2B;AAClBnB,kBAAME,eAAc;AACpB,gBAAI/E,eAAc,GAAI;AACpBJ,kCAAmB;AACnB;;AAEF3B,sCAA0BvD,eAAeuL,YAAY;AACrD;UACF,KAAK5B,eAAe+B;AAClBvB,kBAAME,eAAc;AACpBvF,+BAAmBqF,MAAMpJ,MAAqB;AAC9C;UACF;AACEkK,mBAAOd,KAAK;AACZ;;;;KAGR,CACEoB,cACAN,QACAnG,oBACAQ,gBACAJ,mBAAmB,CACpB;AAGH7N,8BAAU,WAAA;AACR,QAAMvB,UAAUsK,QAAQtK;AAExB,QAAI,CAACA,SAAS;AACZ;;AAGFA,YAAQkQ,iBAAiB,WAAWkE,SAAS;AAE7C,WAAO,WAAA;AACLpU,cAAQqQ,oBAAoB,WAAW+D,SAAS;;KAEjD,CAAC9J,SAAS8J,SAAS,CAAC;AACzB;AAEA,SAASO,2BAAwBA;AAC/B,MAAMkB,0BAA0BrF,2BAA0B;AAC1D,MAAMsF,eAAe3M,gBAAe;AACpC,MAAMmB,UAAUY,WAAU;AAE1B,aAAOe,0BACL,SAASyI,wBAAqBA;AAC5B,QAAIoB,cAAc;AAChB,aAAOnJ,uBAAuBrC,QAAQtK,OAAO;;AAE/C,WAAO6V,wBAAuB;KAEhC,CAACvL,SAASuL,yBAAyBC,YAAY,CAAC;AAEpD;AAEA,SAASJ,kBAAeA;AACtB,MAAM3E,mBAAmBT,oBAAmB;AAC5C,MAAMuF,0BAA0BrF,2BAA0B;AAC1D,MAAMsF,eAAe3M,gBAAe;AAEpC,aAAO8C,0BACL,SAAS8J,gBAAaA;AACpB,QAAID,cAAc;AAChB,aAAO/E,iBAAgB;;AAEzB,WAAO8E,wBAAuB;KAEhC,CAAC9E,kBAAkB+E,cAAcD,uBAAuB,CAAC;AAE7D;AAEA,SAASP,kBAAkBU,UAAoB;AAC7C,MAAMC,kBAAkBjM,iBAAgB;AAExC,MAAI,CAACiM,iBAAiB;AACpB;;AAGF,MAAI,CAACC,sBAAsBD,eAAe,GAAG;AAC3CD,aAAQ;;AAGVrM,0BAAwBsM,eAAe;AACzC;AAEA,SAASV,oBAAiBA;AACxB,MAAMU,kBAAkBjM,iBAAgB;AAExC,MAAI,CAACiM,iBAAiB;AACpB;;AAGFxM,0BAAwBwM,eAAe;AACzC;AAEA,SAASb,YAASA;AAChB,MAAMlE,eAAeD,gBAAe;AACpC,MAAMF,mBAAmBT,oBAAmB;AAC5C,MAAMlb,iBAAiBgT,wBAAuB;AAC9C,MAAMgH,sBAAsBN,uBAAsB;AAElD,SAAO,SAASqG,OAAOd,OAAoB;AACzC,QAAQ3gB,MAAQ2gB,MAAR3gB;AAER,QAAIyiB,YAAY9B,KAAK,KAAKjf,gBAAgB;AACxC;;AAGF,QAAI1B,IAAI0iB,MAAM,oBAAoB,GAAG;AACnC/B,YAAME,eAAc;AACpBnF,0BAAmB;AACnB2B,uBAAgB;AAChBG,mBAAaxd,GAAG;;;AAGtB;AAEA,SAASyiB,YAAY9B,OAAoB;AACvC,MAAQgC,UAA6BhC,MAA7BgC,SAASC,UAAoBjC,MAApBiC,SAASC,SAAWlC,MAAXkC;AAE1B,SAAOF,WAAWC,WAAWC;AAC/B;SCzdgBC,aACdzY,aACApE,OACA7E,YAAsB;AAEtB,MAAI,CAAC6E,OAAO;AACV;;AAGF,MAAI7E,eAAeW,WAAWghB,QAAQ;AACpC;;AAGF,MAAM5b,UAAUP,aAAaX,KAAK;AAElC,MAAI+c,eAAepW,IAAIzF,OAAO,GAAG;AAC/B;;AAGFW,kBAAgB7B,KAAK,EAAEQ,QAAQ,SAACyB,WAAS;AACvC,QAAM+a,WAAW5Y,YAAYnC,WAAW9G,UAAU;AAClD8hB,iBAAaD,QAAQ;GACtB;AAEDD,iBAAe9Y,IAAI/C,OAAO;AAC5B;AAEO,IAAM6b,iBAA8B,oBAAIvY,IAAG;AAElD,SAASyY,aAAaC,KAAW;AAC/B,MAAMC,QAAQ,IAAIC,MAAK;AACvBD,QAAME,MAAMH;AACd;SC3BgBI,aAAUA;AACxB,MAAM3M,UAAUY,WAAU;AAC1B,MAAMpW,aAAa6Q,oBAAmB;AACtC,MAAM5H,cAAc4K,qBAAoB;AAExCpH,8BAAU,WAAA;AACR,QAAIzM,eAAeW,WAAWghB,QAAQ;AACpC;;AAGF,QAAMxG,UAAU3F,QAAQtK;AAExBiQ,eAAO,OAAA,SAAPA,QAASC,iBAAiB,WAAWgH,OAAO;AAE5C,WAAO,WAAA;AACLjH,iBAAO,OAAA,SAAPA,QAASI,oBAAoB,WAAW6G,OAAO;;AAGjD,aAASA,QAAQ7C,OAAiB;AAChC,UAAM8C,SAASxB,iBAAiBtB,MAAMpJ,MAAqB;AAE3D,UAAI,CAACkM,QAAQ;AACX;;AAGF,UAAA3D,oBAAgBC,iBAAiB0D,MAAM,GAAhCxd,QAAK6Z,kBAAA,CAAA;AAEZ,UAAI,CAAC7Z,OAAO;AACV;;AAGF,UAAIsB,mBAAmBtB,KAAK,GAAG;AAC7B6c,qBAAazY,aAAapE,OAAO7E,UAAU;;;KAG9C,CAACwV,SAASxV,YAAYiJ,WAAW,CAAC;AACvC;;ACtBO,IAAMqZ,uBAAuB;AAEpC,SAAwBC,WAAUpe,MAAA;MAAG0F,WAAQ1F,KAAR0F;AACnC,aACEhM,4BAAC6O,uBAAqB,UACpB7O,4BAAC2kB,mBAAiB,MAAE3Y,QAAQ,CAAqB;AAGvD;AAQA,SAAS2Y,kBAAiBC,OAAA;;MAAG5Y,WAAQ4Y,MAAR5Y;AAC3B,MAAA6H,wBAAwBrD,sBAAqB,GAAtCqU,gBAAahR,sBAAA,CAAA;AACpB,MAAMzR,QAAQwS,eAAc;AAC5B,MAAMkQ,mBAAmBtO,gBAAe;AACxC,MAAMiB,gBAAgBU,iBAAgB;AACtC,MAAM5V,YAAY2S,mBAAkB;AACpC,MAAM1S,QAAQ4S,eAAc;AAE5B+L,wBAAqB;AACrBmD,aAAU;AAEV,MAAAS,QAAyCviB,SAAS,CAAA,GAA1C3B,QAAKkkB,MAALlkB,OAAOD,SAAMmkB,MAANnkB,QAAWokB,aAAU/Y,8BAAA8Y,OAAA7Y,WAAA;AAEpC,aACElM,4BAAAA,SAAAA;IACEuC,WAAW0iB,GACTC,OAAOC,MACPD,OAAOE,eACPhjB,UAAUW,MAAMkD,QAAQif,OAAOG,WAC/BjjB,UAAUW,MAAMuiB,QAAQJ,OAAOK,gBAAaC,MAAA,CAAA,GAAAA,IAEzC9mB,WAAW+mB,YAAY,IAAGX,kBAAgBU,MAE7CX,iBAAiBK,OAAOQ,eACxBnjB,SAAS;IAEXojB,KAAKlO;IACLjV,OAAK1C,SAAA,CAAA,GACAklB,YACC,CAACH,iBAAiB;MAAEjkB;MAAQC;KAAO;KAGxCmL,QAAQ;AAGf;AAEA,IAAM4Z,YAAY;EAChB,yCACE;EACF,wCACE;EACF,yBAAyB;EACzB,oBAAoB;EACpB,wBAAwB;EACxB,wBAAwB;EACxB,+BAA+B;EAC/B,iCAAiC;EACjC,6BAA6B;EAC7B,kBAAkB;EAClB,4BAA4B;EAC5B,sCACE;EACF,yCACE;EACF,oCACE;EACF,qCACE;EACF,sCAAsC;EACtC,sCAAsC;;AAGxC,IAAMV,SAAS9lB,WAAWS,OAAO;EAC/BslB,MAAM;IACJ,KAAK,CAAC,YAAYzmB,WAAWmnB,WAAW;IACxCC,UAAU;IACVvmB,SAAS;IACTwmB,eAAe;IACfC,aAAa;IACbC,aAAa;IACbC,cAAc;IACdC,aAAa;IACbC,iBAAiB;IACjBzmB,UAAU;IACVgB,YAAY;IACZ,KAAK;MACH0lB,WAAW;MACXC,YAAY;;;EAGhBlB,eAAe;IACb,MAAM;MACJ,yBAAyB;MACzB,wBAAwB;MACxB,wCAAwC;MACxC,wBAAwB;MACxB,oBAAoB;MACpB,+BAA+B;MAC/B,6BAA6B;MAC7B,kBAAkB;MAClB,4BAA4B;MAC5B,oCAAoC;MACpC,qCAAqC;MACrC,sCAAsC;MACtC,sCAAsC;MAEtC,4BAA4B;MAE5B,8BAA8B;;MAG9B,6BAA6B;MAC7B,wBAAwB;;MAGxB,iDACE;MACF,iCAAiC;;MAGjC,sCAAsC;MACtC,8BAA8B;MAC9B,oCAAoC;MACpC,6BAA6B;MAC7B,iCAAiC;MACjC,wCAAwC;MACxC,kCAAkC;;MAGlC,yCAAyC;;MAGzC,uCAAuC;MACvC,yCAAyC;;MAGzC,wBAAwB;MACxB,2BAA2B;MAC3B,8BAA8B;MAC9B,8BAA8B;MAC9B,4BAA4B;;MAG5B,0BAA0B;;MAG1B,iCAAiC;MACjC,mCAAmC;MACnC,gCAAgC;MAChC,+BAAkCX,uBAAoB;;MAGtD,oBAAoB;MACpB,uBAAuB;MACvB,wBACE;MACF,2BAA2B;MAC3B,yCAAyC;MACzC,+CAA+C;;MAG/C,gCAAgC;MAChC,6CAA6C;MAC7C,gCAAgC;MAChC,uCAAuC;MACvC,yBAAyB;;MAGzB,cAAc;MACd,8CAA8C;MAC9C,8BAA8B;MAC9B,yBAAyB;MACzB,6BAA6B;MAC7B,6CAA6C;MAC7C,6BAA6B;MAC7B,oCAAoC;MACpC,sCAAsC;MACtC,kCAAkC;MAClC,uBAAuB;MACvB,iCAAiC;MACjC,2CAA2C;MAC3C,8CAA8C;MAC9C,yCAAyC;MACzC,0CAA0C;MAC1C,2CAA2C;MAC3C,2CAA2C;;;EAG/Cc,eAAe;IACb,KAAK7mB,WAAW6nB;IAChB,uCAAuC;MACrC,MAAMX;;;EAGVP,WAAW;IACT,KAAK3mB,WAAW2mB;IAChB,MAAMO;;EAERF,eAAe;IACb,KAAK;IACL9kB,QAAQ;IACRrB,SAAS;IACT6mB,iBAAiB;;IAEjBI,gBAAgB;IAChB,MAAM;MACJ,8BAA8B;;;CAGnC;SC3Oe9K,kBACdzB,QACAtD,SAAwB;AAExB,MAAI,CAACsD,UAAU,CAACtD,SAAS;AACvB,WAAO;;AAGT,MAAM8P,cAAcxM,OAAOyM,sBAAqB,EAAG7lB;AACnD,MAAM8lB,eAAehQ,QAAQ+P,sBAAqB,EAAG7lB;AACrD,SAAO+lB,KAAKC,MAAMJ,cAAcE,YAAY;AAC9C;AAEA,SAAgBrL,kBACdrB,QACAtD,SAAwB;AAExB,MAAI,CAACsD,UAAU,CAACtD,SAAS;AACvB,WAAO;;AAGT,MAAMgQ,eAAehQ,QAAQ+P,sBAAqB,EAAG7lB;AACrD,MAAMimB,cAAcnQ,QAAQ+P,sBAAqB,EAAGK;AACpD,MAAMC,aAAa/M,OAAOyM,sBAAqB,EAAGK;AAElD,SAAOH,KAAKC,OAAOC,cAAcE,cAAcL,YAAY;AAC7D;AAEA,SAAgBnL,UACdvB,QACAtD,SAAwB;AAExB,MAAI,CAACsD,UAAU,CAACtD,SAAS;AACvB,WAAO;;AAGT,MAAMsQ,iBAAgBtQ,QAAQ+P,sBAAqB,EAAG9lB;AACtD,MAAMsmB,aAAavQ,QAAQ+P,sBAAqB,EAAG3N;AACnD,MAAMoO,YAAYlN,OAAOyM,sBAAqB,EAAG3N;AACjD,SAAO6N,KAAKQ,OAAOF,aAAaC,aAAaF,cAAa;AAC5D;AAEA,SAAgBlL,WACd9B,QACAtD,SAAwB;AAExB,MAAI,CAACsD,UAAU,CAACtD,SAAS;AACvB,WAAO;;AAGT,MAAMsQ,iBAAgBtQ,QAAQ+P,sBAAqB,EAAG9lB;AACtD,MAAMsmB,aAAavQ,QAAQ+P,sBAAqB,EAAG3N;AACnD,MAAMoO,YAAYlN,OAAOyM,sBAAqB,EAAG3N;AACjD,MAAMsO,eAAepN,OAAOyM,sBAAqB,EAAG9lB;AAEpD,SAAOgmB,KAAKQ,MAAMF,aAAaC,YAAYF,cAAa,IAAII;AAC9D;AAEA,SAASC,eACPC,UACAhM,KACAiM,eAAqB;AAErB,MAAIjM,QAAQ,IAAI;AACd,QAAMkM,UAAUb,KAAKC,OAAOU,SAAS5kB,SAAS,KAAK6kB,aAAa;AAChE,QAAME,oBAAoBD,UAAUD;AACpC,QAAMG,mBAAmBJ,SAAS5kB,SAAS;AAC3C,WAAO4kB,SAASK,MAAMF,mBAAmBC,mBAAmB,CAAC;;AAG/D,SAAOJ,SAASK,MAAMrM,MAAMiM,gBAAgBjM,MAAM,KAAKiM,aAAa;AACtE;AAEA,SAASK,mBACPC,aACAC,YACAP,eAAqB;AAErB,MAAMQ,UAAUD,aAAa;AAE7B,MAAIC,UAAUR,gBAAgBM,YAAYnlB,QAAQ;AAChD,WAAO,CAAA;;AAGT,SAAO2kB,eAAeQ,aAAaE,SAASR,aAAa;AAC3D;AAEA,SAAgB5L,gBACd2L,UACAhM,KACAiM,eACAnM,YAAkB;AAElB,MAAM4M,cAAcX,eAAeC,UAAUhM,KAAKiM,aAAa;AAE/D,SAAOS,YAAY5M,UAAU,KAAK4M,YAAYA,YAAYtlB,SAAS,CAAC,KAAK;AAC3E;AAEA,SAAgBuZ,oBACd4L,aACAC,YACAP,eACAU,OAAa;AAEb,MAAMC,kBAAkBN,mBACtBC,aACAC,YACAP,aAAa;AAIf,SACEW,gBAAgBD,KAAK,KACrBC,gBAAgBA,gBAAgBxlB,SAAS,CAAC,KAC1C;AAEJ;AAEA,SAAgBmZ,oBACdgM,aACAC,YACAP,eACAU,OAAa;AAEb,MAAME,kBAAkBd,eACtBQ,aACAC,aAAa,GACbP,aAAa;AAIf,SACEY,gBAAgBF,KAAK,KACrBE,gBAAgBA,gBAAgBzlB,SAAS,CAAC,KAC1C;AAEJ;AAEA,SAAgB0lB,+BACdpO,QACAsN,UACAe,4BAA0B;MAA1BA,+BAA0B,QAAA;AAA1BA,iCAA6B;;AAE7B,MAAI,CAACrO,UAAU,CAACsN,SAAS5kB,QAAQ;AAC/B,WAAO;;AAGT,MAAMwkB,YAAYlN,OAAOyM,sBAAqB,EAAG3N;AACjD,MAAMwP,eAAetO,OAAOyM,sBAAqB,EAAG8B;AACpD,MAAMC,qBAAqBtB,YAAYuB,eAAezO,MAAM;AAE5D,MAAM0O,kBAAkBpB,SAASve,KAAK,SAAA2N,SAAO;AAC3C,QAAMuQ,aAAavQ,QAAQ+P,sBAAqB,EAAG3N;AACnD,QAAM6P,gBAAgBjS,QAAQ+P,sBAAqB,EAAG8B;AACtD,QAAMK,0BACJlS,QAAQmS,eAAeR;AAEzB,QAAMS,4BAA4B7B,aAAa2B;AAC/C,QAAMG,+BACJJ,gBAAgBC;AAElB,QAAIE,4BAA4BN,oBAAoB;AAClD,aAAO;;AAGT,WACGM,6BAA6B5B,aAC5B4B,6BAA6BR,gBAC9BS,gCAAgC7B,aAC/B6B,gCAAgCT;GAErC;AAED,SAAOI,mBAAmB;AAC5B;AAEA,SAAgBpF,sBAAsB5M,SAAoB;AACxD,SAAO,CAAC,CAACA,QAAQM;AACnB;AAEA,SAASyR,eAAeO,YAAuB;AAC7C,MAAMC,SAASrqB,MAAMsqB,KACnBF,WAAWG,iBAAiBzqB,YAAYD,WAAW2qB,KAAK,CAAC,CAAC;AAG5D,WAAAC,KAAA,GAAAC,UAAoBL,QAAMI,KAAAC,QAAA5mB,QAAA2mB,MAAE;AAAvB,QAAMD,QAAKE,QAAAD,EAAA;AACd,QAAM1oB,SAASyoB,MAAM3C,sBAAqB,EAAG9lB;AAE7C,QAAIA,SAAS,GAAG;AACd,aAAOA;;;AAIX,SAAO6jB;AACT;AC5LO,IAAM+E,sBAAmB,WAAY7qB,YAAYD,WAAWsI,KAAK;AACjE,IAAMyiB,uBAAuB,CAClCD,qBACA7qB,YAAYD,WAAWgrB,OAAO,GAAC,UACvB/qB,YAAYD,WAAWY,MAAM,IAAC,GAAA,EACtCH,KAAK,EAAE;AAET,SAAgB6jB,iBACd2G,cAA6B;;AAE7B,UAAAC,wBAAOD,gBAAY,OAAA,SAAZA,aAAclQ,QAAQ+P,mBAAmB,MAAC,OAAAI,wBAAI;AACvD;AAEA,SAQgB9I,iBACdnK,SAAwB;AAExB,MAAMkT,kBAAkBC,gCAAgCnT,OAAO;AAC/D,MAAMzO,UAAU6hB,wBAAwBpT,OAAO;AAE/C,MAAI,CAACkT,iBAAiB;AACpB,WAAO,CAAA;;AAGT,MAAM7iB,QAAQmC,eAAejB,WAAO,OAAPA,UAAW2hB,eAAe;AAEvD,MAAI,CAAC7iB,OAAO;AACV,WAAO,CAAA;;AAGT,SAAO,CAACA,OAAOkB,OAAiB;AAClC;AAEA,SAAgB8hB,eAAerT,SAAwB;;AACrD,SAAOnI,SACLmI,WAAO,OAAA,SAAPA,QAASsT,QAAQT,mBAAmB,OAClC7S,WAAO,OAAA,UAAAuT,wBAAPvT,QAASwT,kBAAa,OAAA,SAAtBD,sBAAwBD,QAAQT,mBAAmB,EAAC;AAE1D;AAEA,SAagBvC,cAActQ,SAAwB;;AACpD,UAAAyT,wBAAOzT,WAAO,OAAA,SAAPA,QAASmS,iBAAY,OAAAsB,wBAAI;AAClC;AAEA,SAAgBC,mBAAmB1T,SAAwB;AACzD,MAAI,CAACA,SAAS;AACZ,WAAO;;AAGT,MAAM6N,SAASxB,iBAAiBrM,OAAO;AACvC,MAAM3S,WAAW+V,gBAAgByK,MAAM;AAGvC,MAAM8F,cAAcxQ,oBAAoB9V,QAAQ;AAEhD,SAAOumB,iBAAiB/F,MAAM,IAAI+F,iBAAiBvmB,QAAQ,IAAIsmB;AACjE;AAEA,SAAgBxQ,oBAAoB9V,UAAyB;;AAC3D,MAAI,CAACA,UAAU;AACb,WAAO;;AAGT,MAAMwmB,uBAAuBxmB,SAASymB,cACpC9rB,YAAYD,WAAWyc,eAAe,CAAC;AAGzC,WACEuP,wBAAC1mB,YAAQ,OAAA,SAARA,SAAU8kB,iBAAY,OAAA4B,wBAAI,OAACC,wBAAKH,wBAAoB,OAAA,SAApBA,qBAAsB1B,iBAAY,OAAA6B,wBAAI;AAE3E;AAEA,SAAgBnR,mBAAmBxS,OAAsB;AACvD,MAAI,CAACA,OAAO;AACV,WAAO;;AAGT,SACE6S,2BAA2B7S,KAAK,IAChC8S,oBAAoBC,gBAAgB/S,KAAK,CAAC;AAE9C;AAEA,SAAgBiS,gBAAgBH,MAAqB;AACnD,MAAI,CAACA,KAAM,QAAO;AAElB,SAAOA,KAAKmR,QAAQtrB,YAAYD,WAAWib,UAAU,CAAC,IAClDb,OACAA,KAAK2R,cAAc9rB,YAAYD,WAAWib,UAAU,CAAC;AAC3D;AAEA,SAAgBE,2BAA2B7S,OAAsB;;AAC/D,MAAI,CAACA,OAAO;AACV,WAAO;;AAGT,SAAOqjB,mBAAmBrjB,KAAK,MAAC4jB,yBAAAC,qBAAIjR,kBAAkB5S,KAAK,MAAC,OAAA,SAAxB6jB,mBAA0B3R,cAAS,OAAA0R,wBAAI;AAC7E;AAEA,SAAgBhR,kBAAkBjD,SAAwB;;AACxD,MAAI,CAACA,SAAS;AACZ,WAAO;;AAGT,UAAAmU,mBAAOnU,QAAQ8C,QAAQ9a,YAAYD,WAAWib,UAAU,CAAC,MAAC,OAAAmR,mBAAI;AAChE;AAEA,SAAgBC,mBAAmBpU,SAAwB;AACzD,MAAM6N,SAASxB,iBAAiBrM,OAAO;AACvC,MAAM3S,WAAW+V,gBAAgByK,MAAM;AAEvC,SAAOwG,kBAAkBxG,MAAM,IAAIwG,kBAAkBhnB,QAAQ;AAC/D;AAEA,SAASumB,iBAAiB5T,SAAwB;;AAChD,UAAAsU,qBAAOtU,WAAO,OAAA,SAAPA,QAASuU,cAAS,OAAAD,qBAAI;AAC/B;AAEA,SAASD,kBAAkBrU,SAAwB;;AACjD,UAAAwU,sBAAOxU,WAAO,OAAA,SAAPA,QAASyU,eAAU,OAAAD,sBAAI;AAChC;AAEA,SAAgBpB,wBAAwB/iB,OAAsB;;AAC5D,UAAAqkB,qBAAOC,kBAAkBtI,iBAAiBhc,KAAK,GAAG,SAAS,MAAC,OAAAqkB,qBAAI;AAClE;AAEA,SAAgBvB,gCACd9iB,OAAsB;AAEtB,MAAMkB,UAAU6hB,wBAAwB/iB,KAAK;AAE7C,MAAIkB,SAAS;AACX,WAAOD,uBAAuBC,OAAO;;AAEvC,SAAO;AACT;AAEA,SAAgBqjB,2BACdvkB,OAAsB;AAEtB,MAAI,CAACA,OAAO;AACV,WAAO;MACLkB,SAAS;MACT2hB,iBAAiB;;;AAIrB,SAAO;IACL3hB,SAAS6hB,wBAAwB/iB,KAAK;IACtC6iB,iBAAiBC,gCAAgC9iB,KAAK;;AAE1D;AAEA,SAASskB,kBACP3U,SACA5V,KAAW;;AAEX,UAAAyqB,sBAAOC,eAAe9U,OAAO,EAAE5V,GAAG,MAAC,OAAAyqB,sBAAI;AACzC;AAEA,SAASC,eAAe9U,SAAwB;;AAC9C,UAAA+U,mBAAO/U,WAAO,OAAA,SAAPA,QAASgV,YAAO,OAAAD,mBAAI,CAAA;AAC7B;AAEA,SAAgBE,eAAejV,SAAoB;AACjD,SAAOA,QAAQkV,UAAUC,SAASptB,WAAWgrB,OAAO;AACtD;AAEA,SAAgBqC,SAASpV,SAAwB;AAC/C,MAAI,CAACA,QAAS,QAAO;AAErB,SAAOA,QAAQkV,UAAUC,SAASptB,WAAWY,MAAM;AACrD;AAEA,SAAgBuc,iBAAiB5B,QAAuB;AACtD,MAAI,CAACA,QAAQ;AACX,WAAO,CAAA;;AAGT,SAAOpb,MAAMsqB,KACXlP,OAAOmP,iBAAiBK,oBAAoB,CAAC;AAEjD;AAEA,SAAgBlP,iBAAiB5D,SAAwB;AACvD,MAAI,CAACA,QAAS,QAAO;AAErB,MAAM7P,aAAY+U,iBAAiBlF,OAAO;AAC1C,MAAAqV,mBAAellB,WAAU8gB,MAAM,EAAE,GAA1BqE,OAAID,iBAAA,CAAA;AACX,MAAI,CAACC,MAAM;AACT,WAAO;;AAGT,MAAI,CAACL,eAAeK,IAAI,GAAG;AACzB,WAAOrR,iBAAiBqR,IAAI;;AAG9B,SAAOA;AACT;AAEA,SAAgBxR,iBAAiB9D,SAAoB;AACnD,MAAMtV,OAAOsV,QAAQM;AAErB,MAAI,CAAC5V,MAAM;AACT,WAAO6Y,kBAAkBQ,aAAa/D,OAAO,CAAC;;AAGhD,MAAI,CAACiV,eAAevqB,IAAI,GAAG;AACzB,WAAOoZ,iBAAiBpZ,IAAI;;AAG9B,SAAOA;AACT;AAEA,SAAgBuZ,iBAAiBjE,SAAoB;AACnD,MAAMvV,OAAOuV,QAAQI;AAErB,MAAI,CAAC3V,MAAM;AACT,WAAOmZ,iBAAiBM,aAAalE,OAAO,CAAC;;AAG/C,MAAI,CAACiV,eAAexqB,IAAI,GAAG;AACzB,WAAOwZ,iBAAiBxZ,IAAI;;AAG9B,SAAOA;AACT;AAEA,SAAgB8Y,kBAAkBD,QAAuB;AACvD,MAAI,CAACA,QAAQ;AACX,WAAO;;AAGT,MAAMnT,aAAY+U,iBAAiB5B,MAAM;AAEzC,SAAOoO,+BAA+BpO,QAAQnT,YAAW,GAAG;AAC9D;AAEA,SAAgB+T,aAAalE,SAAwB;AACnD,MAAM3S,WAAW+V,gBAAgBpD,OAAO;AAExC,MAAI,CAAC3S,UAAU;AACb,WAAO;;AAGT,MAAM5C,OAAO4C,SAAS+S;AAEtB,MAAI,CAAC3V,MAAM;AACT,WAAO;;AAGT,MAAI2qB,SAAS3qB,IAAI,GAAG;AAClB,WAAOyZ,aAAazZ,IAAI;;AAG1B,SAAOA;AACT;AAEA,SAAgBsZ,aAAa/D,SAAwB;AACnD,MAAM3S,WAAW+V,gBAAgBpD,OAAO;AAExC,MAAI,CAAC3S,UAAU;AACb,WAAO;;AAGT,MAAM3C,OAAO2C,SAASiT;AAEtB,MAAI,CAAC5V,MAAM;AACT,WAAO;;AAGT,MAAI0qB,SAAS1qB,IAAI,GAAG;AAClB,WAAOqZ,aAAarZ,IAAI;;AAG1B,SAAOA;AACT;AAEA,SAAgB0Y,gBAAgBpD,SAAwB;AACtD,MAAI,CAACA,SAAS;AACZ,WAAO;;AAET,SAAOA,QAAQ8C,QAAQ9a,YAAYD,WAAWsF,QAAQ,CAAC;AACzD;AAEA,SAAgBoX,uBAAuBzE,SAAwB;AAC7D,MAAI,CAACA,SAAS;AACZ,WAAO;;AAET,SAAOA,QAAQ8C,QACb9a,YAAYD,WAAWyc,eAAe,CAAC;AAE3C;SCnUgB+Q,iBAAiBhkB,SAAe;AAC9C,SAAOA,QACJX,MAAM,GAAG,EACTtI,IAAI,SAAAktB,KAAG;AAAA,WAAIC,OAAOC,cAAcC,SAASH,KAAK,EAAE,CAAC;KACjDhtB,KAAK,EAAE;AACZ;ACAA,IAAMotB,mBAAmB;AAUzB,SAAgBC,aAAaC,MAAqB;AAChD,MAAI;AAAA,QAAAlf,SAAAmf,uBAAAC;AACF,QAAI,GAAApf,UAACC,WAAM,QAAND,QAAQqf,eAAc;AACzB,aAAO,CAAA;;AAET,QAAMC,SAASC,KAAKC,OAAKL,yBAAAC,WACvBnf,WAAM,OAAA,SAANmf,SAAQC,aAAaI,QAAQT,gBAAgB,MAAC,OAAAG,wBAAI,IAAI;AAGxD,QAAID,SAAS5pB,eAAe0I,UAAU;AACpC,aAAOshB,OAAOzM,KAAK,SAACC,GAAGC,GAAC;AAAA,eAAKA,EAAE2M,QAAQ5M,EAAE4M;;;AAG3C,WAAOJ;WACPK,SAAM;AACN,WAAO,CAAA;;AAEX;AAEA,SAAgBC,aAAanmB,OAAkBP,UAAmB;AAChE,MAAMomB,SAASL,aAAY;AAE3B,MAAMtkB,UAAUP,aAAaX,OAAOP,QAAQ;AAC5C,MAAMojB,kBAAkBliB,aAAaX,KAAK;AAE1C,MAAIomB,WAAWP,OAAO7jB,KAAK,SAAA1C,MAAA;AAAA,QAAY+mB,IAAC/mB,KAAV4B;AAAO,WAAUmlB,MAAMnlB;;AAErD,MAAIolB;AAEJ,MAAIF,UAAU;AACZE,eAAW,CAACF,QAAQ,EAAEjZ,OAAO0Y,OAAO7N,OAAO,SAAAuO,GAAC;AAAA,aAAIA,MAAMH;MAAS;SAC1D;AACLA,eAAW;MACTllB;MACAslB,UAAU3D;MACVoD,OAAO;;AAETK,eAAQ,CAAIF,QAAQ,EAAAjZ,OAAK0Y,MAAM;;AAGjCO,WAASH;AAETK,WAAS3qB,SAASikB,KAAK6G,IAAIH,SAAS3qB,QAAQ,EAAE;AAE9C,MAAI;AAAA,QAAA+qB;AACF,KAAAA,WAAAlgB,WAAM,OAAA,SAANkgB,SAAQd,aAAae,QAAQpB,kBAAkBO,KAAKc,UAAUN,QAAQ,CAAC;WAEvEO,UAAM;;AAGV;SCzDgBC,iBACd9pB,UAA+C;AAE/C,SAAOA,SAASA,aAAaf,WAAWI;AAC1C;AAEA,SAAgB0qB,cAAc/mB,OAAyB;AACrD,SAAOA,MAAM6C,WAAWoG;AAC1B;SCoBgB+d,qBACdC,cACAta,kBAAoC;AAEpC,MAAMua,wBAAoBjhB,qBAAM;AAChC,MAAMoP,qBAAqBqE,sBAAqB;AAChD,MAAMzR,mBAAmBmB,oBAAmB;AAC5C,MAAAgM,wBAAoClL,6BAA4B,GAAvD0P,0BAAuBxE,sBAAA,CAAA;AAChC,MAAMK,sBAAsBN,uBAAsB;AAClD,MAAAgS,wBAAyBvd,uBAAsB,GAAxCnB,iBAAc0e,sBAAA,CAAA;AACrB,MAAMjc,eAAewB,sBAAsBC,gBAAgB;AAC3D,MAAAya,sBAA4B9c,mBAAkB,GAArCI,kBAAe0c,oBAAA,CAAA;AACxB,MAAMhjB,cAAc4K,qBAAoB;AACxC,MAAMqY,mBAAmBrb,oBAAmB;AAE5C,MAAMsb,cAAUtuB,0BACd,SAASsuB,SAAQ5M,OAAiB;AAChC,QAAIzS,iBAAiB5B,SAAS;AAC5B;;AAGFoP,wBAAmB;AAEnB,QAAA8R,kBAAyBC,eAAe9M,KAAK,GAAtC1a,QAAKunB,gBAAA,CAAA,GAAErmB,UAAOqmB,gBAAA,CAAA;AAErB,QAAI,CAACvnB,SAAS,CAACkB,SAAS;AACtB;;AAGF,QAAMumB,gBACJ1kB,2BAA2B7B,OAAO,KAAKuH;AAEzCiC,oBAAe;AACfyb,iBAAanmB,OAAOynB,aAAa;AACjCvc,iBACEwc,iBAAiB1nB,OAAOynB,eAAeJ,kBAAkBjjB,WAAW,GACpEsW,KAAK;KAGT,CACEjS,gBACAgN,qBACAxN,kBACAiD,cACAR,iBACAtG,aACAijB,gBAAgB,CACjB;AAGH,MAAMM,kBAAc3uB,0BAClB,SAAS2uB,aAAYjN,OAAiB;;AACpC,QAAIwM,kBAAkB7gB,SAAS;AAC7BC,mBAAa4gB,kBAAkB7gB,OAAO;;AAGxC,QAAAuhB,mBAAgBJ,eAAe9M,KAAK,GAA7B1a,QAAK4nB,iBAAA,CAAA;AAEZ,QAAI,CAAC5nB,SAAS,CAACsB,mBAAmBtB,KAAK,GAAG;AACxC;;AAGFknB,sBAAkB7gB,WAAOE,UAAGC,WAAM,OAAA,SAAND,QAAQ1G,WAAW,WAAA;AAC7CoI,uBAAiB5B,UAAU;AAC3B6gB,wBAAkB7gB,UAAU4C;AAC5BwM,0BAAmB;AACnBJ,yBAAmBqF,MAAMpJ,MAAqB;AAC9CsI,8BAAwB5Z,KAAK;OAC5B,GAAG;KAER,CACEiI,kBACAwN,qBACAJ,oBACAuE,uBAAuB,CACxB;AAEH,MAAMiO,gBAAY7uB,0BAChB,SAAS6uB,aAASA;AAChB,QAAIX,kBAAkB7gB,SAAS;AAC7BC,mBAAa4gB,kBAAkB7gB,OAAO;AACtC6gB,wBAAkB7gB,UAAU4C;eACnBhB,iBAAiB5B,SAAS;AAOnCuJ,4BAAsB,WAAA;AACpB3H,yBAAiB5B,UAAU;OAC5B;;KAGL,CAAC4B,gBAAgB,CAAC;AAGpBL,8BAAU,WAAA;AACR,QAAI,CAACqf,aAAa5gB,SAAS;AACzB;;AAEF,QAAMyhB,eAAeb,aAAa5gB;AAClCyhB,iBAAavR,iBAAiB,SAAS+Q,SAAS;MAC9C7Q,SAAS;KACV;AAEDqR,iBAAavR,iBAAiB,aAAaoR,aAAa;MACtDlR,SAAS;KACV;AACDqR,iBAAavR,iBAAiB,WAAWsR,WAAW;MAClDpR,SAAS;KACV;AAED,WAAO,WAAA;AACLqR,sBAAY,OAAA,SAAZA,aAAcpR,oBAAoB,SAAS4Q,OAAO;AAClDQ,sBAAY,OAAA,SAAZA,aAAcpR,oBAAoB,aAAaiR,WAAW;AAC1DG,sBAAY,OAAA,SAAZA,aAAcpR,oBAAoB,WAAWmR,SAAS;;KAEvD,CAACZ,cAAcK,SAASK,aAAaE,SAAS,CAAC;AACpD;AAEA,SAASL,eAAe9M,OAAiB;AACvC,MAAMpJ,SAASoJ,SAAK,OAAA,SAALA,MAAOpJ;AACtB,MAAI,CAAC0R,eAAe1R,MAAM,GAAG;AAC3B,WAAO,CAAA;;AAGT,SAAOwI,iBAAiBxI,MAAM;AAChC;AAEA,SAASoW,iBACP1nB,OACAyI,gBACA4e,kBACAjjB,aAAwB;AAExB,MAAMzB,QAAQxC,WAAWH,KAAK;AAE9B,MAAI+mB,cAAc/mB,KAAK,GAAG;AACxB,QAAMkB,WAAUP,aAAaX,KAAK;AAClC,WAAO;MACLyI;MACAzI,OAAOkB;MACP6mB,aAAW,SAAAA,cAAAA;AACT,eAAO/nB,MAAM6C;;MAEfmlB,UAAUhoB,MAAM6C;MAChBolB,UAAU;MACVtlB;MACAzB,SAAAA;MACAD,wBAAwBC;;;AAG5B,MAAMA,UAAUP,aAAaX,OAAOyI,cAAc;AAElD,SAAO;IACLA;IACAzI,OAAOklB,iBAAiBhkB,OAAO;IAC/B6mB,aAAW,SAAAA,YAAC5sB,YAAAA;UAAAA,eAAAA,QAAAA;AAAAA,qBAAyBksB,oBAAgB,OAAhBA,mBAAoBvrB,WAAW4C;;AAClE,aAAO0F,YAAYlD,SAAS/F,UAAU;;IAExC6sB,UAAU5jB,YAAYlD,SAASmmB,oBAAgB,OAAhBA,mBAAoBvrB,WAAW4C,KAAK;IACnEupB,UAAU;IACVtlB;IACAzB;IACAD,wBAAwBN,aAAaX,KAAK;;AAE9C;SC9LgBkoB,OAAOC,OAAY;AACjC,aACEnvB,4BAAAA,UAAAA,OAAAA,OAAAA;IACEovB,MAAK;KACDD,OAAK;IACT5sB,WAAW0iB,GAAGC,SAAOV,QAAQ2K,MAAM5sB,SAAS;MAE3C4sB,MAAMnjB,QAAQ;AAGrB;AAEA,IAAMkZ,WAAS9lB,WAAWS,OAAO;EAC/B2kB,QAAQ;IACN,KAAK;IACL6K,QAAQ;IACRC,QAAQ;IACRC,YAAY;IACZC,SAAS;;CAEZ;SCVeC,qBAAoBnpB,MAAA;;MAClCa,cAAUb,KAAVa,YACAe,UAAO5B,KAAP4B,SACA5I,UAAMgH,KAANhH,QACAkB,iBAAc8F,KAAd9F,gBAAckvB,sBAAAppB,KACdqpB,gBAAAA,iBAAcD,wBAAA,SAAG,OAAIA,qBACrBE,gBAAatpB,KAAbspB,eACA5jB,WAAQ1F,KAAR0F,UACAzJ,YAAS+D,KAAT/D,WAASstB,oBAAAvpB,KACTwpB,cAAAA,eAAYD,sBAAA,SAAG,QAAKA;AAEpB,aACE7vB,4BAACkvB,QAAM;IACL3sB,WAAW0iB,GACTC,SAAOle,OACP1H,WAAUM,aAAaN,QACvBkB,kBAAkBH,wBAAwBG,iBAAcglB,MAAA,CAAA,GAAAA,IAErD9mB,WAAWgrB,OAAO,IAAG,CAACpqB,WAAU,CAACkB,gBAAcglB,MAElD,CAAC,EAAEoK,iBAAiBD,mBAAmBzK,SAAO0K,eAC9CE,gBAAgB5K,SAAO4K,cACvBvtB,SAAS;oBAEG2F;kBACF6nB,aAAa5oB,WAAU;sBACnBA;KAEf6E,QAAQ;AAGf;AAEA,SAAS+jB,aAAa5oB,aAAoB;;AACxC,SAAOA,YAAW,CAAC,EAAEsc,MAAM,OAAO,KAACuM,eAC/B7oB,YAAW,CAAC,MAAC,OAAA6oB,eAAI7oB,YAAW,CAAC,IAC7BA,YAAW,CAAC;AAClB;AAEA,IAAM+d,WAAS9lB,WAAWS,OAAO;EAC/BmH,OAAO;IACL,KAAKtI,WAAWsI;IAChB8e,UAAU;IACVjlB,OAAO;IACPD,QAAQ;IACRylB,WAAW;IACX9mB,SAAS;IACT0wB,YAAY;IACZC,gBAAgB;IAChBC,UAAU;IACVC,WAAW;IACXlK,cAAc;IACdvmB,UAAU;IACVgB,YAAY;IACZ,UAAU;MACRylB,iBAAiB;;IAEnB,UAAU;MACRA,iBAAiB;;;EAGrB0J,cAAc;IACZP,YAAY;IACZ,UAAU;MACRnJ,iBAAiB;MACjBmJ,YAAY;;IAEd,UAAU;MACRnJ,iBAAiB;MACjBmJ,YAAY;;;EAGhBK,eAAe;IACb,KAAKlxB,WAAW4J;IAChB,UAAU;MACR+nB,SAAS;MACT9wB,SAAS;MACTsB,OAAO;MACPD,QAAQ;MACR0vB,OAAO;MACP9H,QAAQ;MACR1C,UAAU;MACVyK,YAAY;MACZC,aAAa;MACbC,WAAW;MACXC,cAAc;MACdC,QAAQ;;IAEV,gBAAgB;MACdD,cAAc;;;CAGnB;AChHM,IAAME,cAAcxxB,WAAWS,OAAO;EAC3CgxB,UAAU;IACR,KAAKnyB,WAAWmyB;IAChBC,UAAU;;EAEZC,QAAQ;IACNC,WAAW;IACXC,aAAa;IACb1xB,SAAS;;CAEZ;SCLe2xB,SAAQ5qB,MAAA;MACtB0B,aAAS1B,KAAT0B,WACAxF,QAAK8D,KAAL9D,OAAK2uB,gBAAA7qB,KACL8qB,UAAAA,WAAQD,kBAAA,SAAG,QAAKA,eAChBtnB,SAAMvD,KAANuD,QACAwnB,UAAO/qB,KAAP+qB,SACA9uB,YAAS+D,KAAT/D;AAUA,aACEvC,4BAAAA,OAAAA;IACEqkB,KAAKxa;IACLynB,KAAKtpB;IACLzF,WAAW0iB,GAAGC,SAAOqM,WAAWX,YAAYC,UAAUD,YAAYG,QAAQxuB,SAAS;IACnFivB,SAASJ,WAAW,SAAS;IAC7BC;IACA7uB;;AAGN;AAEA,IAAM0iB,WAAS9lB,WAAWS,OAAO;EAC/B0xB,WAAW;IACT,KAAK;IACLpB,UAAU;IACVC,WAAW;IACXqB,UAAU;IACVC,WAAW;IACXC,SAAS;;CAEZ;SCrCeC,YAAWtrB,MAAA;MACzB4B,UAAO5B,KAAP4B,SACA1F,QAAK8D,KAAL9D,OACAD,YAAS+D,KAAT/D;AAMA,aACEvC,4BAAAA,QAAAA;IACEuC,WAAW0iB,GACTC,SAAO2M,aACPjB,YAAYG,QACZH,YAAYC,UACZtuB,SAAS;oBAEG2F;IACd1F;KAEC0pB,iBAAiBhkB,OAAO,CAAC;AAGhC;AAEA,IAAMgd,WAAS9lB,WAAWS,OAAO;EAC/BgyB,aAAa;IACX,KAAK;IACLvL,YACE;IACFR,UAAU;IACVgM,YAAY;IACZhB,UAAU;IACViB,WAAW;IACXf,WAAW;IACXC,aAAa;IACbe,eAAe;IACfL,SAAS;;CAEZ;SChCeM,cAAa3rB,MAAA;MAC3BU,QAAKV,KAALU,OACAkB,UAAO5B,KAAP4B,SACA/F,aAAUmE,KAAVnE,YACA+vB,OAAI5rB,KAAJ4rB,MACAd,WAAQ9qB,KAAR8qB,UAAQe,mBAAA7rB,KACR8E,aAAAA,cAAW+mB,qBAAA,SAAGvpB,oBAAiBupB,kBAC/B5vB,YAAS+D,KAAT/D;AAEA,MAAA6vB,wBAAsCthB,+BAA8B,GAA3DuhB,4BAAyBD,sBAAA,CAAA;AAElC,MAAM5vB,QAAQ,CAAA;AACd,MAAI0vB,MAAM;AACR1vB,UAAM3B,QAAQ2B,MAAM5B,SAAS4B,MAAMsuB,WAAcoB,OAAI;;AAGvD,MAAMI,gBAAgBtrB,QAAQA,QAAQmC,eAAejB,OAAO;AAE5D,MAAI,CAACoqB,eAAe;AAClB,WAAO;;AAGT,MAAIvE,cAAcuE,aAAa,GAAG;AAChC,eACEtyB,4BAACkxB,UAAQ;MACP1uB;MACAwF,WAAWE;MACX/F,YAAYW,WAAWghB;MACvBsN;MACAvnB,QAAQyoB,cAAczoB;MACtBwnB;MACA9uB;;;AAKN,aACEvC,4BAAAA,uBAAAA,MACGmC,eAAeW,WAAWghB,aACzB9jB,4BAAC4xB,aAAW;IAAC1pB;IAAkB1F;IAAcD;WAE7CvC,4BAACkxB,UAAQ;IACP1uB;IACAwF,WAAWA,UAAUsqB,aAAa;IAClCnwB;IACAivB;IACAvnB,QAAQuB,YAAYlD,SAAS/F,UAAU;IACvCkvB;IACA9uB;IAEH;AAIL,WAAS8uB,UAAOA;AACdgB,8BAA0B,SAAAjxB,MAAI;AAAA,aAAI,IAAIoK,IAAIpK,IAAI,EAAE6J,IAAI/C,OAAO;;;AAE/D;SCpDgBqqB,eAAcjsB,MAAA;MAC5BU,QAAKV,KAALU,OACAkB,UAAO5B,KAAP4B,SACA5I,UAAMgH,KAANhH,QACAkB,iBAAc8F,KAAd9F,gBACA2B,aAAUmE,KAAVnE,YAAUutB,sBAAAppB,KACVqpB,gBAAAA,iBAAcD,wBAAA,SAAG,OAAIA,qBACrBwC,OAAI5rB,KAAJ4rB,MACAd,WAAQ9qB,KAAR8qB,UACAhmB,cAAW9E,KAAX8E,aACA7I,YAAS+D,KAAT/D,WAASstB,oBAAAvpB,KACTwpB,cAAAA,eAAYD,sBAAA,SAAG,QAAKA;AAEpB,MAAMD,gBAAgBtnB,mBAAmBtB,KAAK;AAE9C,aACEhH,4BAACyvB,sBAAoB;IACnBG;IACAD;IACArwB,QAAQA;IACRkB;IACA2G,YAAYA,WAAWH,KAAK;IAC5BkB;IACA4nB;SAEA9vB,4BAACiyB,eAAa;IACZ/pB;IACAlB;IACAkrB;IACA/vB;IACAivB;IACAhmB;IACA7I;IACA;AAGR;;SC/CgBiwB,UAAOA;AACrB,MAAA3e,wBAA6BrD,sBAAqB,GAAzCiiB,mBAAgB5e,sBAAA,CAAA;AACzB,aACE7T,4BAACkvB,QAAM;kBACM;IACXwD,OAAM;IACNC,UAAU;IACVpwB,WAAW0iB,GAAGC,SAAO0N,QAAQ;IAC7BtE,SAAS,SAAAA,UAAAA;AAAA,aAAMmE,iBAAiB,KAAK;;;AAG3C;AAEA,IAAMvN,WAAS9lB,WAAWS,OAAMC,SAAA;EAC9B8yB,UAAU;IACR9B,UAAU;IACVa,SAAS;IACTkB,OAAO;IACP3M,cAAc;IACd6L,WAAW;IACXD,YAAY;IACZjxB,OAAO;IACPD,QAAQ;IACRrB,SAAS;IACT2wB,gBAAgB;IAChBD,YAAY;IACZtvB,YAAY;IACZ,UAAU;MACR0vB,SAAS;MACToB,UAAU;MACVC,WAAW;MACXoB,iBAAe,SAASC,OAAI;MAC5B3M,iBAAiB;MACjB4M,kBAAkB;MAClBC,gBAAgB;MAChB1yB,qBAAqB;;IAEvB,UAAU;MACRsyB,OAAO;MACPzM,iBAAiB;MACjB,UAAU;QACR7lB,qBAAqB;;;IAGzB,UAAU;MACRsyB,OAAO;MACPzM,iBAAiB;MACjB,UAAU;QACR7lB,qBAAqB;;;;AAG1B,GACEO,SAAS,YAAY;EACtB,UAAU;IAAEP,qBAAqB;;EACjC,gBAAgB;IAAEA,qBAAqB;;CACxC,CAAC,CACH;SC7Ce2yB,YAASA;AACvB,MAAArf,wBAAwBrD,sBAAqB,GAAtC2iB,gBAAatf,sBAAA,CAAA;AACpB,MAAMmE,eAAeQ,gBAAe;AACpC,MAAM/M,YAAYqK,mBAAkB;AACpCkY,uBAAqBhW,cAAc1F,mBAAmB0B,SAAS;AAC/D,MAAM7R,aAAa6Q,oBAAmB;AACtC,MAAMtH,uBAAuBkH,wBAAuB;AACpD,MAAMxH,cAAc4K,qBAAoB;AAExC,MAAI,CAACmd,eAAe;AAClB,WAAO;;AAGT,aACEnzB,4BAAAA,MAAAA;IACEuC,WAAW0iB,GAAGC,SAAOkO,MAAM,CAACD,iBAAiBvzB,aAAaN,MAAM;IAChEqmB,KAAK3N;KAEJvM,UAAUxM,IAAI,SAAAo0B,UAAQ;AAAA,eACrBrzB,4BAAAA,MAAAA;MAAIe,KAAKsyB;WACPrzB,4BAACuyB,gBAAc;MACbvrB,OAAOmC,eAAekqB,QAAQ;MAC9BlxB;MACA+F,SAASmrB;MACT1D,gBAAgB;MAChBptB,WAAW0iB,GAAGC,SAAOoO,WAAW;MAChCxD,cAAY;MACZ1kB;MACA;GAEL,GACAM,2BACC1L,4BAAAA,MAAAA,UACEA,4BAACwyB,SAAO,IAAA,CAAG,IAEX,IAAI;AAGd;AAEA,IAAMtN,WAAS9lB,WAAWS,OAAO;EAC/BuzB,MAAM;IACJG,WAAW;IACXC,QAAQ;IACR7B,SAAS;IACTpyB,SAAS;IACT2wB,gBAAgB;IAChBD,YAAY;IACZrvB,QAAQ;;EAEV0yB,aAAa;IACX,UAAU;MACR7C,WAAW;;IAEb,UAAU;MACRA,WAAW;;IAEb,WAAW;MACTA,WAAW;;IAEb9vB,YAAY;;CAEf;SC5Ee8yB,YAAY9b,SAAmB;AAC7C,MAAM8E,sBAAsBN,uBAAsB;AAElDvN,8BAAU,WAAA;AACR,QAAM0O,UAAU3F,QAAQtK;AACxB,QAAI,CAACiQ,SAAS;AACZ;;AAGFA,YAAQC,iBAAiB,UAAUmW,UAAU;MAC3CjW,SAAS;KACV;AAED,aAASiW,WAAQA;AACfjX,0BAAmB;;AAGrB,WAAO,WAAA;AACLa,iBAAO,OAAA,SAAPA,QAASI,oBAAoB,UAAUgW,QAAQ;;KAEhD,CAAC/b,SAAS8E,mBAAmB,CAAC;AACnC;SCrBgBkX,mBAAgBA;AAC9B,MAAAvB,wBAAiCthB,+BAA8B,GAAxD8iB,yBAAsBxB,sBAAA,CAAA;AAC7B,MAAMyB,kBAAkBjU,mBAAkB;AAE1C,SAAO,SAAC5Y,OAAgB;AACtB,QAAMkB,UAAUP,aAAaX,KAAK;AAElC,QAAM8sB,eAAeF,uBAAuBjmB,IAAIzF,OAAO;AACvD,QAAM6rB,cAAcF,gBAAgB3rB,OAAO;AAE3C,WAAO;MACL4rB;MACAC;MACAz0B,QAAQw0B,gBAAgBC;;;AAG9B;SCAgBC,cAAa1tB,MAAA;MAC3B2tB,iBAAc3tB,KAAd2tB,gBACAjoB,WAAQ1F,KAAR0F,UACA1M,UAAMgH,KAANhH,QACAkB,iBAAc8F,KAAd9F;AAEA,MAAMwD,WAAWK,2BAA2B4vB,cAAc;AAC1D,MAAMC,eAAe5vB,+BAA+B2vB,cAAc;AAElE,aACEj0B,4BAAAA,MAAAA;IACEuC,WAAW0iB,GACTC,SAAOlhB,UACP1E,WAAUM,aAAaN,QACvBkB,kBAAkBH,wBAAwBG,cAAc;iBAE/CwD;kBACCkwB;SAEZl0B,4BAAAA,MAAAA;IAAIuC,WAAW0iB,GAAGC,SAAOmE,KAAK;KAAI6K,YAAY,OAC9Cl0B,4BAAAA,OAAAA;IAAKuC,WAAW0iB,GAAGC,SAAO/J,eAAe;KAAInP,QAAQ,CAAO;AAGlE;AAEA,IAAMkZ,WAAS9lB,WAAWS,OAAO;EAC/BmE,UAAU;IACR,KAAKtF,WAAWsF;IAChB,4BAA4B;MAC1BzE,SAAS;;;EAGb4b,iBAAiB;IACf,KAAKzc,WAAWyc;IAChB5b,SAAS;IACT40B,SAAS;IACTC,qBAAqB;IACrBlE,gBAAgB;IAChBsD,QAAQ;IACR1N,UAAU;;EAEZuD,OAAO;IACL,KAAK3qB,WAAW2qB;IAChB4G,YAAY;;IAEZzJ,gBAAgB;IAChBJ,iBAAiB;IACjByM,OAAO;IACPtzB,SAAS;IACTuxB,UAAU;IACVuD,YAAY;IACZzzB,QAAQ;IACR4yB,QAAQ;IACR7B,SAAS;IACT7L,UAAU;IACVwO,eAAe;IACfvb,KAAK;IACLlY,OAAO;IACP8vB,QAAQ;;CAEX;AChFD,IAAI4D,gBAAgB;AAEpB,SAAgBC,mBAAgBA;AAC9B,MAAAloB,sBAAkCtM,uBAAeu0B,aAAa,GAAvDE,YAASnoB,gBAAA,CAAA,GAAEooB,eAAYpoB,gBAAA,CAAA;AAE9BtM,8BAAgB,WAAA;AACd00B,iBAAa,IAAI;AACjBH,oBAAgB;KACf,CAAA,CAAE;AAEL,SAAOE,aAAaF;AACtB;SCQgBI,UAASruB,MAAA;MAAG2tB,iBAAc3tB,KAAd2tB;AAC1B,MAAA7F,sBAA2B9c,mBAAkB,GAAtCE,mBAAgB4c,oBAAA,CAAA;AACvB,MAAMqG,YAAYD,iBAAgB;AAClC,MAAMI,4BAA4B9f,6BAA4B;AAC9D,MAAM1J,cAAc4K,qBAAoB;AACxC,MAAM6e,gBAAY70B;IAChB,WAAA;AAAA,UAAA80B;AAAA,cAAAA,gBAAMtI,aAAaoI,yBAAyB,MAAC,OAAAE,gBAAI,CAAA;;;IAEjD,CAACtjB,kBAAkBojB,yBAAyB;EAAC;AAE/C,MAAMzyB,aAAa6Q,oBAAmB;AACtC,MAAMzE,oBAAoBF,qBAAoB;AAE9C,MAAI,CAAComB,WAAW;AACd,WAAO;;AAGT,aACEz0B,4BAACg0B,eAAa;IACZC;IACAzzB,gBAAc;IACdlB,QAAQu1B,UAAUlyB,WAAW;KAE5BkyB,UAAU51B,IAAI,SAAA81B,eAAa;AAC1B,QAAM/tB,QAAQmC,eAAe4rB,cAAcvH,QAAQ;AAEnD,QAAI,CAACxmB,OAAO;AACV,aAAO;;AAGT,QAAIuH,kBAAkBvH,KAAK,GAAG;AAC5B,aAAO;;AAGT,eACEhH,4BAACuyB,gBAAc;MACb5C,gBAAgB;MAChBznB,SAAS6sB,cAAc7sB;MACvB/F;MACA6E;MACAjG,KAAKg0B,cAAc7sB;MACnBkD;;GAGL,CAAC;AAGR;SCvCgB4pB,YAASA;AACvB,MAAMlqB,aAAasI,oBAAmB;AACtC,MAAM6hB,gCAA4Bj1B,qBAAa,CAAC;AAEhD,aACEA,4BAAAA,MAAAA;IAAIuC,WAAW0iB,GAAGC,SAAOgQ,SAAS;KAC/BpqB,WAAW7L,IAAI,SAAAg1B,gBAAc;AAC5B,QAAMjwB,WAAWK,2BAA2B4vB,cAAc;AAE1D,QAAIjwB,aAAaf,WAAWG,WAAW;AACrC,iBAAOpD,4BAAC20B,WAAS;QAAC5zB,KAAKiD;QAAUiwB;;;AAGnC,eACEj0B,4BAACA,uBAAc;MAACe,KAAKiD;WACnBhE,4BAACm1B,gBAAc;MACbnxB;MACAiwB;MACAgB;MACA;GAGP,CAAC;AAGR;AAEA,SAASE,eAAc7uB,MAAA;MACrBtC,WAAQsC,KAARtC,UACAiwB,iBAAc3tB,KAAd2tB,gBACAgB,4BAAyB3uB,KAAzB2uB;AAMA,MAAMG,gBAAgBzB,iBAAgB;AACtC,MAAMrxB,iBAAiB0S,wBAAuB;AAC9C,MAAM7S,aAAa6Q,oBAAmB;AACtC,MAAMlD,oBAAoBkB,qBAAoB;AAC9C,MAAAmd,wBAAyBvd,uBAAsB,GAAxCnB,iBAAc0e,sBAAA,CAAA;AACrB,MAAM5f,oBAAoBF,qBAAoB;AAC9C,MAAMjD,cAAc4K,qBAAoB;AACxC,MAAM2Z,iBAAiB,CAAC7c,2BAA0B;AAIlD,MAAMuiB,eACJ,CAACvlB,qBAAqBmlB,0BAA0B5nB,UAAU,IACtD,CAAA,IACA5E,iBAAiBzE,QAAQ;AAE/B,MAAIqxB,aAAa1yB,SAAS,GAAG;AAC3BsyB,8BAA0B5nB;;AAG5B,MAAIioB,gBAAgB;AAEpB,MAAM3sB,UAAS0sB,aAAap2B,IAAI,SAAA+H,OAAK;AACnC,QAAMkB,UAAUP,aAAaX,OAAOyI,cAAc;AAClD,QAAA8lB,iBAA8CH,cAAcpuB,KAAK,GAAzD8sB,eAAYyB,eAAZzB,cAAcC,cAAWwB,eAAXxB,aAAaz0B,UAAMi2B,eAANj2B;AAEnC,QAAMk2B,eAAejnB,kBAAkBvH,KAAK;AAE5C,QAAI1H,WAAUk2B,cAAc;AAC1BF;;AAGF,QAAIE,cAAc;AAChB,aAAO;;AAGT,eACEx1B,4BAACuyB,gBAAc;MACb5C;MACA5uB,KAAKmH;MACLlB;MACAkB;MACA5I,QAAQw0B;MACRtzB,gBAAgBuzB;MAChB5xB;MACAivB,UAAU9uB;MACV8I;;GAGL;AAED,aACEpL,4BAACg0B,eAAa;IACZC;;;IAGA30B,QAAQg2B,kBAAkB3sB,QAAOhG;KAEhCgG,OAAM;AAGb;AAEA,IAAMuc,WAAS9lB,WAAWS,OAAO;EAC/Bq1B,WAAW;IACT,KAAKx2B,WAAWw2B;IAChB3B,WAAW;IACXC,QAAQ;IACR7B,SAAS;;CAEZ;;ACtGD,IAAK8D;CAAL,SAAKA,YAAS;AACZA,EAAAA,WAAAA,WAAAA,IAAAA,IAAAA,CAAAA,IAAAA;AACAA,EAAAA,WAAAA,WAAAA,MAAAA,IAAAA,CAAAA,IAAAA;AACF,GAHKA,cAAAA,YAAS,CAAA,EAAA;AAMd,SAAgBC,uBAAoBA;AAClC,MAAMhe,mBAAmBU,oBAAmB;AAC5C,MAAML,qBAAqBa,sBAAqB;AAChD,MAAAwD,wBAAgBlL,6BAA4B,GAArClK,QAAKoV,sBAAA,CAAA;AACZ,MAAMja,aAAa6Q,oBAAmB;AAEtC,MAAA2iB,wBAAqCC,sBACnC7d,kBAAkB,GADZ8d,SAAMF,sBAANE,QAAQC,mBAAgBH,sBAAhBG;AAGhB,MAAMnV,sBAAsBtI,uBAAsB;AAClD,MAAM0d,kBAAkBC,gBAAgBje,kBAAkB;AAC1D,MAAM3M,cAAc4K,qBAAoB;AAExC,MAAMwO,SAASxB,iBAAiBtL,iBAAiBrK,OAAO;AAExD,MAAMqc,UAAUlb,QACdxH,SACEwd,UACAlc,mBAAmBtB,KAAK,KACxBwd,OAAOqH,UAAUC,SAASptB,WAAW4J,kBAAkB,CAAC;AAG5DsG,8BAAU,WAAA;AACR,QAAI,CAAC8a,SAAS;AACZ;;AAGF1P,2BAAuBjC,mBAAmB1K,OAAO;KAChD,CAAC0K,oBAAoB2R,SAAShS,gBAAgB,CAAC;AAElD,MAAIqB,KAAKkd;AAET,MAAI,CAACvM,WAAWhS,iBAAiBrK,SAAS;AACxCsT,wBAAoB,IAAI;SACnB;AACL5H,UAAM8c,OAAM;AACZI,mBAAeF,gBAAe;;AAGhC,aACE/1B,4BAAAA,OAAAA;IACE2lB,KAAK5N;IACLxV,WAAW0iB,GACTC,SAAOxL,iBACPoc,iBAAgB,MAAOL,UAAUS,QAAQhR,SAAOiR,YAChDzM,WAAWxE,SAAOwE,OAAO;IAE3BlnB,OAAO;MAAEuW;;KAER2Q,WAAW1iB,QACR,CAACW,aAAaX,KAAK,CAAC,EACjBmN,OAAOtL,gBAAgB7B,KAAK,CAAC,EAC7B4gB,MAAM,GAAG,CAAC,EACV3oB,IAAI,SAAAiJ,SAAO;AAAA,eACVlI,4BAACuyB,gBAAc;MACbxxB,KAAKmH;MACLlB;MACAkB;MACA/F;MACAwtB,gBAAgB;MAChBvkB;;GAEH,IACH,UACJpL,4BAAAA,OAAAA;IAAKuC,WAAW0iB,GAAGC,SAAOkR,OAAO;IAAG5zB,OAAOyzB;IAAgB;AAGjE;AAEA,SAASD,gBAAgBje,oBAAgD;AACvE,MAAML,mBAAmBU,oBAAmB;AAC5C,SAAO,SAAS2d,kBAAeA;AAC7B,QAAMvzB,QAA6B,CAAA;AACnC,QAAI,CAACuV,mBAAmB1K,SAAS;AAC/B,aAAO7K;;AAGT,QAAIkV,iBAAiBrK,SAAS;AAC5B,UAAMmX,SAASxB,iBAAiBtL,iBAAiBrK,OAAO;AAExD,UAAM+d,aAAaL,mBAAmBvG,MAAM;AAE5C,UAAI,CAACA,QAAQ;AACX,eAAOhiB;;AAITA,YAAMukB,OAAOqE,cAAa5G,UAAM,OAAA,SAANA,OAAQ6R,eAAc;;AAGlD,WAAO7zB;;AAEX;AAEA,SAASozB,sBACP7d,oBAAgD;AAEhD,MAAML,mBAAmBU,oBAAmB;AAC5C,MAAMT,UAAUY,WAAU;AAC1B,MAAI+d,YAAYb,UAAUc;AAE1B,SAAO;IACLT;IACAD;;AAGF,WAASC,mBAAgBA;AACvB,WAAOQ;;AAGT,WAAST,SAAMA;AACbS,gBAAYb,UAAUc;AACtB,QAAIC,iBAAiB;AAErB,QAAI,CAACze,mBAAmB1K,SAAS;AAC/B,aAAO;;AAGT,QAAMzM,SAASqmB,cAAclP,mBAAmB1K,OAAO;AAEvD,QAAIqK,iBAAiBrK,SAAS;AAAA,UAAAopB;AAC5B,UAAMnZ,UAAU3F,QAAQtK;AACxB,UAAMmX,SAASxB,iBAAiBtL,iBAAiBrK,OAAO;AAExD,UAAMqpB,eAAezP,cAAczC,MAAM;AAEzCgS,uBAAiBnM,mBAAmB7F,MAAM;AAE1C,UAAMtL,aAASud,qBAAGnZ,WAAO,OAAA,SAAPA,QAASpE,cAAS,OAAAud,qBAAI;AAExC,UAAIvd,YAAYsd,iBAAiB51B,QAAQ;AACvC01B,oBAAYb,UAAUS;AACtBM,0BAAkBE,eAAe91B;;;AAIrC,WAAO41B,iBAAiB51B;;AAE5B;AAEA,IAAMskB,WAAS9lB,WAAWS,OAAMC,SAAA;EAC9B4Z,iBAAiB;IACf,KAAKhb,WAAWgb;IAChBoM,UAAU;IACVwK,OAAO;IACPvJ,MAAM;IACN4K,SAAS;IACTgF,WAAW;IACXzQ,cAAc;IACd3mB,SAAS;IACT0wB,YAAY;IACZC,gBAAgB;IAChB1wB,SAAS;IACTE,YAAY;IACZD,eAAe;IACfsZ,KAAK;IACLuW,QAAQ;IACR1uB,QAAQ;IACR+vB,QAAQ;IACRpB,YAAY;IACZkB,WAAW;IACX9vB,YAAY;;EAEd+oB,SAAS;IACPlqB,SAAS;IACTE,YAAY;IACZD,eAAe;IACfgxB,WAAW;;EAEb0F,YAAY;IACV,KAAK;IACLS,iBAAiB;IACjBnG,WAAW;;EAEb,gBAAgB;IACd2F,SAAS;MACPrd,KAAK;MACL0X,WAAW;;;EAGf2F,SAAS;IACP,KAAK;IACL/F,SAAS;IACTvK,UAAU;IACVjlB,OAAO;IACPD,QAAQ;IACRoyB,kBAAkB;IAClB6D,oBAAoB;IACpB5D,gBAAgB;IAChBla,KAAK;IACL0X,WAAW;IACXqC,iBAAe,SAASgE,cAAW;;AACpC,GACEh2B,SAAS,WAAW;EACrB+1B,oBAAoB;CACrB,CAAC,CACH;SC1NeE,OAAIA;AAClB,MAAMpf,UAAUY,WAAU;AAC1Bkb,cAAY9b,OAAO;AACnBqW,uBAAqBrW,SAASrF,mBAAmB0kB,MAAM;AACvD3Z,iBAAc;AAEd,aACErd,4BAAAA,OAAAA;IACEuC,WAAW0iB,GAAGC,SAAO+R,MAAM52B,wBAAwBK,iBAAiB;IACpEilB,KAAKhO;SAEL3X,4BAAC01B,sBAAoB,IAAA,OACrB11B,4BAACg1B,WAAS,IAAA,CAAG;AAGnB;AAEA,IAAM9P,WAAS9lB,WAAWS,OAAO;EAC/Bo3B,MAAM;IACJ,KAAKv4B,WAAWib;IAChBud,MAAM;IACNC,WAAW;IACXC,WAAW;IACXtR,UAAU;;CAEb;SCxCeuR,8BACd7S,QACAlH,SAAwB;AAExB,MAAI,CAACkH,UAAU,CAAClH,SAAS;AACvB,WAAO;;AAGT,MAAMga,aAAa9S,OAAOkC,sBAAqB;AAC/C,MAAM6Q,WAAWja,QAAQoJ,sBAAqB;AAG9C,SAAO6Q,SAAS32B,UAAU02B,WAAWE,IAAID,SAASC;AACpD;SCEgBC,sBACdC,OACAC,iBAAmE;AAEnE,MAAMhgB,UAAUY,WAAU;AAC1B,MAAM6E,oBAAoBD,qBAAoB;AAC9C,MAAMD,iBAAiBD,kBAAiB;AAExCrO,8BAAU,WAAA;AACR,QAAI,CAAC8oB,OAAO;AACV;;AAEF,QAAMpa,UAAU3F,QAAQtK;AAExBiQ,eAAO,OAAA,SAAPA,QAASC,iBAAiB,WAAWqa,UAAU;MAC7Cna,SAAS;KACV;AAEDH,eAAO,OAAA,SAAPA,QAASC,iBAAiB,aAAasa,aAAa,IAAI;AAExDva,eAAO,OAAA,SAAPA,QAASC,iBAAiB,SAASua,SAAS,IAAI;AAEhDxa,eAAO,OAAA,SAAPA,QAASC,iBAAiB,YAAYwa,SAAS;MAC7Cta,SAAS;KACV;AACDH,eAAO,OAAA,SAAPA,QAASC,iBAAiB,QAAQwa,SAAS,IAAI;AAE/C,aAASD,QAAQE,GAAa;AAC5B,UAAMxT,SAASxB,iBAAiBgV,EAAE1f,MAAqB;AAEvD,UAAI,CAACkM,QAAQ;AACX,eAAOuT,QAAO;;AAGhB,UAAAE,wBAAqC1M,2BAA2B/G,MAAM,GAA9Dtc,UAAO+vB,sBAAP/vB,SAAS2hB,kBAAeoO,sBAAfpO;AAEjB,UAAI,CAAC3hB,WAAW,CAAC2hB,iBAAiB;AAChC,eAAOkO,QAAO;;AAGhBJ,sBAAgB;QACdzvB;QACA2hB;OACD;;AAEH,aAASkO,QAAQC,GAA2B;AAC1C,UAAIA,GAAG;AACL,YAAME,gBAAgBF,EAAEE;AAExB,YAAI,CAAClV,iBAAiBkV,aAAa,GAAG;AACpC,iBAAOP,gBAAgB,IAAI;;;AAI/BA,sBAAgB,IAAI;;AAEtB,aAASC,SAASI,GAAgB;AAChC,UAAIA,EAAEj3B,QAAQ,UAAU;AACtB42B,wBAAgB,IAAI;;;AAIxB,aAASE,YAAYG,GAAa;AAChC,UAAI5a,kBAAiB,GAAI;AACvB;;AAGF,UAAMoH,SAASxB,iBAAiBgV,EAAE1f,MAAqB;AAEvD,UAAIkM,QAAQ;AACV,YAAM2T,gBAAgBd,8BAA8B7S,QAAQlH,OAAO;AACnE,YAAMoZ,eAAelS,OAAOkC,sBAAqB,EAAG9lB;AACpD,YAAIu3B,gBAAgBzB,cAAc;AAChC,iBAAO0B,mCAAmC5T,QAAQmT,eAAe;;AAGnEjhB,qBAAa8N,MAAM;;;AAIvB,WAAO,WAAA;AACLlH,iBAAO,OAAA,SAAPA,QAASI,oBAAoB,aAAama,WAAW;AACrDva,iBAAO,OAAA,SAAPA,QAASI,oBAAoB,YAAYqa,OAAO;AAChDza,iBAAO,OAAA,SAAPA,QAASI,oBAAoB,SAASoa,SAAS,IAAI;AACnDxa,iBAAO,OAAA,SAAPA,QAASI,oBAAoB,QAAQqa,SAAS,IAAI;AAClDza,iBAAO,OAAA,SAAPA,QAASI,oBAAoB,WAAWka,QAAQ;;KAEjD,CAACjgB,SAAS+f,OAAOC,iBAAiBva,mBAAmBF,cAAc,CAAC;AACzE;AAEA,SAASkb,mCACP5T,QACAmT,iBAAmE;;AAEnE,MAAAU,yBAAqC9M,2BAA2B/G,MAAM,GAA9Dtc,UAAOmwB,uBAAPnwB,SAAS2hB,kBAAewO,uBAAfxO;AAEjB,MAAI,CAAC3hB,WAAW,CAAC2hB,iBAAiB;AAChC;;AAGD,GAAAyO,wBAAAhhB,SAASC,kBAA6B,OAAA,SAAtC+gB,sBAAwCC,QAAI,OAAA,SAA5CD,sBAAwCC,KAAI;AAE7CZ,kBAAgB;IACdzvB;IACA2hB;GACD;AACH;;ACtHA,IAAY2O;CAAZ,SAAYA,gBAAa;AACvBA,EAAAA,eAAAA,KAAAA,IAAAA;AACAA,EAAAA,eAAAA,QAAAA,IAAAA;AACF,GAHYA,kBAAAA,gBAAa,CAAA,EAAA;AAYzB,SAAwBC,KAAInyB,MAAA;MAC1B0F,WAAQ1F,KAAR0F,UACAzJ,YAAS+D,KAAT/D,WAASm2B,aAAApyB,KACT9D,OAAAA,QAAKk2B,eAAA,SAAG,CAAA,IAAEA,YAAAC,iBAAAryB,KACVgwB,WAAAA,YAASqC,mBAAA,SAAGH,cAAcI,MAAGD;AAE7B,aACE34B,4BAAAA,OAAAA;IACEwC,OAAK1C,SAAA,CAAA,GAAO0C,KAAK;IACjBD,WAAW0iB,GAAGC,SAAOgS,MAAM30B,WAAW2iB,SAAOoR,SAAS,CAAC;KAEtDtqB,QAAQ;AAGf;AAEA,IAAMkZ,WAAS9lB,WAAWS,QAAMg5B,qBAAA;EAC9B3B,MAAM;IACJ33B,SAAS;;AACV,GAAAs5B,mBACAL,cAAcI,GAAG,IAAG;EACnB7S,eAAe;GAChB8S,mBACAL,cAAcM,MAAM,IAAG;EACtB/S,eAAe;GAChB8S,mBAAA;SCjCqB5V,MAAK3c,MAAA;MAAG/D,YAAS+D,KAAT/D,WAASm2B,aAAApyB,KAAE9D,OAAAA,QAAKk2B,eAAA,SAAG,CAAA,IAAEA;AACnD,aAAO14B,4BAAAA,OAAAA;IAAKwC,OAAK1C,SAAA;MAAIo3B,MAAM;OAAM10B,KAAK;IAAID,WAAW0iB,GAAG1iB,SAAS;;AACnE;SCHwBw2B,SAAQzyB,MAAA;MAAG0F,WAAQ1F,KAAR0F,UAAUzJ,YAAS+D,KAAT/D,WAAWC,QAAK8D,KAAL9D;AACtD,aACExC,4BAAAA,OAAAA;IAAKwC,OAAK1C,SAAA,CAAA,GAAO0C,OAAK;MAAEsjB,UAAU;;IAAcvjB;KAC7CyJ,QAAQ;AAGf;SCNwBgtB,SAAQ1yB,MAAA;MAAG0F,WAAQ1F,KAAR0F,UAAUzJ,YAAS+D,KAAT/D,WAAWC,QAAK8D,KAAL9D;AACtD,aACExC,4BAAAA,OAAAA;IAAKwC,OAAK1C,SAAA,CAAA,GAAO0C,OAAK;MAAEsjB,UAAU;;IAAcvjB;KAC7CyJ,QAAQ;AAGf;ACGA,SAAgBitB,qBAAoB3yB,MAAA;MAClC+b,SAAM/b,KAAN+b,QACAiM,UAAOhoB,KAAPgoB,SACA4K,WAAQ5yB,KAAR4yB,UACAC,oBAAiB7yB,KAAjB6yB,mBACA32B,QAAK8D,KAAL9D;AAEA,aACExC,4BAACkvB,QAAM;IACL1sB;IACA8rB;IACA/rB,WAAW0iB,GAAE,cACCkU,mBACZjU,SAAOkU,MACP,CAAC/W,UAAU6C,SAAOmU,YAClBH,YAAYhU,SAAOoU,MAAM;oBAEbJ;iCACWhzB,eAAeizB,iBAA8B;;AAG5E;AAEA,IAAMjU,WAAS9lB,WAAWS,OAAO;EAC/Bw5B,YAAY;IACV75B,SAAS;IACTmxB,QAAQ;;EAEV2I,QAAQ;IACN,KAAK;IACL3I,QAAQ;IACRnxB,SAAS;;EAEX45B,MAAM;IACJ,KAAK;IACLv4B,OAAO;IACPtB,SAAS;IACT8vB,QAAQ;IACRnJ,cAAc;IACdtlB,QAAQ;IACRklB,UAAU;IACVwK,OAAO;IACP3vB,YAAY;IACZgwB,QAAQ;IACRrB,QAAQ;IACRqH,WAAW;IACX,UAAU;MACRA,WAAW;;IAEb,UAAU;MACRA,WAAW;;IAEb,sBAAsB;MACpBvQ,iBAAiB;;IAEnB,oBAAoB;MAClBA,iBAAiB;;IAEnB,oBAAoB;MAClBA,iBAAiB;;IAEnB,oBAAoB;MAClBA,iBAAiB;;IAEnB,oBAAoB;MAClBA,iBAAiB;;IAEnB,oBAAoB;MAClBA,iBAAiB;;;CAGtB;ACjED,IAAMmT,YAAY;AAMlB,SAAgBC,qBAAkBA;AAChC,aACEx5B,4BAACg5B,UAAQ;IAACx2B,OAAO;MAAE5B,QAAQ24B;;SACzBv5B,4BAAC+4B,UAAQ;IAACv2B,OAAO;MAAEgmB,QAAQ;MAAG8H,OAAO;;SACnCtwB,4BAACy5B,gBAAc;IAACnD,WAAWoD,wBAAwBC;IAAY,CACtD;AAGjB;AAEA,SAAgBF,eAAcnzB,MAAA;4BAC5BgwB,WAAAA,YAASqC,mBAAA,SAAGe,wBAAwBE,aAAUjB;AAE9C,MAAM9gB,oBAAoBa,qBAAoB;AAC9C,MAAMmhB,aAAa/mB,2BAA0B;AAC7C,MAAAwJ,wBAA4BlL,wBAAuB,GAA5CiR,SAAM/F,sBAAA,CAAA,GAAEgG,YAAShG,sBAAA,CAAA;AACxB,MAAA6R,wBAA4Cvd,uBAAsB,GAA3DnB,iBAAc0e,sBAAA,CAAA,GAAE2L,oBAAiB3L,sBAAA,CAAA;AACxC,MAAM9b,mBAAmBmC,0BAAyB;AAClD,MAAMiI,sBAAsBN,uBAAsB;AAClD,MAAMiC,mBAAmBT,oBAAmB;AAE5C,MAAIkc,YAAY;AACd,WAAO;;AAGT,MAAME,YAAeR,YAAY5zB,mBAAmBhD,SAAM;AAE1D,MAAMq3B,eAAe3X,SAAS0X,YAAYR,YAAY;AAEtD,MAAMU,WAAW3D,cAAcoD,wBAAwBC;AAEvD,aACE35B,4BAACg5B,UAAQ;IACPz2B,WAAW0iB,GACTC,SAAOgV,WACPD,YAAY/U,SAAO+U,UACnB5X,UAAU6C,SAAOvjB,MACjBs4B,YAAY5X,UAAU6C,SAAOiV,cAAc;IAE7C33B,OACEy3B,WACI;MAAEG,WAAWJ;MAAcp5B,QAAQo5B;QACnC;MAAEI,WAAWJ;;SAGnBh6B,4BAAAA,OAAAA;IAAKuC,WAAW0iB,GAAGC,SAAOmV,MAAM;IAAG1U,KAAK9N;KACrClS,mBAAmB1G,IAAI,SAACk6B,mBAAmB5L,GAAC;AAC3C,QAAM+L,SAASH,sBAAsB1pB;AAErC,eACEzP,4BAACi5B,sBAAoB;MACnBl4B,KAAKo4B;MACLA;MACA9W;MACA7f,OAAO;QACLiuB,WAAWxL,GACTgV,WAAQ,iBACW1M,KAAKlL,SAASkX,YAAY,KAAE,QAAA,iBAC5BhM,KAAKlL,SAASkX,YAAY,KAAE,OAC/ClX,UAAUiX,UAAU,YAAY;;MAGpCJ,UAAUI;MACVhL,SAAS,SAAAA,UAAAA;AACP,YAAIjM,QAAQ;AACVyX,4BAAkBX,iBAAiB;AACnC9mB,2BAAiB8mB,iBAAiB;AAClC/a,2BAAgB;eACX;AACLkE,oBAAU,IAAI;;AAEhB7F,4BAAmB;;;GAI1B,CAAC,CACE;AAGZ;AAEA,IAAYid;CAAZ,SAAYA,0BAAuB;AACjCA,EAAAA,yBAAAA,UAAAA,IAAAA;AACAA,EAAAA,yBAAAA,YAAAA,IAAAA;AACF,GAHYA,4BAAAA,0BAAuB,CAAA,EAAA;AAKnC,IAAMxU,WAAS9lB,WAAWS,OAAO;EAC/Bq6B,WAAW;IACT,KAAK;IACL,MAAM;MACJ,wBAAwB;;IAE1B36B,SAAS;IACT0wB,YAAY;IACZC,gBAAgB;IAChBvvB,YAAY;IACZgxB,SAAS;;EAEXsI,UAAU;IACRtI,SAAS;IACT1B,YAAY;IACZlK,eAAe;IACfG,cAAc;IACdoJ,QAAQ;;EAEV6K,gBAAgB;IACdxD,WAAW;;EAEbh1B,MAAM;;IAEJ6kB,gBAAgB;IAChB+I,YAAY;IACZ,eAAe;MACbD,QAAQ;;;EAGZ+K,QAAQ;IACN,KAAK;IACLvU,UAAU;IACVjlB,OAAO;IACPD,QAAQ;;CAEX;SC1He05B,UAAOA;AACrB,MAAM3vB,gBAAgB+J,iBAAgB;AACtC,MAAM6N,sBAAsBtB,uBAAsB;AAClD,MAAApN,wBAAwBrD,sBAAqB,GAAtC2iB,gBAAatf,sBAAA,CAAA;AAEpB,MAAI,CAAClJ,cAAckB,aAAa;AAC9B,WAAO;;AAGT,aACE7L,4BAACy4B,MAAI;IACHl2B,WAAW0iB,GACTC,SAAOqV,SACPl6B,wBAAwBK,mBACxByyB,iBAAiBjO,SAAOsV,eAAe;SAGzCx6B,4BAACy6B,aAAW,IAAA,OACZz6B,4BAACijB,OAAK,IAAA,GACLV,0BAAsBviB,4BAACw5B,oBAAkB,IAAA,IAAM,IAAI;AAG1D;AAEA,SAAgBiB,cAAWA;;AACzB,MAAM9vB,gBAAgB+J,iBAAgB;AACtC,MAAA9H,gBAAwCC,uBAAuB,IAAI,GAA5D6tB,eAAY9tB,UAAA,CAAA,GAAE+qB,kBAAe/qB,UAAA,CAAA;AACpC,MAAMzK,aAAa6Q,oBAAmB;AACtC,MAAAoJ,wBAA+BlL,6BAA4B,GAApDypB,uBAAoBve,sBAAA,CAAA;AAC3B,MAAMhR,cAAc4K,qBAAoB;AAExCyhB,wBAAsB9sB,cAAckB,aAAa8rB,eAAe;AAEhE,MAAM3wB,QAAQmC,gBAAcyxB,wBAC1BF,gBAAY,OAAA,SAAZA,aAAcxyB,YAAO,OAAA0yB,wBAAIF,gBAAY,OAAA,SAAZA,aAAc7Q,eAAe;AAGxD,MAAMgR,OAAO7zB,SAAS,QAAQ0zB,gBAAgB;AAE9C,aAAO16B,4BAAC86B,gBAAc,IAAA;AAEtB,WAASA,iBAAcA;AACrB,QAAMnvB,eACJgvB,wBAAoB,OAApBA,uBAAwBxxB,eAAewB,cAAcgB,YAAY;AACnE,QAAI,CAACA,cAAc;AACjB,aAAO;;AAET,QAAMovB,cAAcJ,uBAChB3yB,UAAU2yB,oBAAoB,IAC9BhwB,cAAciB;AAElB,eACE5L,4BAAAA,uBAAAA,UACEA,4BAAAA,OAAAA,MACG66B,WACC76B,4BAACiyB,eAAa;MACZ/pB,SAASwyB,gBAAY,OAAA,SAAZA,aAAcxyB;MACvBlB;MACA7E;MACA+vB,MAAM;MACN9mB;MACA7I,WAAW0iB,GAAGC,SAAOle,KAAK;SAE1B2E,mBACF3L,4BAACiyB,eAAa;MACZ/pB,SAASP,aAAagE,YAAY;MAClC3E,OAAO2E;MACPxJ;MACA+vB,MAAM;MACN9mB;MACA7I,WAAW0iB,GAAGC,SAAOle,KAAK;SAE1B,IAAI,OAEVhH,4BAAAA,OAAAA;MAAKuC,WAAW0iB,GAAGC,SAAOmE,KAAK;OAC5BwR,OAAO7yB,UAAUhB,KAAK,IAAI+zB,WAAW,CAClC;;AAId;AAOA,IAAM7V,WAAS9lB,WAAWS,OAAO;EAC/B06B,SAAS;IACPtK,YAAY;IACZ+K,WAAW;IACXp6B,QAAQ;IACR+wB,SAAS;IACT7L,UAAU;IACV6K,QAAQ;;EAEVtH,OAAO;IACLwJ,OAAO;IACP/B,UAAU;IACVa,SAAS;IACT2C,eAAe;;EAEjBttB,OAAO;IACL2qB,SAAS;;EAEX6I,iBAAiB;IACfh7B,SAAS;IACTmB,YAAY;;CAEf;SC1Ies6B,oBAAoBC,WAAyB;;AAC3D,UAAAC,wBAAOD,aAAS,OAAA,SAATA,UAAWE,aAAa,WAAW,MAAC,OAAAD,wBAAI;AACjD;SCIgBE,iCACdC,mBAA6C;AAE7C,MAAM3jB,UAAUY,WAAU;AAE1B3J,8BAAU,WAAA;AACR,QAAM2sB,oBAAoB,oBAAIC,IAAG;AACjC,QAAMle,UAAU3F,QAAQtK;AACxB,QAAMouB,WAAW,IAAIC,qBACnB,SAAAv1B,SAAO;AACL,UAAI,CAACmX,SAAS;AACZ;;AAGF,eAAAqe,YAAAC,gCAAoBz1B,OAAO,GAAA01B,OAAA,EAAAA,QAAAF,UAAA,GAAAG,QAAE;AAAA,YAAlBC,QAAKF,MAAA76B;AACd,YAAM4I,MAAKqxB,oBAAoBc,MAAMzjB,MAAM;AAC3CijB,0BAAkBS,IAAIpyB,KAAImyB,MAAME,iBAAiB;;AAGnD,UAAMC,SAASr9B,MAAMsqB,KAAKoS,iBAAiB;AAC3C,UAAMY,eAAeD,OAAOA,OAAOv5B,SAAS,CAAC;AAE7C,UAAIw5B,aAAa,CAAC,KAAK,GAAG;AACxB,eAAOb,kBAAkBa,aAAa,CAAC,CAAC;;AAG1C,eAAA7S,KAAA,GAAA8S,UAA0BF,QAAM5S,KAAA8S,QAAAz5B,QAAA2mB,MAAE;AAA7B,YAAA+S,aAAAD,QAAA9S,EAAA,GAAO1f,KAAEyyB,WAAA,CAAA,GAAEC,QAAKD,WAAA,CAAA;AACnB,YAAIC,OAAO;AACThB,4BAAkB1xB,EAAE;AACpB;;;OAIN;MACE2yB,WAAW,CAAC,GAAG,CAAC;KACjB;AAEHjf,eAAO,OAAA,SAAPA,QAAS8L,iBAAiBzqB,YAAYD,WAAWsF,QAAQ,CAAC,EAAEwD,QAAQ,SAAAg1B,IAAE;AACpEf,eAASgB,QAAQD,EAAE;KACpB;KACA,CAAC7kB,SAAS2jB,iBAAiB,CAAC;AACjC;SCxCgBoB,4BAAyBA;AACvC,MAAM/kB,UAAUY,WAAU;AAC1B,MAAMd,gBAAgBU,iBAAgB;AAEtC,SAAO,SAASwkB,uBAAuB34B,UAAgB;;AACrD,QAAI,CAAC2T,QAAQtK,SAAS;AACpB;;AAEF,QAAM6tB,aAAS0B,mBAAGjlB,QAAQtK,YAAO,OAAA,SAAfuvB,iBAAiBnS,cAAa,iBAC/BzmB,WAAQ,IAAI;AAG7B,QAAI,CAACk3B,WAAW;AACd;;AAGF,QAAMhQ,YAAYgQ,UAAUhQ,aAAa;AAEzCrS,aAASpB,cAAcpK,SAAS6d,SAAS;;AAE7C;SCzBgB2R,4BAAyBA;AACvC,MAAMC,uBAAuBxpB,sBAAqB;AAElD,MAAI,CAACwpB,sBAAsB;AACzB,WAAO;;AAGT,SAAOA,qBAAqBn6B,WAAW;AACzC;;SCegBo6B,eAAcz2B,MAAA;;MAC5B02B,mBAAgB12B,KAAhB02B,kBACAh5B,WAAQsC,KAARtC,UACAi5B,kBAAe32B,KAAf22B,iBACAhJ,iBAAc3tB,KAAd2tB,gBACA3F,UAAOhoB,KAAPgoB;AAEA,aACEtuB,4BAACkvB,QAAM;IACLyD,UAAUsK,kBAAkB,IAAI;IAChC16B,WAAW0iB,GACTC,SAAOgY,QACP78B,wBAAwBC,aAAW,aACxB0D,WAAQwhB,MAAA,CAAA,GAAAA,IAEhB9mB,WAAW46B,MAAM,IAAG0D,kBAAgBxX,IAAA;IAGzC8I;kBACYhqB,+BAA+B2vB,cAAc;qBAC1C+I;IACfG,MAAK;qBACS;;AAGpB;AAEA,IAAMC,sBAAsB;EAC1B78B,qBAAqB;;AAEvB,IAAM88B,gBAAgB;EACpB98B,qBAAqB;;AAGvB,IAAM+8B,uBAAuB;EAC3B,4BAA4B;IAC1BJ,QAAQ;MACN,UAAUE;MACV,gBAAgBA;;;;AAKtB,IAAMlY,WAAS9lB,WAAWS,OAAMC,SAAA;EAC9Bo9B,QAAQ;IACN,KAAK;IACL39B,SAAS;IACToB,YAAY;IACZmlB,UAAU;IACVllB,QAAQ;IACRC,OAAO;IACPoyB,gBAAgB;IAChBzD,SAAS;IACTqH,oBAAoB;IACpB/D,iBAAe,SAASyK,gBAAa;IACrC,iBAAiB;MACflN,SAAS;MACTvK,UAAU;MACV/M,KAAK;MACLgO,MAAM;MACNuJ,OAAO;MACP9H,QAAQ;MACR8G,QAAQ;MACRpJ,cAAc;;IAEhB,uBAAuB;MACrBsX,qBACE;;IAEJ,oBAAoB;MAClBA,qBACE;;IAEJ,wBAAwB;MACtBA,qBACE;;IAEJ,4BAA4B;MAC1BA,qBACE;;IAEJ,mBAAmB;MACjBA,qBACE;;IAEJ,wBAAwB;MACtBA,qBACE;;IAEJ,qBAAqB;MACnBA,qBACE;;IAEJ,4BAA4B;MAC1BA,qBAAqB;;IAEvB,qBAAqB;MACnBA,qBACE;;IAEJ,2BAA2B;MACzBA,qBACE;;;AAEL,GACE18B,SAAS,UAAUu8B,aAAa,GAAC;EACpC,mBAAiBv9B,SAAA,CAAA,GACZw9B,oBAAoB;EAEzB,mBAAiBx9B,SAAA,CAAA,GACZw9B,oBAAoB;AACxB,CAAA,CACF;SCzHeG,qBAAkBA;AAChC,MAAA7wB,gBAA4CC,uBAAwB,IAAI,GAAjE6wB,iBAAc9wB,UAAA,CAAA,GAAE0uB,oBAAiB1uB,UAAA,CAAA;AACxC,MAAM+vB,yBAAyBD,0BAAyB;AACxDrB,mCAAiCC,iBAAiB;AAClD,MAAMnY,eAAe3M,gBAAe;AAEpC,MAAMmnB,mBAAmBvqB,oBAAmB;AAC5C,MAAM0E,wBAAwBa,yBAAwB;AACtD,MAAMilB,qBAAqBf,0BAAyB;AAEpD,aACE78B,4BAAAA,OAAAA;IACEuC,WAAW0iB,GAAGC,SAAO2Y,GAAG;IACxBV,MAAK;kBACM;IACXvzB,IAAG;IACH+b,KAAK7N;KAEJ6lB,iBAAiB1+B,IAAI,SAAAg1B,gBAAc;AAClC,QAAMjwB,WAAWK,2BAA2B4vB,cAAc;AAC1D,QAAM+I,mBAAmBh5B,aAAa05B;AAEtC,QAAI5P,iBAAiBmG,cAAc,KAAK2J,oBAAoB;AAC1D,aAAO;;AAGT,QAAMX,kBAAkB,CAAC9Z,gBAAgB,CAAC6Z;AAE1C,eACEh9B,4BAAC+8B,gBAAc;MACbh8B,KAAKiD;MACLA;MACAg5B;MACAC;MACAhJ;MACA3F,SAAS,SAAAA,UAAAA;AACPqO,+BAAuB34B,QAAQ;AAC/B6C,mBAAW,WAAA;AACTy0B,4BAAkBt3B,QAAQ;WACzB,EAAE;;;GAIZ,CAAC;AAGR;AAEA,IAAMkhB,WAAS9lB,WAAWS,OAAO;EAC/Bg+B,KAAK;IACH,KAAK;IACLt+B,SAAS;IACTwmB,eAAe;IACfmK,gBAAgB;IAChByB,SAAS;;EAEX,sBAAsB;IACpBkM,KAAK;MACHr+B,SAAS;MACT6vB,QAAQ;MACR5vB,eAAe;;;EAGnB,gDAAgD;IAC9Co+B,KAAK;MACHr+B,SAAS;MACT6vB,QAAQ;MACR5vB,eAAe;;;CAGpB;;SCzEeq+B,iBAAcA;AAC5B,MAAMzf,cAAcJ,eAAc;AAElC,aACEje,4BAACkvB,QAAM;IACL3sB,WAAW0iB,GACTC,SAAO6Y,gBACP19B,wBAAwBI,mBAAmB;IAE7C6tB,SAASjQ;kBACE;IACXqU,OAAM;SAEN1yB,4BAAAA,OAAAA;IAAKuC,WAAW0iB,GAAGC,SAAO8Y,eAAe;IAAK;AAGpD;AAEA,IAAMC,YAAY;EAChB,UAAU;IACR,2BAA2B;MACzB19B,qBAAqB;;;;AAK3B,IAAM2kB,WAAS9lB,WAAWS,OAAMC,SAAA;EAC9Bi+B,gBAAgB;IACd,KAAK;IACLjY,UAAU;IACVwK,OAAO;IACP1vB,QAAQ;IACRC,OAAO;IACPtB,SAAS;IACT0wB,YAAY;IACZC,gBAAgB;IAChBnX,KAAK;IACL0X,WAAW;IACXkB,SAAS;IACTzL,cAAc;IACd,UAAU;MACRqJ,YAAY;;IAEd,UAAU;MACRA,YAAY;;;EAGhByO,iBAAiB;IACf,KAAK;IACL5X,iBAAiB;IACjB4M,kBAAkB;IAClBC,gBAAgB;IAChBryB,QAAQ;IACRC,OAAO;IACPiyB,iBAAe,SAASoL,WAAQ;IAChC,UAAU;MACR39B,qBAAqB;;IAEvB,UAAU;MACRA,qBAAqB;;;AAExB,GACEO,SAAS,mBAAmB;EAC7BP,qBAAqB;CACtB,GACEO,SAAS,kBAAkBm9B,SAAS,CAAC,CACzC;AC1ED,IAAME,QAAWx/B,YAAYD,WAAWmnB,WAAW,IAAC,MAAIlnB,YACtDD,WAAWw2B,SAAS;AAGtB,IAAMkJ,eAAe,CAAC,UAAUz/B,YAAYD,WAAWsI,KAAK,CAAC,EAAE7H,KAAK,EAAE;AACtE,IAAMk/B,WAAW1/B,YAAYD,WAAWsF,QAAQ;AAEhD,SAAgBs6B,UAASh4B,MAAA;MAAGtF,QAAKsF,KAALtF;AAC1B,MAAI,CAACA,OAAO;AACV,WAAO;;AAGT,MAAMu9B,IAAIC,SAASx9B,KAAK;AAExB,aACEhB,4BAAAA,SAAAA,MAAAA,WACEm+B,QAAK,MAAIC,eAAY,8CAKrBD,QAAK,MAAII,IAAC,4CAIVJ,QAAK,MAAIE,WAAQ,eAAaE,IAAC,uCAGlC;AAEH;AAEA,SAASC,SAASx9B,OAAa;AAC7B,SAAO,CACLo9B,cACA,sBACA3f,wBAAwBzd,KAAK,GAC7B,IAAI,EACJ7B,KAAK,EAAE;AACX;;SCrCgBs/B,YAASA;AACvB,aAAOz+B,4BAAAA,OAAAA;IAAKuC,WAAW0iB,GAAGC,SAAOwZ,SAAS;;AAC5C;AAEA,IAAMxZ,WAAS9lB,WAAWS,OAAMC,SAAA;EAC9B4+B,WAAW;IACT,KAAK;IACLrO,SAAS;IACTvK,UAAU;IACV/M,KAAK;IACLgO,MAAM;IACN0J,WAAW;IACX5vB,OAAO;IACPD,QAAQ;IACRoyB,kBAAkB;IAClB6D,oBAAoB;IACpB5D,gBAAgB;IAChBH,iBAAe,SAAS6L,eAAY;;AACrC,GACE79B,SAAS,aAAa;EACvBP,qBAAqB;CACtB,CAAC,CACH;SCNeq+B,kBAAeA;AAC7B,MAAMn8B,iBAAiBgT,wBAAuB;AAE9C,MAAMwM,qBAAqBlB,sBAAqB;AAEhD,MAAIte,gBAAgB;AAClB,WAAO;;AAGT,aACEzC,4BAACy4B,MAAI;IAACl2B,WAAW0iB,GAAGC,SAAO2Z,OAAO;SAChC7+B,4BAAC8+B,QAAM,IAAA,GAEN7c,yBAAqBjiB,4BAACy5B,gBAAc,IAAA,IAAM,IAAI;AAGrD;AAEA,SAAgBqF,SAAMA;AACpB,MAAAlyB,gBAAsBC,uBAAS,CAAC,GAAzBkyB,MAAGnyB,UAAA,CAAA,GAAEoyB,SAAMpyB,UAAA,CAAA;AAClB,MAAM6P,sBAAsBN,uBAAsB;AAClD,MAAMvE,iBAAiBa,kBAAiB;AACxC,MAAMwmB,cAAc1sB,2BAA0B;AAC9C,MAAM2sB,YAAYhsB,yBAAwB;AAC1C,MAAAisB,aAAsDzgB,UAAS,GAAvDE,sBAAmBugB,WAAnBvgB,qBAAqBrP,aAAU4vB,WAAV5vB,YAAYuP,YAAQqgB,WAARrgB;AAEzC,MAAMsgB,QAAQxnB,kBAAc,OAAA,SAAdA,eAAgBvK;AAC9B,MAAMrM,QAAQo+B,SAAK,OAAA,SAALA,MAAOp+B;AAErB,aACEhB,4BAACg5B,UAAQ;IAACz2B,WAAW0iB,GAAGC,SAAOma,eAAe;SAC5Cr/B,4BAACs+B,WAAS;IAACt9B;UACXhB,4BAAAA,SAAAA;;IAEEk/B;kBACY;IACZ3a,SAAS9H;IACTla,WAAW0iB,GAAGC,SAAOoa,MAAM;IAC3BlQ,MAAK;qBACS;IACd6P;IACAngB,UAAU,SAAAA,SAAA4C,OAAK;AACbsd,aAAOD,MAAM,CAAC;AACdl4B,iBAAW,WAAA;;AACTiY,mBAAQygB,sBAAC7d,SAAK,OAAA,UAAA8d,gBAAL9d,MAAOpJ,WAAM,OAAA,SAAbknB,cAAex+B,UAAK,OAAAu+B,sBAAIv+B,KAAK;OACvC;;IAEH2kB,KAAK/N;MAENrI,iBACCvP,4BAAAA,OAAAA;IACEm9B,MAAK;IACL56B,WAAW0iB,GAAG,6BAA6BC,SAAOua,cAAc;iBACtD;IACV71B,IAAG;mBACS;KAEXgV,mBAAmB,IAEpB,UACJ5e,4BAACy+B,WAAS,IAAA,OACVz+B,4BAAC89B,gBAAc,IAAA,CAAG;AAGxB;AAEA,IAAM5Y,WAAS9lB,WAAWS,OAAMC,SAAA;EAC9B++B,SAAS;IACPlN,SAAS;IACThB,QAAQ;;EAEV0O,iBAAiB;IACf,KAAK;IACLnI,MAAM;IACN33B,SAAS;IACTkyB,UAAU;;EAEZgO,gBAAgB;IACdC,MAAM;IACNC,UAAU;IACV/+B,QAAQ;IACRjB,UAAU;IACVmmB,UAAU;IACV8Z,YAAY;IACZ/+B,OAAO;;EAETy+B,QAAQ;IACN9P,SAAS;IACT7uB,YAAY;IACZkyB,OAAO;IACP3M,cAAc;IACdyL,SAAS;IACT/wB,QAAQ;IACRwlB,iBAAiB;IACjBkJ,QAAQ;IACRzuB,OAAO;IACP,UAAU;MACRulB,iBAAiB;MACjBkJ,QAAQ;;IAEV,iBAAiB;MACfuD,OAAO;;;EAIXkL,gBAAgB;IACd,KAAK;IACLjY,UAAU;IACVwK,OAAO;IACP1vB,QAAQ;IACRC,OAAO;IACPtB,SAAS;IACT0wB,YAAY;IACZC,gBAAgB;IAChBnX,KAAK;IACL0X,WAAW;IACXkB,SAAS;IACTzL,cAAc;IACd,UAAU;MACRqJ,YAAY;;IAEd,UAAU;MACRA,YAAY;;;EAGhByO,iBAAiB;IACf,KAAK;IACL5X,iBAAiB;IACjB4M,kBAAkB;IAClBC,gBAAgB;IAChBryB,QAAQ;IACRC,OAAO;IACPiyB,iBAAe,SAASoL,WAAQ;IAChC,UAAU;MACR39B,qBAAqB;;IAEvB,UAAU;MACRA,qBAAqB;;;AAExB,GACEO,SAAS,mBAAmB;EAC7BP,qBAAqB;CACtB,GACEO,SAAS,kBAAkB;EAC5B,kCAAkC;IAChCP,qBAAqB;;CAExB,CAAC,CACH;SClKes/B,SAAMA;AACpB,aACE7/B,4BAACg5B,UAAQ;IACPz2B,WAAW0iB,GAAG,cAAc5kB,wBAAwBK,iBAAiB;SAErEV,4BAAC4+B,iBAAe,IAAA,OAChB5+B,4BAACy9B,oBAAkB,IAAA,CAAG;AAG5B;ACFA,SAASqC,YAAY3Q,OAAkB;AACrC,aACEnvB,4BAACwX,2BAAyB,UACxBxX,4BAACD,gBAAc,IAAA,OACfC,4BAAC+L,sBAAoB,OAAA,OAAA,CAAA,GAAKojB,KAAK,OAC7BnvB,4BAAC+/B,gBAAc,IAAA,CAAG,CACG;AAG7B;AAEA,SAASA,iBAAcA;AACrB,MAAAlsB,wBAA+BrD,sBAAqB,GAA7C3O,uBAAoBgS,sBAAA,CAAA;AAC3B,MAAMnI,uBAAuBkH,wBAAuB;AAEpD,MAAAtG,sBAAkCtM,uBAAe,CAAC6B,oBAAoB,GAA/Dm+B,YAAS1zB,gBAAA,CAAA,GAAE2zB,eAAY3zB,gBAAA,CAAA;AAC9B,MAAM+V,SAAS7O,cAAa;AAE5BxT,8BAAgB,WAAA;AACd,QAAI6B,wBAAwB,CAAC6J,sBAAsB;AACjD;;AAGF,QAAI,CAACs0B,WAAW;AACdC,mBAAa,IAAI;;KAElB,CAACD,WAAWt0B,sBAAsB7J,oBAAoB,CAAC;AAE1D,MAAI,CAACwgB,QAAQ;AACX,WAAO;;AAGT,aACEriB,4BAAC0kB,YAAU,UACT1kB,4BAACkzB,WAAS,IAAA,OACVlzB,4BAACkgC,uBAAqB;IAACF;IAAwB;AAGrD;AAEA,SAASE,sBAAqB55B,MAAA;MAAG05B,YAAS15B,KAAT05B;AAC/B,MAAI,CAACA,WAAW;AACd,WAAO;;AAGT,aACEhgC,4BAAAA,uBAAAA,UACEA,4BAAC6/B,QAAM,IAAA,OACP7/B,4BAAC+2B,MAAI,IAAA,OACL/2B,4BAACs6B,SAAO,IAAA,CAAG;AAGjB;AAGA,IAAA,uBAAet6B,mBAAW8/B,aAAa3+B,aAAa;ACvErB,IAEVg/B,gBAAc,SAAAC,kBAAA;AAAAC,iBAAAF,gBAAAC,gBAAA;AAIjC,WAAAD,eAAYhR,OAAoC;;AAC9CmR,YAAAF,iBAAAG,KAAA,MAAMpR,KAAK,KAAC;AACZmR,UAAKxzB,QAAQ;MAAE0zB,UAAU;;AAAQ,WAAAF;;AAClCH,EAAAA,eAEMM,2BAAP,SAAAA,2BAAAA;AACE,WAAO;MAAED,UAAU;;;AACpB,MAAAE,SAAAP,eAAAQ;AAAAD,SAEDE,oBAAA,SAAAA,kBAAkBC,OAAcC,WAAc;AAE5CC,YAAQF,MAAM,wCAAwCA,OAAOC,SAAS;;AACvEJ,SAEDM,SAAA,SAAAA,SAAAA;AACE,QAAI,KAAKl0B,MAAM0zB,UAAU;AACvB,aAAO;;AAGT,WAAO,KAAKrR,MAAMnjB;;AACnB,SAAAm0B;AAAA,EAxBwCngC,sBAG1C;SCEeihC,cAAa36B,MAAA;MAC3B4B,UAAO5B,KAAP4B,SAAOg5B,YAAA56B,KACP4rB,MAAAA,OAAIgP,cAAA,SAAG,KAAEA,WAAAC,kBAAA76B,KACTnE,YAAAA,aAAUg/B,oBAAA,SAAGr+B,WAAW4C,QAAKy7B,iBAAAhQ,gBAAA7qB,KAC7B8qB,UAAAA,WAAQD,kBAAA,SAAG,QAAKA,eAChB/lB,cAAW9E,KAAX8E,aACA4Y,WAAQ1d,KAAR0d;AASA,MAAI,CAAC9b,WAAW,CAAC8b,YAAY,CAAC5Y,aAAa;AACzC,WAAO;;AAGT,aACEpL,4BAACiyB,eAAa;IACZ/pB;IACAgqB;IACA/vB;IACAivB;IACAhmB,aAAa4Y,WAAW,WAAA;AAAA,aAAMA;QAAW5Y;;AAG/C;SCXwB00B,cAAY3Q,OAAkB;AACpD,MAAMld,mBAAmBD,uBAAuB;IAC9CE,cAAcid,MAAMjd;IACpBE,iBAAiB+c,MAAM/c;IACvBC,kBAAkB8c,MAAM9c;GACzB;AAED,aACErS,4BAACmgC,eAAa,UACZngC,4BAAC2R,qBAAqBtF,UAAQ;IAACrL,OAAOiR;SACpCjS,4BAACohC,kBAAgB,OAAA,OAAA,CAAA,GAAKjS,KAAK,CAAA,CAAI,CACD;AAGtC;;", "names": ["selectors", "classes", "styles", "name", "ClassNames", "asSelectors", "classNames", "Array", "_len", "_key", "arguments", "map", "c", "join", "stylesheet", "createSheet", "hidden", "display", "opacity", "pointerEvents", "visibility", "overflow", "commonStyles", "create", "_extends", "PickerStyleTag", "React", "suppressHydrationWarning", "dangerouslySetInnerHTML", "__html", "getStyle", "commonInteractionStyles", "categoryBtn", "backgroundPositionY", "hiddenOnSearch", "visibleOnSearchOnly", "hiddenOnReactions", "transition", "height", "width", "darkMode", "key", "value", "_eprDarkTheme", "_eprAutoTheme", "compareConfig", "prev", "next", "prevCustomEmojis", "_prev$customEmojis", "customEmojis", "nextCustomEmojis", "_next$customEmojis", "open", "emojiVersion", "reactionsDefaultOpen", "searchPlaceHolder", "searchPlaceholder", "defaultSkinTone", "skinTonesDisabled", "autoFocusSearch", "emojiStyle", "theme", "suggestedEmojisMode", "lazyLoadEmojis", "className", "style", "searchDisabled", "skinTonePickerLocation", "length", "DEFAULT_REACTIONS", "SuggestionMode", "EmojiStyle", "Theme", "SkinTones", "Categories", "SkinTonePickerLocation", "categoriesOrdered", "SUGGESTED", "CUSTOM", "SMILEYS_PEOPLE", "ANIMALS_NATURE", "FOOD_DRINK", "TRAVEL_PLACES", "ACTIVITIES", "OBJECTS", "SYMBOLS", "FLAGS", "SuggestedRecent", "name", "category", "configByCategory", "_configByCategory", "baseCategoriesConfig", "modifiers", "categoryFromCategoryConfig", "categoryNameFromCategoryConfig", "mergeCategoriesConfig", "userCategoriesConfig", "extra", "suggestionMode", "RECENT", "base", "_userCategoriesConfig", "getBaseConfigByCategory", "modifier", "Object", "assign", "CDN_URL_APPLE", "CDN_URL_FACEBOOK", "CDN_URL_TWITTER", "CDN_URL_GOOGLE", "cdnUrl", "TWITTER", "GOOGLE", "FACEBOOK", "APPLE", "skinToneVariations", "NEUTRAL", "LIGHT", "MEDIUM_LIGHT", "MEDIUM", "MEDIUM_DARK", "DARK", "skinTonesNamed", "entries", "reduce", "acc", "_ref", "skinTonesMapped", "mapped", "skinTone", "_Object$assign", "EmojiProperties", "alphaNumericEmojiIndex", "setTimeout", "allEmojis", "searchIndex", "emoji", "indexEmoji", "joinedNameString", "emojiNames", "flat", "toLowerCase", "replace", "split", "for<PERSON>ach", "char", "_alphaNumericEmojiInd", "emojiUnified", "_emoji$EmojiPropertie", "addedIn", "parseFloat", "added_in", "emojiName", "unifiedWithoutSkinTone", "unified", "splat", "_splat$splice", "splice", "emojiHasVariations", "_emojiVariationUnifie", "emojiVariationUnified", "emojisByCategory", "_emojis$category", "emojis", "emojiUrlByUnified", "emojiVariations", "_emoji$EmojiPropertie2", "variations", "find", "variation", "includes", "emojiByUnified", "allEmojisByUnified", "withoutSkinTone", "values", "setCustomEmojis", "emojiData", "customToRegularEmoji", "push", "names", "id", "imgUrl", "<PERSON><PERSON><PERSON>", "activeVariationFromUnified", "_unified$split", "suspectedSkinTone", "KNOWN_FAILING_EMOJIS", "DEFAULT_SEARCH_PLACEHOLDER", "SEARCH_RESULTS_NO_RESULTS_FOUND", "SEARCH_RESULTS_SUFFIX", "SEARCH_RESULTS_ONE_RESULT_FOUND", "SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND", "mergeConfig", "userConfig", "basePickerConfig", "previewConfig", "_userConfig$previewCo", "config", "categories", "hidden<PERSON><PERSON><PERSON><PERSON>", "unicodeToHide", "add", "_config$customEmojis", "PREVIEW", "getEmojiUrl", "basePreviewConfig", "SEARCH", "FREQUENT", "Set", "reactions", "allowExpandReactions", "defaultEmoji", "defaultCaption", "showPreview", "ConfigContext", "PickerConfigProvider", "children", "_objectWithoutPropertiesLoose", "_excluded", "mergedConfig", "useSetConfig", "Provider", "_React$useState", "setMergedConfig", "usePickerConfig", "useDebouncedState", "initialValue", "delay", "_useState", "useState", "state", "setState", "timer", "useRef", "debouncedSetState", "Promise", "resolve", "current", "clearTimeout", "_window", "window", "useIsUnicodeHidden", "useUnicodeToHide", "has", "useDisallowedEmojis", "DisallowedEmojisRef", "emojiVersionConfig", "useEmojiVersionConfig", "useMemo", "Number", "isNaN", "disallowed<PERSON><PERSON><PERSON><PERSON>", "addedInNewerVersion", "useIsEmojiDisallowed", "isUnicodeHidden", "isEmojiDisallowed", "Boolean", "supportedLevel", "useMarkInitialLoad", "dispatch", "useEffect", "PickerContextProvider", "useDefaultSkinToneConfig", "useReactionsOpenConfig", "filterRef", "disallowClickRef", "disallowMouseRef", "disallowedEmojisRef", "suggestedUpdateState", "Date", "now", "searchTerm", "skinToneFanOpenState", "activeSkinTone", "activeCategoryState", "emojisThatFailedToLoadState", "emojiVariationPickerState", "reactionsModeState", "isPastInitialLoad", "setIsPastInitialLoad", "<PERSON>er<PERSON>ontext", "undefined", "useFilterRef", "_React$useContext", "useDisallowClickRef", "_React$useContext2", "useDisallowMouseRef", "_React$useContext3", "useReactionsModeState", "_React$useContext4", "useSearchTermState", "_React$useContext5", "useActiveSkinToneState", "_React$useContext6", "useEmojisThatFailedToLoadState", "_React$useContext7", "useIsPastInitialLoad", "_React$useContext8", "useEmojiVariationPickerState", "_React$useContext9", "useSkinToneFanOpenState", "_React$useContext10", "useUpdateSuggested", "_React$useContext12", "suggestedUpdated", "setsuggestedUpdate", "updateSuggested", "MutableConfigContext", "createContext", "useMutableConfig", "mutableConfig", "useContext", "useDefineMutableConfig", "MutableConfigRef", "onEmojiClick", "emptyFunc", "onReactionClick", "onSkinToneChange", "MOUSE_EVENT_SOURCE", "useSearchPlaceHolderConfig", "_usePickerConfig", "_find", "p", "_usePickerConfig2", "useAllowExpandReactions", "_usePickerConfig3", "useSkinTonesDisabledConfig", "_usePickerConfig4", "useEmojiStyleConfig", "_usePickerConfig5", "useAutoFocusSearchConfig", "_usePickerConfig6", "useCategoriesConfig", "_usePickerConfig7", "useCustomEmojisConfig", "_usePickerConfig8", "useOpenConfig", "_usePickerConfig9", "useOnEmojiClickConfig", "mouseEventSource", "_useMutableConfig", "_useReactionsModeStat", "setReactionsOpen", "handler", "REACTIONS", "args", "apply", "concat", "collapseToReactions", "o", "_len2", "_key2", "useOnSkinToneChangeConfig", "_useMutableConfig2", "usePreviewConfig", "_usePickerConfig10", "useThemeConfig", "_usePickerConfig11", "useSuggestedEmojisModeConfig", "_usePickerConfig12", "useLazyLoadEmojisConfig", "_usePickerConfig13", "useClassNameConfig", "_usePickerConfig14", "useStyleConfig", "_usePickerConfig15", "getDimension", "_usePickerConfig16", "_usePickerConfig17", "useSearchDisabledConfig", "_usePickerConfig18", "useSkinTonePickerLocationConfig", "_usePickerConfig19", "_usePickerConfig20", "useReactionsConfig", "_usePickerConfig21", "useGetEmojiUrlConfig", "_usePickerConfig22", "dimensionConfig", "useSearchResultsConfig", "searchResultsCount", "hasResults", "isPlural", "toString", "useIsSearchMode", "_useSearchTermState", "focusElement", "element", "requestAnimationFrame", "focus", "focusPrevElementSibling", "previousElementSibling", "focusNextElementSibling", "nextElement<PERSON><PERSON>ling", "focusFirstElementChild", "first", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "getActiveElement", "document", "activeElement", "ElementRefContextProvider", "Picker<PERSON>ain<PERSON><PERSON>", "AnchoredEmojiRef", "BodyRef", "SearchInputRef", "SkinTonePickerRef", "CategoryNavigationRef", "VariationPickerRef", "ReactionsRef", "ElementRefContext", "useElementRef", "usePickerMainRef", "useAnchoredEmojiRef", "useSetAnchoredEmojiRef", "target", "useBodyRef", "useReactionsRef", "useSearchInputRef", "useSkinTonePickerRef", "useCategoryNavigationRef", "useVariationPickerRef", "scrollTo", "root", "top", "$eprBody", "queryScrollBody", "scrollTop", "scrollBy", "by", "useScrollTo", "useCallback", "scrollEmojiAboveLabel", "isEmojiBehindLabel", "closest", "variationPicker", "scrollBody", "closestScrollBody", "emojiDistanceFromScrollTop", "categoryLabelHeight", "closestCategory", "focusFirstVisibleEmoji", "parent", "firstVisibleEmoji", "focusAndClickFirstVisibleEmoji", "first<PERSON><PERSON><PERSON>", "click", "focusLastVisibleEmoji", "lastVisibleEmoji", "focusNextVisibleEmoji", "nextVisibleEmoji", "nextCategory", "focusPrevVisibleEmoji", "prevVisibleEmoji", "prevCategory", "focusVisibleEmojiOneRowUp", "exitUp", "visibleEmojiOneRowUp", "focusVisibleEmojiOneRowDown", "visibleEmojiOneRowDown", "categoryContent", "closestCategoryContent", "indexInRow", "elementIndexInRow", "row", "rowNumber", "countInRow", "elementCountInRow", "prevVisibleCategory", "getElementInRow", "allVisibleEmojis", "getElementInPrevRow", "hasNextRow", "nextVisibleCategory", "itemInNextRow", "getElementInNextRow", "useCloseAllOpenToggles", "_useEmojiVariationPic", "setVariationPicker", "_useSkinToneFanOpenSt", "skinToneFanOpen", "setSkinToneFanOpen", "closeAllOpenToggles", "useHasOpenToggles", "_useEmojiVariationPic2", "_useSkinToneFanOpenSt2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useDisallowMouseMove", "DisallowMouseRef", "disallowMouseMove", "useAllowMouseMove", "allowMouseMove", "useIsMouseDisallowed", "isMouseDisallowed", "useOnMouseMove", "bodyRef", "addEventListener", "onMouseMove", "passive", "removeEventListener", "useFocusSearchInput", "useFocusSkinTonePicker", "useFocusCategoryNavigation", "useSetFilterRef", "setFilter", "setter", "useClearSearch", "applySearch", "useApplySearch", "focusSearchInput", "clearSearch", "useAppendSearch", "appendSearch", "str", "getNormalizedSearchTerm", "useFilter", "setFilterRef", "statusSearchResults", "getStatusSearchResults", "onChange", "inputValue", "filter", "nextValue", "longestMatch", "findLongestMatch", "filterEmojiObjectByKeyword", "_useSearchTermState2", "setSearchTerm", "then", "keyword", "filtered", "hasMatch", "some", "useIsEmojiFiltered", "_useFilterRef", "_useSearchTermState3", "isEmojiFilteredBySearchTerm", "_filter$searchTerm", "dict", "longestMatchingKey", "keys", "sort", "a", "b", "trim", "filterState", "_Object$entries", "useSetVariationPicker", "setAnchoredEmojiRef", "setEmojiVariationPicker", "_emojiFromElement", "emojiFromElement", "useIsSkinToneInSearch", "skinTonePickerLocationConfig", "useIsSkinToneInPreview", "KeyboardEvents", "useKeyboardNavigation", "usePickerMainKeyboardEvents", "useSearchInputKeyboardEvents", "useSkinTonePickerKeyboardEvents", "useCategoryNavigationKeyboardEvents", "useBodyKeyboardEvents", "onKeyDown", "event", "Escape", "preventDefault", "focusSkinTonePicker", "setSkinToneFanOpenState", "goDownFromSearchInput", "useGoDownFromSearchInput", "isSkinToneInSearch", "ArrowRight", "ArrowDown", "Enter", "isOpen", "setIsOpen", "isSkinToneInPreview", "onType", "useOnType", "ArrowLeft", "focusNextSkinTone", "focusPrevSkinTone", "ArrowUp", "goUpFromBody", "useGoUpFromBody", "buttonFromTarget", "Space", "focusCategoryNavigation", "isSearchMode", "goUpFromEmoji", "exitLeft", "currentSkinTone", "hasNextElementSibling", "hasModifier", "match", "metaKey", "ctrl<PERSON>ey", "altKey", "preloadEmoji", "NATIVE", "preloadedEmojs", "emojiUrl", "preloadImage", "url", "image", "Image", "src", "useOnFocus", "onFocus", "button", "DEFAULT_LABEL_HEIGHT", "<PERSON><PERSON><PERSON><PERSON>", "PickerRootElement", "_ref2", "reactionsMode", "searchModeActive", "_ref3", "styleProps", "cx", "styles", "main", "baseVariables", "darkTheme", "AUTO", "autoThemeDark", "_cx", "searchActive", "reactionsMenu", "ref", "DarkTheme", "emojiPicker", "position", "flexDirection", "borderWidth", "borderStyle", "borderRadius", "borderColor", "backgroundColor", "boxSizing", "fontFamily", "autoTheme", "<PERSON><PERSON>ilter", "parentWidth", "getBoundingClientRect", "elementWidth", "Math", "floor", "elementLeft", "left", "parentLeft", "elementHeight", "elementTop", "parentTop", "round", "parentHeight", "getRowElements", "elements", "elementsInRow", "lastRow", "firstElementIndex", "lastElementIndex", "slice", "getNextRowElements", "allElements", "currentRow", "nextRow", "rowElements", "index", "nextRowElements", "prevRowElements", "firstVisibleElementInContainer", "maxVisibilityDiffThreshold", "parentBottom", "bottom", "parentTopWithLabel", "getLabelHeight", "visibleElements", "elementBottom", "maxVisibilityDiffPixels", "clientHeight", "elementTopWithAllowedDiff", "elementBottomWithAllowedDiff", "parentNode", "labels", "from", "querySelectorAll", "label", "_i", "_labels", "EmojiButtonSelector", "VisibleEmojiSelector", "visible", "emojiElement", "_emojiElement$closest", "originalUnified", "originalUnifiedFromEmojiElement", "unifiedFromEmojiElement", "isEmojiElement", "matches", "_element$parentElemen", "parentElement", "_element$clientHeight", "emojiTrueOffsetTop", "labelHeight", "elementOffsetTop", "categoryWithoutLabel", "querySelector", "_category$clientHeigh", "_categoryWithoutLabel", "_closestScrollBody$sc", "_closestScrollBody", "_element$closest", "emojiTruOffsetLeft", "elementOffsetLeft", "_element$offsetTop", "offsetTop", "_element$offsetLeft", "offsetLeft", "_elementDataSetKey", "elementDataSetKey", "allUnifiedFromEmojiElement", "_elementDataSet$key", "elementDataSet", "_element$dataset", "dataset", "isVisibleEmoji", "classList", "contains", "isHidden", "_allEmojis$slice", "last", "parseNative<PERSON><PERSON><PERSON>", "hex", "String", "fromCodePoint", "parseInt", "SUGGESTED_LS_KEY", "getSuggested", "mode", "_window$localStorage$", "_window2", "localStorage", "recent", "JSON", "parse", "getItem", "count", "_unused", "setSuggested", "existing", "u", "nextList", "i", "original", "min", "_window3", "setItem", "stringify", "_unused2", "isCustomCategory", "isCustomEmoji", "useMouseDownHandlers", "ContainerRef", "mouseDownTimerRef", "_useActiveSkinToneSta", "_useUpdateSuggested", "activeEmojiStyle", "onClick", "_emojiFromEvent", "emojiFromEvent", "skinToneToUse", "emojiClickOutput", "onMouseDown", "_emojiFromEvent2", "onMouseUp", "confainerRef", "getImageUrl", "imageUrl", "isCustom", "<PERSON><PERSON>", "props", "type", "cursor", "border", "background", "outline", "ClickableEmojiButton", "_ref$showVariations", "showVariations", "hasVariations", "_ref$noBackground", "noBackground", "getAriaLabel", "_emojiNames$", "alignItems", "justifyContent", "max<PERSON><PERSON><PERSON>", "maxHeight", "content", "right", "borderLeft", "borderRight", "transform", "borderBottom", "zIndex", "emojiStyles", "external", "fontSize", "common", "alignSelf", "justifySelf", "EmojiImg", "_ref$lazyLoad", "lazyLoad", "onError", "alt", "emojiImag", "loading", "min<PERSON><PERSON><PERSON>", "minHeight", "padding", "NativeEmoji", "native<PERSON><PERSON>ji", "lineHeight", "textAlign", "letterSpacing", "View<PERSON>n<PERSON><PERSON><PERSON><PERSON>", "size", "_ref$getEmojiUrl", "_useEmojisThatFailedT", "setEmojisThatFailedToLoad", "emojiToRender", "ClickableEmoji", "BtnPlus", "setReactionsMode", "title", "tabIndex", "plusSign", "color", "backgroundImage", "Plus", "backgroundRepeat", "backgroundSize", "Reactions", "reactionsOpen", "list", "reaction", "emojiButton", "listStyle", "margin", "useOnScroll", "onScroll", "useIsEmojiHidden", "emojisThatFailedToLoad", "isEmojiFiltered", "failedToLoad", "filteredOut", "EmojiCategory", "categoryConfig", "categoryName", "gridGap", "gridTemplateColumns", "fontWeight", "textTransform", "isEverMounted", "useIsEverMounted", "isMounted", "setIsMounted", "Suggested", "suggestedEmojisModeConfig", "suggested", "_getSuggested", "suggestedItem", "EmojiList", "renderdCategoriesCountRef", "emojiList", "RenderCategory", "isEmojiHidden", "emojisToPush", "hiddenCounter", "_isEmojiHidden", "isDisallowed", "Direction", "EmojiVariationPicker", "_useVariationPickerTo", "useVariationPickerTop", "getTop", "getMenuDirection", "getPointerStyle", "usePointerStyle", "pointerStyle", "Down", "pointingUp", "pointer", "clientWidth", "direction", "Up", "emojiOffsetTop", "_bodyRef$scrollTop", "buttonHeight", "boxShadow", "transform<PERSON><PERSON>in", "backgroundPosition", "SVGTriangle", "Body", "PICKER", "body", "flex", "overflowY", "overflowX", "detectEmojyPartiallyBelowFold", "buttonRect", "bodyRect", "y", "useEmojiPreviewEvents", "allow", "setPreviewEmoji", "onEscape", "onMouseOver", "onEnter", "onLeave", "e", "_allUnifiedFromEmojiE", "relatedTarget", "belowFoldByPx", "handlePartiallyVisibleElementFocus", "_allUnifiedFromEmojiE2", "_document$activeEleme", "blur", "FlexDirection", "Flex", "_ref$style", "_ref$direction", "ROW", "_stylesheet$create", "COLUMN", "Absolute", "Relative", "BtnSkinToneVariation", "isActive", "skinToneVariation", "tone", "closedTone", "active", "ITEM_SIZE", "SkinTonePickerMenu", "SkinTonePicker", "SkinTonePickerDirection", "VERTICAL", "HORIZONTAL", "isDisabled", "setActiveSkinTone", "fullWidth", "expandedSize", "vertical", "skinTones", "verticalShadow", "flexBasis", "select", "Preview", "preview", "hideOnReactions", "PreviewBody", "previewEmoji", "variationPickerEmoji", "_previewEmoji$unified", "show", "PreviewContent", "defaultText", "borderTop", "categoryNameFromDom", "$category", "_$category$getAttribu", "getAttribute", "useActiveCategoryScrollDetection", "setActiveCategory", "visibleCategories", "Map", "observer", "IntersectionObserver", "_iterator", "_createForOfIteratorHelperLoose", "_step", "done", "entry", "set", "intersectionRatio", "ratios", "lastCategory", "_ratios", "_ratios$_i", "ratio", "threshold", "el", "observe", "useScrollCategoryIntoView", "scrollCategoryIntoView", "_BodyRef$current", "useShouldHideCustomEmojis", "customCategoryConfig", "CategoryButton", "isActiveCategory", "allowNavigation", "catBtn", "role", "DarkActivePositionY", "DarkPositionY", "DarkInactivePosition", "SVGNavigation", "backgroundPositionX", "CategoryNavigation", "activeCategory", "categoriesConfig", "hideCustomCategory", "nav", "BtnClearSearch", "btnClearSearch", "icnClearnSearch", "HoverDark", "SVGTimes", "SCOPE", "EMOJI_BUTTON", "CATEGORY", "CssSearch", "q", "gen<PERSON><PERSON><PERSON>", "IcnSearch", "icnSearch", "SVGMagnifier", "SearchContainer", "overlay", "Search", "inc", "setInc", "placeholder", "autoFocus", "_useFilter", "input", "searchContainer", "search", "_event$target$value", "_event$target", "visuallyHidden", "clip", "clipPath", "whiteSpace", "Header", "EmojiPicker", "ContentControl", "renderAll", "setRenderAll", "ExpandedPickerContent", "Error<PERSON>ou<PERSON><PERSON>", "_React$Component", "_inherits<PERSON><PERSON>e", "_this", "call", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "_proto", "prototype", "componentDidCatch", "error", "errorInfo", "console", "render", "ExportedEmoji", "_ref$size", "_ref$emojiStyle", "EmojiPickerReact"]}